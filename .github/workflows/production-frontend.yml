# GitHub Actions workflow for deploying CloudAudit Frontend to Production EC2
name: Deploy Frontend to Production

on:
  push:
    branches:
      - production

jobs:
  build-and-deploy:
    runs-on: self-hosted
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install dependencies
        run: npm ci
        working-directory: ./cloudaudit-frontend

      - name: Build frontend
        run: npm run build
        working-directory: ./cloudaudit-frontend
        env:
          VITE_API_URL: ${{ secrets.PROD_VITE_API_URL }}

      - name: Deploy to EC2 via SSH
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.PROD_EC2_HOST }}
          username: ${{ secrets.PROD_EC2_USER }}
          key: ${{ secrets.PROD_EC2_SSH_KEY }}
          port: ${{ secrets.PROD_EC2_PORT }}
          script: |
            cd /path/to/your/frontend
            git pull origin production
            npm ci
            npm run build
            pm2 restart frontend || pm2 start npm --name "frontend" -- run start
