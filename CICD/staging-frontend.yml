name: Staging-Deploy Frontend

on:
  push:
    branches:
      - staging

jobs:
  deploy:
    runs-on: self-hosted

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install dependencies
        run: npm ci

      - name: Build frontend
        env:
          REACT_APP_API_URL: ${{ secrets.STAGING_API_URL }}
          REACT_APP_ENV: "staging"
        run: npm run build

      - name: Deploy to EC2
        run: |
          ssh -o StrictHostKeyChecking=no ec2-user@${{ secrets.STAGING_SERVER_IP }} << 'EOF'
          cd /var/www/cloud-audit-frontend
          rm -rf *
          exit
          EOF
          scp -o StrictHostKeyChecking=no -r build/* ec2-user@${{ secrets.STAGING_SERVER_IP }}:/var/www/cloud-audit-frontend
          ssh -o StrictHostKeyChecking=no ec2-user@${{ secrets.STAGING_SERVER_IP }} "sudo systemctl restart nginx"
