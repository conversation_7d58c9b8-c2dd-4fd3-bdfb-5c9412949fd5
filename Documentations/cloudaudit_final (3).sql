-- MySQL dump 10.13  Distrib 8.0.42, for Linux (x86_64)
--
-- Host: 127.0.0.1    Database: cloudaudit_final
-- ------------------------------------------------------
-- Server version	8.0.42

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Current Database: `cloudaudit_final`
--

CREATE DATABASE /*!32312 IF NOT EXISTS*/ `cloudaudit_final` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;

USE `cloudaudit_final`;

--
-- Table structure for table `accounts`
--

DROP TABLE IF EXISTS `accounts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `accounts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `account_name` varchar(255) NOT NULL,
  `user_id` int NOT NULL,
  `cloud_provider_id` int NOT NULL,
  `workspace_id` int NOT NULL,
  `credential_data` json NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_accounts_id_user_id` (`id`,`user_id`),
  KEY `fk_accounts_user` (`user_id`),
  KEY `fk_accounts_cloud_provider` (`cloud_provider_id`),
  KEY `fk_accounts_workspace` (`workspace_id`),
  CONSTRAINT `fk_accounts_cloud_provider` FOREIGN KEY (`cloud_provider_id`) REFERENCES `cloud_providers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_accounts_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_accounts_workspace` FOREIGN KEY (`workspace_id`) REFERENCES `workspaces` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `accounts`
--

LOCK TABLES `accounts` WRITE;
/*!40000 ALTER TABLE `accounts` DISABLE KEYS */;
INSERT INTO `accounts` VALUES (33,'Admin Account',22,1,12,'{\"access_key\": \"********************\", \"secret_key\": \"/YHbzrjsd5EryKfY6KT6FVoyM3ih5XqBR8CtluCW\", \"aws_account_id\": \"************\"}','2025-05-21 13:09:56');
/*!40000 ALTER TABLE `accounts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cloud_providers`
--

DROP TABLE IF EXISTS `cloud_providers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cloud_providers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` enum('AWS','GCP','Azure') NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_enable` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cloud_providers`
--

LOCK TABLES `cloud_providers` WRITE;
/*!40000 ALTER TABLE `cloud_providers` DISABLE KEYS */;
INSERT INTO `cloud_providers` VALUES (1,'AWS','2025-05-11 09:00:26',1),(2,'GCP','2025-05-11 09:00:26',0),(3,'Azure','2025-05-11 09:00:26',0);
/*!40000 ALTER TABLE `cloud_providers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `custom_role_permissions`
--

DROP TABLE IF EXISTS `custom_role_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `custom_role_permissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `custom_role_id` int NOT NULL,
  `permission_id` int NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_custom_role_permissions_custom_role_id_permission_id` (`custom_role_id`,`permission_id`),
  KEY `fk_custom_role_permissions_permission` (`permission_id`),
  CONSTRAINT `fk_custom_role_permissions_custom_role` FOREIGN KEY (`custom_role_id`) REFERENCES `custom_roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_custom_role_permissions_permission` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=167 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `custom_role_permissions`
--

LOCK TABLES `custom_role_permissions` WRITE;
/*!40000 ALTER TABLE `custom_role_permissions` DISABLE KEYS */;
INSERT INTO `custom_role_permissions` VALUES (157,17,1),(158,17,2),(83,17,3),(84,17,4),(85,17,5),(86,17,6),(87,17,7),(107,17,10),(88,17,11),(89,17,12),(90,17,13),(91,18,1),(92,18,2),(93,18,10),(108,18,13),(126,19,1),(127,19,2),(137,19,3),(138,19,4),(139,19,5),(140,19,6),(141,19,7),(142,19,8),(143,19,9),(103,19,10);
/*!40000 ALTER TABLE `custom_role_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `custom_roles`
--

DROP TABLE IF EXISTS `custom_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `custom_roles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `workspace_id` int NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_custom_roles_workspace` (`workspace_id`),
  CONSTRAINT `fk_custom_roles_workspace` FOREIGN KEY (`workspace_id`) REFERENCES `workspaces` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `custom_roles`
--

LOCK TABLES `custom_roles` WRITE;
/*!40000 ALTER TABLE `custom_roles` DISABLE KEYS */;
INSERT INTO `custom_roles` VALUES (17,'User management',12,'2025-05-22 09:03:16'),(18,'Account Management',12,'2025-05-22 09:15:11'),(19,'custom',13,'2025-05-22 10:56:56');
/*!40000 ALTER TABLE `custom_roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `findings`
--

DROP TABLE IF EXISTS `findings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `findings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `scan_id` int NOT NULL,
  `service_id` int NOT NULL,
  `policy_check` varchar(100) NOT NULL,
  `severity` enum('low','medium','high','critical') NOT NULL,
  `description` text NOT NULL,
  `status` enum('pass','fail','remediated') NOT NULL,
  `details` json NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_findings_scan_id_service_id_policy_check` (`scan_id`,`service_id`,`policy_check`),
  KEY `fk_findings_service` (`service_id`),
  CONSTRAINT `fk_findings_scan` FOREIGN KEY (`scan_id`) REFERENCES `scans` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_findings_service` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3940 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `findings`
--

LOCK TABLES `findings` WRITE;
/*!40000 ALTER TABLE `findings` DISABLE KEYS */;
INSERT INTO `findings` VALUES (3933,59,5,'prohibit_public_access','critical','Lambda function policies should prohibit public access','remediated','[{\"region\": \"ap-south-1\", \"compliance\": true, \"function_name\": \"daily-billing-to-slack\", \"public_access\": false}, {\"region\": \"ap-south-1\", \"compliance\": true, \"function_name\": \"sendToSlack\", \"public_access\": false}, {\"region\": \"ap-south-1\", \"remediate\": {\"status\": \"pass\", \"message\": \"Successfully removed public access from Lambda function cloud-audit-testing\", \"attempted_at\": \"2025-06-13 06:43:25.602559\", \"attempted_by\": {\"id\": 22, \"email\": \"<EMAIL>\", \"is_admin\": true}}, \"compliance\": true, \"function_name\": \"cloud-audit-testing\", \"public_access\": false}]','2025-06-11 10:18:29'),(3934,59,5,'supported_runtimes','medium','Lambda functions should use supported runtimes','pass','[{\"region\": \"ap-south-1\", \"runtime\": \"python3.10\", \"compliance\": true, \"is_supported\": true, \"function_name\": \"daily-billing-to-slack\"}, {\"region\": \"ap-south-1\", \"runtime\": \"python3.13\", \"compliance\": true, \"is_supported\": true, \"function_name\": \"sendToSlack\"}, {\"region\": \"ap-south-1\", \"runtime\": \"python3.10\", \"compliance\": true, \"is_supported\": true, \"function_name\": \"cloud-audit-testing\"}]','2025-06-11 10:18:29'),(3935,59,5,'multi_az_vpc_operation','medium','VPC Lambda functions should operate in multiple Availability Zones','pass','[]','2025-06-11 10:18:29'),(3936,59,11,'private_cluster_endpoint','high','EKS cluster endpoints should not be publicly accessible','fail','[{\"region\": \"ap-south-1\", \"status\": \"fail\", \"compliance\": false, \"cluster_name\": \"demo-site\", \"private_endpoint\": false, \"endpoint_public_access\": true}]','2025-06-13 11:30:48'),(3937,59,11,'supported_kubernetes_version','high','EKS clusters should run on a supported Kubernetes version','pass','[{\"region\": \"ap-south-1\", \"status\": \"pass\", \"compliance\": true, \"cluster_name\": \"demo-site\", \"kubernetes_version\": \"1.30\", \"is_supported_version\": true}]','2025-06-13 11:30:48'),(3938,59,11,'encrypted_kubernetes_secrets','medium','EKS clusters should use encrypted Kubernetes secrets','fail','[{\"region\": \"ap-south-1\", \"status\": \"fail\", \"compliance\": false, \"cluster_name\": \"demo-site\", \"encryption_config\": [], \"has_secrets_encryption\": false}]','2025-06-13 11:30:48'),(3939,59,11,'audit_logging_enabled','medium','EKS clusters should have audit logging enabled','fail','[{\"region\": \"ap-south-1\", \"status\": \"fail\", \"compliance\": false, \"cluster_name\": \"demo-site\", \"audit_logging_enabled\": false}]','2025-06-13 11:30:48');
/*!40000 ALTER TABLE `findings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permissions`
--

DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions`
--

LOCK TABLES `permissions` WRITE;
/*!40000 ALTER TABLE `permissions` DISABLE KEYS */;
INSERT INTO `permissions` VALUES (1,'create_account'),(2,'delete_account'),(3,'create_user'),(4,'delete_user'),(5,'update_user_permissions'),(6,'assign_role_to_user'),(7,'revoke_role_from_user'),(8,'scan_service'),(9,'remediate_findings'),(10,'read_only'),(11,'create_custom_role'),(12,'update_custom_role'),(13,'delete_custom_role');
/*!40000 ALTER TABLE `permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `refresh_tokens`
--

DROP TABLE IF EXISTS `refresh_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `refresh_tokens` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `refresh_token` text NOT NULL,
  `expires_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=287 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `refresh_tokens`
--

LOCK TABLES `refresh_tokens` WRITE;
/*!40000 ALTER TABLE `refresh_tokens` DISABLE KEYS */;
INSERT INTO `refresh_tokens` VALUES (127,2,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************.jNI6zLiZgt5zrbWsHC6j-5NV9uHJMDwHd3AiH4SYL5c','2025-05-28 10:15:18'),(129,1,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************.WcAKGHuzuc1mg5rq1EXcWDoHu7-0qOmWWrAm4m3jFZI','2025-05-28 09:29:12'),(130,1,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************.fcbyCJeoSoGGltnThyNaiglbbbL44iMzOuTLKfmzp88','2025-05-28 09:45:23'),(131,5,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************.BG262Rd3ItqdV9dfSf8-FaJrXcR1H9au9JPzzkC_wcA','2025-05-28 09:48:14'),(132,5,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************.sqzIv4F1B5EJSlkw2MhCZ45PF99yik0EHhDLdtp5LH4','2025-05-28 09:58:43'),(133,16,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************._OB3IMhZIy1oJQFW6apCSbFh1GE5nzQkn5hrkY1TGYI','2025-05-28 10:02:26'),(134,16,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************.B3v7tMh0mP9cD8jprb-TtpI4wt5dd1p64MQDx0HlB9M','2025-05-28 10:05:46'),(135,17,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************.f3LAb9_CL-prz9b1CJ2ySrBuC6tbmD0V34x4gUF4ynA','2025-05-28 10:12:04'),(136,18,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbkBhZG1pbi5jb20iLCJ1c2VyX2lkIjoxOCwid29ya3NwYWNlX2lkIjoxMCwiZXhwIjoxNzQ4NDI3MjM2fQ.Zz8SQlufr-MuEy__yQDYKbQVpVyqS33StzKITr_y4MU','2025-05-28 10:13:57'),(138,18,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbkBhZG1pbi5jb20iLCJ1c2VyX2lkIjoxOCwid29ya3NwYWNlX2lkIjoxMCwiZXhwIjoxNzQ4NDI3NjA4fQ.jkZISiNgVADPjFqhSaYHzY3lSM6q10RSZwlN1pSD42c','2025-05-28 10:20:08'),(141,21,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************.qnu0NeR3bKX-lpSsmbUjp2spmvQoSQXxa-R5l9v5gN4','2025-05-28 10:30:57'),(156,27,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************.b8kLyw3UmAMU3W6BdgnjRrht009ExVULhRFdF6uW6S4','2025-05-29 11:08:52'),(158,27,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************.he1P0ZsWwXUTtKTG2_bMu3zN5TufQir6OUCX4Ml3ePA','2025-05-29 11:10:13'),(159,27,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************.VZQ41Y6E8PItwHSJc2NKbRGY6eIoLTYt6e1gm4VD_KE','2025-05-29 11:10:40'),(160,27,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************.SYBjmRkbILAFjIp4mkFImXifENOS4q7_RuGA0jcm8kg','2025-05-29 11:11:39'),(161,27,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************.4QCXTvY5QwS9AciG-oSMkMYTssvm1iZrpm0iooJ_ivg','2025-05-29 11:11:41'),(162,27,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJrcnVuYWxkdWJleTFAb3V0bG9vay5jb20iLCJ1c2VyX2lkIjoyNywid29ya3NwYWNlX2lkIjoxMywiZXhwIjoxNzQ4NTE3MTA2fQ.m1yobbVm0ST1z9_W1bVgz1Px9WEO7CWJOeetPoSxyvw','2025-05-29 11:11:47'),(163,27,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJrcnVuYWxkdWJleTFAb3V0bG9vay5jb20iLCJ1c2VyX2lkIjoyNywid29ya3NwYWNlX2lkIjoxMywiZXhwIjoxNzQ4NTE3MTMxfQ.InDP8LmkD3Jt-6n7vcr_9RB_jTcETjiXJB58oD9Tmmc','2025-05-29 11:12:12'),(164,27,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJrcnVuYWxkdWJleTFAb3V0bG9vay5jb20iLCJ1c2VyX2lkIjoyNywid29ya3NwYWNlX2lkIjoxMywiZXhwIjoxNzQ4NTE3MTM1fQ.YUq-2YnH84zsd84JVJnn6NUCWKNLvkq-gCPpM9RjDHI','2025-05-29 11:12:16'),(165,27,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJrcnVuYWxkdWJleTFAb3V0bG9vay5jb20iLCJ1c2VyX2lkIjoyNywid29ya3NwYWNlX2lkIjoxMywiZXhwIjoxNzQ4NTE3MTM3fQ.FGozUOm4-HyHphKX9CtmgBPylMG3vT_PUImrDpExr8o','2025-05-29 11:12:18'),(166,27,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************.M_mVO9-JJ5ZumzdAaSp6aCeCIwe67HqfYK4pZUgrgl0','2025-05-29 11:12:42'),(176,28,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.tRps2ma0LyswG0tLljj9sb112YUE6OgG8h8Mlptciz8','2025-05-29 12:36:40'),(177,28,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************._Tz7lpKPoL1V_HV70y2XBLboOZjeT6kv4q2TPenpic0','2025-05-29 12:36:45'),(220,30,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************.57CzOiXV-Dsz_2UwqmBYLcwT6nR2gu8KBgmimhmFbco','2025-05-30 10:14:55'),(222,23,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************.BpUHWX_X6d4u6Jq-QuQ4LQFnunZ8MZ8ZGVKl32T2k6M','2025-06-03 04:58:56'),(225,23,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************.cDd0u6LNSQcKdNNnM8-edItRv9PFo9bXtn6xLGnXDa4','2025-06-03 06:06:27'),(238,37,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************.hGEJt1gtAEDffRCxOFLIPOEDy72EikCAMHvnhiVgSZk','2025-06-03 09:09:07'),(245,23,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************.YUJUhp1VN0HV435sANxMXtPnlnpLe8315rsPrLb1pFg','2025-06-03 10:52:10'),(255,23,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************.5PMrbFQEPyRd29ydVZgSrdkqtIT02QMhJLyOZNbz8ao','2025-06-03 13:34:11'),(268,23,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************.qo4JeDtHa67PpgJ6h9H0yPpfxl_Bz7jx12SRjbXAta0','2025-06-04 08:52:36'),(282,40,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************.7OARWycSukI4sz6F5hhj4L8Z3GsCM1hgpgCthLZ6fbY','2025-06-05 09:32:24'),(284,23,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************.HFnnlVTQ6amaAdMBNnxHoRxKZDGXpePCjWBi0HhvdeY','2025-06-05 09:51:41'),(285,22,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbkBhZG1pbi5jb20iLCJ1c2VyX2lkIjoyMiwid29ya3NwYWNlX2lkIjoxMiwiZXhwIjoxNzUwODMwMDk5fQ.y2F0WqU0Rcq-FPAX3PJ7VbyGK2JAMuo3lVyKpZPB5cA','2025-06-25 05:41:39'),(286,22,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbkBhZG1pbi5jb20iLCJ1c2VyX2lkIjoyMiwid29ya3NwYWNlX2lkIjoxMiwiZXhwIjoxNzUwODMwMDk5fQ.y2F0WqU0Rcq-FPAX3PJ7VbyGK2JAMuo3lVyKpZPB5cA','2025-06-25 05:41:39');
/*!40000 ALTER TABLE `refresh_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role_permissions`
--

DROP TABLE IF EXISTS `role_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role_permissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `role_id` int NOT NULL,
  `permission_id` int NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_permissions_role_id_permission_id` (`role_id`,`permission_id`),
  KEY `fk_role_permissions_permission` (`permission_id`),
  CONSTRAINT `fk_role_permissions_permission` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_role_permissions_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role_permissions`
--

LOCK TABLES `role_permissions` WRITE;
/*!40000 ALTER TABLE `role_permissions` DISABLE KEYS */;
INSERT INTO `role_permissions` VALUES (1,1,1),(2,1,2),(3,1,3),(4,1,4),(5,1,5),(6,1,6),(7,1,7),(8,1,8),(9,1,9),(10,1,10),(11,1,11),(12,1,12),(13,1,13),(16,2,3),(17,2,4),(18,2,8),(19,2,9),(20,2,10),(23,3,3),(24,3,4),(25,3,8),(26,3,10),(30,4,3),(31,4,4),(32,4,5),(33,4,6),(34,4,7),(35,4,10),(37,5,10),(38,6,8),(39,6,10),(41,7,9),(42,7,10);
/*!40000 ALTER TABLE `role_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roles`
--

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;
INSERT INTO `roles` VALUES (1,'Admin'),(2,'Power User'),(3,'Moderator'),(4,'User Manager'),(5,'Auditor'),(6,'Scanner'),(7,'Remediator');
/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `scan_services`
--

DROP TABLE IF EXISTS `scan_services`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `scan_services` (
  `id` int NOT NULL AUTO_INCREMENT,
  `scan_id` int NOT NULL,
  `service_id` int NOT NULL,
  `status` enum('pending','running','completed') NOT NULL DEFAULT 'pending',
  `last_scanned_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_scan_services_scan` (`scan_id`),
  KEY `fk_scan_services_service` (`service_id`),
  CONSTRAINT `fk_scan_services_scan` FOREIGN KEY (`scan_id`) REFERENCES `scans` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_scan_services_service` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=372 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `scan_services`
--

LOCK TABLES `scan_services` WRITE;
/*!40000 ALTER TABLE `scan_services` DISABLE KEYS */;
INSERT INTO `scan_services` VALUES (370,59,5,'completed','2025-06-13 06:42:51'),(371,59,11,'completed','2025-06-16 10:29:48');
/*!40000 ALTER TABLE `scan_services` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `scans`
--

DROP TABLE IF EXISTS `scans`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `scans` (
  `id` int NOT NULL AUTO_INCREMENT,
  `account_id` int NOT NULL,
  `scan_start` timestamp NULL DEFAULT NULL,
  `scan_end` timestamp NULL DEFAULT NULL,
  `status` enum('running','completed') NOT NULL DEFAULT 'running',
  PRIMARY KEY (`id`),
  KEY `fk_scans_account` (`account_id`),
  CONSTRAINT `fk_scans_account` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=60 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `scans`
--

LOCK TABLES `scans` WRITE;
/*!40000 ALTER TABLE `scans` DISABLE KEYS */;
INSERT INTO `scans` VALUES (59,33,'2025-06-16 10:29:41','2025-06-16 10:29:48','completed');
/*!40000 ALTER TABLE `scans` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `services`
--

DROP TABLE IF EXISTS `services`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `services` (
  `id` int NOT NULL AUTO_INCREMENT,
  `cloud_provider_id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_enable` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `fk_services_cloud_provider` (`cloud_provider_id`),
  CONSTRAINT `fk_services_cloud_provider` FOREIGN KEY (`cloud_provider_id`) REFERENCES `cloud_providers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `services`
--

LOCK TABLES `services` WRITE;
/*!40000 ALTER TABLE `services` DISABLE KEYS */;
INSERT INTO `services` VALUES (1,1,'s3','2025-05-11 09:00:26',1),(2,1,'ec2','2025-05-11 09:00:26',1),(3,1,'rds','2025-05-11 09:00:26',1),(4,1,'iam','2025-05-11 09:00:26',1),(5,1,'lambda','2025-05-11 09:00:26',1),(6,1,'sqs','2025-05-11 09:00:26',0),(7,1,'sns','2025-05-11 09:00:26',0),(8,1,'dynamodb','2025-05-11 09:00:26',0),(9,1,'cloudwatch','2025-05-11 09:00:26',0),(10,1,'ecs','2025-05-11 09:00:26',1),(11,1,'eks','2025-05-11 09:00:26',1),(12,1,'elasticache','2025-05-11 09:00:26',1),(13,1,'route53','2025-05-11 09:00:26',0),(14,1,'kms','2025-05-11 09:00:26',0),(15,1,'ssm','2025-05-11 09:00:26',0),(16,1,'waf','2025-05-11 09:00:26',0),(17,1,'sts','2025-05-11 09:00:26',0),(18,1,'organizations','2025-05-11 09:00:26',0),(19,1,'elb','2025-05-11 09:00:26',1),(20,1,'efs','2025-05-11 09:00:26',1);
/*!40000 ALTER TABLE `services` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_accounts`
--

DROP TABLE IF EXISTS `user_accounts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_accounts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `account_id` int NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_accounts_id_user_id` (`user_id`,`account_id`),
  KEY `fk_user_accounts_account` (`account_id`),
  CONSTRAINT `fk_user_accounts_account` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_accounts_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=52 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_accounts`
--

LOCK TABLES `user_accounts` WRITE;
/*!40000 ALTER TABLE `user_accounts` DISABLE KEYS */;
INSERT INTO `user_accounts` VALUES (36,22,33,'2025-05-21 13:09:55'),(42,30,33,'2025-05-23 10:13:59'),(47,40,33,'2025-05-27 10:28:18');
/*!40000 ALTER TABLE `user_accounts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_custom_roles`
--

DROP TABLE IF EXISTS `user_custom_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_custom_roles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `custom_role_id` int NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_custom_roles_user_id_custom_role_id` (`user_id`,`custom_role_id`),
  KEY `fk_user_custom_roles_custom_role` (`custom_role_id`),
  CONSTRAINT `fk_user_custom_roles_custom_role` FOREIGN KEY (`custom_role_id`) REFERENCES `custom_roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_custom_roles_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_custom_roles`
--

LOCK TABLES `user_custom_roles` WRITE;
/*!40000 ALTER TABLE `user_custom_roles` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_custom_roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_roles`
--

DROP TABLE IF EXISTS `user_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_roles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `role_id` int NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_roles_user_id_role_id` (`user_id`,`role_id`),
  KEY `fk_user_roles_role` (`role_id`),
  CONSTRAINT `fk_user_roles_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_roles_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=95 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_roles`
--

LOCK TABLES `user_roles` WRITE;
/*!40000 ALTER TABLE `user_roles` DISABLE KEYS */;
INSERT INTO `user_roles` VALUES (22,22,1),(23,23,1),(63,25,4),(46,29,1),(93,30,3),(57,33,1),(59,35,1),(60,36,3),(62,38,1),(94,40,2),(70,42,1),(71,43,1);
/*!40000 ALTER TABLE `user_roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `workspace_id` int NOT NULL,
  `created_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `first_name` varchar(100) DEFAULT NULL,
  `last_name` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_users_workspace` (`workspace_id`),
  KEY `fk_users_created_by` (`created_by`),
  CONSTRAINT `fk_users_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_users_workspace` FOREIGN KEY (`workspace_id`) REFERENCES `workspaces` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=47 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (22,'<EMAIL>','7676aaafb027c825bd9abab78b234070e702752f625b752e55e55b48e607e358',12,22,'2025-05-21 10:32:58','Admin','User'),(23,'<EMAIL>','849f1575ccfbf3a4d6cf00e6c5641b7fd4da2ed3e212c2d79ba9161a5a432ff0',13,23,'2025-05-21 10:40:49',NULL,NULL),(25,'<EMAIL>','1e0b8af599d2ca1b2fac55872152682a6ed4122b2d32d90d6e9384532d9b389d',13,23,'2025-05-22 08:28:37',NULL,NULL),(29,'<EMAIL>','6481f8e1a060d56eeb7c10ac7809d316800dce013713c412e1d22076505b11a8',14,29,'2025-05-23 09:11:57',NULL,NULL),(30,'<EMAIL>','ca5cdd0a139364382eabaa8e107ddecabbe4d4ec0c7f01f26d80aeedde6ec987',12,22,'2025-05-23 10:13:59','Deep','Prajapati'),(33,'<EMAIL>','7676aaafb027c825bd9abab78b234070e702752f625b752e55e55b48e607e358',15,33,'2025-05-27 06:06:08','abcd','xyz'),(35,'<EMAIL>','7676aaafb027c825bd9abab78b234070e702752f625b752e55e55b48e607e358',17,35,'2025-05-27 06:08:37','Deep','Prajapati'),(36,'<EMAIL>','7676aaafb027c825bd9abab78b234070e702752f625b752e55e55b48e607e358',17,35,'2025-05-27 06:09:50',NULL,NULL),(38,'<EMAIL>','849f1575ccfbf3a4d6cf00e6c5641b7fd4da2ed3e212c2d79ba9161a5a432ff0',18,38,'2025-05-27 06:16:46',NULL,NULL),(40,'<EMAIL>','88d577bfa453f305f7974aa0b89c034b57fb45c5ecda44684cf2c5d5e71a68c5',12,22,'2025-05-27 10:28:18','Krunal','Dubey'),(42,'<EMAIL>','423e20c8ccf69e056502185b23f87e4eb3034db3ef7232bf531e6d90cebc5164',19,42,'2025-05-27 11:00:31','k','d'),(43,'<EMAIL>','849f1575ccfbf3a4d6cf00e6c5641b7fd4da2ed3e212c2d79ba9161a5a432ff0',20,43,'2025-05-27 11:01:02','abcd','xyz');
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `workspaces`
--

DROP TABLE IF EXISTS `workspaces`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workspaces` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_workspaces_created_by` (`created_by`),
  CONSTRAINT `fk_workspaces_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `workspaces`
--

LOCK TABLES `workspaces` WRITE;
/*!40000 ALTER TABLE `workspaces` DISABLE KEYS */;
INSERT INTO `workspaces` VALUES (12,'Admin Workspace','2025-05-21 10:32:58',22),(13,'Maruti Tech','2025-05-21 10:40:49',23),(14,'Maruti Tech','2025-05-23 09:11:57',29),(15,'Maruti','2025-05-27 06:06:08',33),(17,'Maruti','2025-05-27 06:08:37',35),(18,'Maruti Tech','2025-05-27 06:16:46',38),(19,'Kd@12345','2025-05-27 11:00:31',42),(20,'Maruti Tech','2025-05-27 11:01:01',43);
/*!40000 ALTER TABLE `workspaces` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping events for database 'cloudaudit_final'
--

--
-- Dumping routines for database 'cloudaudit_final'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-03 11:18:16
