DROP DATABASE IF EXISTS cloudaudit_final;
CREATE DATABASE IF NOT EXISTS cloudaudit_final;
USE cloudaudit_final;
-- Table: users
CREATE TABLE users (
    id INT NOT NULL AUTO_INCREMENT,
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    email VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    workspace_id INT NOT NULL,
    created_by INT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

-- Table: workspaces
CREATE TABLE workspaces (
    id INT NOT NULL AUTO_INCREMENT,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by INT,
    PRIMARY KEY (id)
);

-- Table: accounts
CREATE TABLE accounts (
    id INT NOT NULL AUTO_INCREMENT,
    account_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    user_id INT NOT NULL,
    cloud_provider_id INT NOT NULL,
    workspace_id INT NOT NULL,
    credential_data JSON NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

-- Table: user_accounts
CREATE TABLE user_accounts (
    id INT NOT NULL AUTO_INCREMENT,
    user_id INT NOT NULL,
    account_id INT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

-- Table: cloud_providers
CREATE TABLE cloud_providers (
    id INT NOT NULL AUTO_INCREMENT,
    name ENUM('AWS', 'GCP', 'Azure') NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_enable BOOL DEFAULT TRUE,
    PRIMARY KEY (id)
);

-- Table: permissions
CREATE TABLE permissions (
    id INT NOT NULL AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    PRIMARY KEY (id)
);

-- Table: roles
CREATE TABLE roles (
    id INT NOT NULL AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    PRIMARY KEY (id)
);

-- Table: user_roles
CREATE TABLE user_roles (
    id INT NOT NULL AUTO_INCREMENT,
    user_id INT NOT NULL,
    role_id INT NOT NULL,
    PRIMARY KEY (id)
);

-- Table: role_permissions
CREATE TABLE role_permissions (
    id INT NOT NULL AUTO_INCREMENT,
    role_id INT NOT NULL,
    permission_id INT NOT NULL,
    PRIMARY KEY (id)
);

-- Table: custom_roles
CREATE TABLE custom_roles (
    id INT NOT NULL AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    workspace_id INT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

-- Table: custom_role_permissions
CREATE TABLE custom_role_permissions (
    id INT NOT NULL AUTO_INCREMENT,
    custom_role_id INT NOT NULL,
    permission_id INT NOT NULL,
    PRIMARY KEY (id)
);

-- Table: user_custom_roles
CREATE TABLE user_custom_roles (
    id INT NOT NULL AUTO_INCREMENT,
    user_id INT NOT NULL,
    custom_role_id INT NOT NULL,
    PRIMARY KEY (id)
);

-- Table: scans
CREATE TABLE scans (
    id INT NOT NULL AUTO_INCREMENT,
    account_id INT NOT NULL,
    scan_start TIMESTAMP,
    scan_end TIMESTAMP,
    status ENUM('running', 'completed') NOT NULL DEFAULT 'running',
    PRIMARY KEY (id)
);

-- Table: scan_services
CREATE TABLE scan_services (
    id INT NOT NULL AUTO_INCREMENT,
    scan_id INT NOT NULL,
    service_id INT NOT NULL,
    status ENUM('pending', 'running', 'completed') NOT NULL DEFAULT 'pending',
    last_scanned_at TIMESTAMP,
    PRIMARY KEY (id)
);

-- Table: services
CREATE TABLE services (
    id INT NOT NULL AUTO_INCREMENT,
    cloud_provider_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_enable BOOLEAN DEFAULT FALSE,
    PRIMARY KEY (id)
);

-- Table: findings
-- CREATE TABLE findings (
--    id INT NOT NULL AUTO_INCREMENT,
--    scan_id INT NOT NULL,
--    scan_service_id INT NOT NULL,
--    finding JSON NOT NULL,
--    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
--    PRIMARY KEY (id)
-- );


-- Table: findings
CREATE TABLE findings (
    id INT NOT NULL AUTO_INCREMENT,
    scan_id INT NOT NULL,
    service_id INT NOT NULL,
    policy_check VARCHAR(100) NOT NULL,
    severity ENUM('low', 'medium', 'high', 'critical') NOT NULL,
    description TEXT NOT NULL,
    status ENUM('pass', 'fail', 'remediated') NOT NULL,
    details JSON NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);


-- Table: refresh_tokens
CREATE TABLE refresh_tokens (
    id INT NOT NULL AUTO_INCREMENT,
    user_id INT NOT NULL,
    refresh_token TEXT NOT NULL,
    expires_at DATETIME NOT NULL,
    PRIMARY KEY (id)
);


-- Foreign Key Constraints
ALTER TABLE users ADD CONSTRAINT fk_users_workspace FOREIGN KEY (workspace_id) REFERENCES workspaces (id) ON DELETE CASCADE;
ALTER TABLE users ADD CONSTRAINT fk_users_created_by FOREIGN KEY (created_by) REFERENCES users (id) ON DELETE CASCADE;
ALTER TABLE workspaces ADD CONSTRAINT fk_workspaces_created_by FOREIGN KEY (created_by) REFERENCES users (id) ON DELETE CASCADE;
ALTER TABLE accounts ADD CONSTRAINT fk_accounts_user FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE;
ALTER TABLE accounts ADD CONSTRAINT fk_accounts_cloud_provider FOREIGN KEY (cloud_provider_id) REFERENCES cloud_providers (id) ON DELETE CASCADE;
ALTER TABLE accounts ADD CONSTRAINT fk_accounts_workspace FOREIGN KEY (workspace_id) REFERENCES workspaces (id) ON DELETE CASCADE;
ALTER TABLE user_accounts ADD CONSTRAINT fk_user_accounts_account FOREIGN KEY (account_id) REFERENCES accounts (id) ON DELETE CASCADE;
ALTER TABLE user_accounts ADD CONSTRAINT fk_user_accounts_user FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE;
ALTER TABLE services ADD CONSTRAINT fk_services_cloud_provider FOREIGN KEY (cloud_provider_id) REFERENCES cloud_providers (id) ON DELETE CASCADE;
ALTER TABLE user_roles ADD CONSTRAINT fk_user_roles_user FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE;
ALTER TABLE user_roles ADD CONSTRAINT fk_user_roles_role FOREIGN KEY (role_id) REFERENCES roles (id) ON DELETE CASCADE;
ALTER TABLE role_permissions ADD CONSTRAINT fk_role_permissions_role FOREIGN KEY (role_id) REFERENCES roles (id) ON DELETE CASCADE;
ALTER TABLE role_permissions ADD CONSTRAINT fk_role_permissions_permission FOREIGN KEY (permission_id) REFERENCES permissions (id) ON DELETE CASCADE;
ALTER TABLE custom_roles ADD CONSTRAINT fk_custom_roles_workspace FOREIGN KEY (workspace_id) REFERENCES workspaces (id) ON DELETE CASCADE;
ALTER TABLE custom_role_permissions ADD CONSTRAINT fk_custom_role_permissions_custom_role FOREIGN KEY (custom_role_id) REFERENCES custom_roles (id) ON DELETE CASCADE;
ALTER TABLE custom_role_permissions ADD CONSTRAINT fk_custom_role_permissions_permission FOREIGN KEY (permission_id) REFERENCES permissions (id) ON DELETE CASCADE;
ALTER TABLE user_custom_roles ADD CONSTRAINT fk_user_custom_roles_user FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE;
ALTER TABLE user_custom_roles ADD CONSTRAINT fk_user_custom_roles_custom_role FOREIGN KEY (custom_role_id) REFERENCES custom_roles (id) ON DELETE CASCADE;
ALTER TABLE scans ADD CONSTRAINT fk_scans_account FOREIGN KEY (account_id) REFERENCES accounts (id) ON DELETE CASCADE;
ALTER TABLE scan_services ADD CONSTRAINT fk_scan_services_scan FOREIGN KEY (scan_id) REFERENCES scans (id) ON DELETE CASCADE;
ALTER TABLE scan_services ADD CONSTRAINT fk_scan_services_service FOREIGN KEY (service_id) REFERENCES services (id) ON DELETE CASCADE;
ALTER TABLE findings ADD CONSTRAINT fk_findings_scan FOREIGN KEY (scan_id) REFERENCES scans (id) ON DELETE CASCADE;
ALTER TABLE findings ADD CONSTRAINT fk_findings_service FOREIGN KEY (service_id) REFERENCES services (id) ON DELETE CASCADE;
-- ALTER TABLE findings ADD CONSTRAINT fk_findings_scan_service FOREIGN KEY (scan_service_id) REFERENCES scan_services (id) ON DELETE CASCADE;

-- Unique Constraints
ALTER TABLE accounts ADD CONSTRAINT uk_accounts_id_user_id UNIQUE (id, user_id);
ALTER TABLE user_accounts ADD CONSTRAINT uk_accounts_id_user_id UNIQUE (user_id, account_id);
ALTER TABLE user_roles ADD CONSTRAINT uk_user_roles_user_id_role_id UNIQUE (user_id, role_id);
ALTER TABLE role_permissions ADD CONSTRAINT uk_role_permissions_role_id_permission_id UNIQUE (role_id, permission_id);
-- ALTER TABLE custom_roles ADD CONSTRAINT uk_custom_roles_user_id_workspace_id UNIQUE (user_id, workspace_id);
ALTER TABLE custom_role_permissions ADD CONSTRAINT uk_custom_role_permissions_custom_role_id_permission_id UNIQUE (custom_role_id, permission_id);
ALTER TABLE user_custom_roles ADD CONSTRAINT uk_user_custom_roles_user_id_custom_role_id UNIQUE (user_id, custom_role_id);
-- ALTER TABLE findings ADD CONSTRAINT uk_findings_scan_id_scan_service_id UNIQUE (scan_id, scan_service_id);
ALTER TABLE findings ADD CONSTRAINT uk_findings_scan_id_service_id_policy_check UNIQUE (scan_id, service_id, policy_check);


-- Add Master Tables Data
INSERT INTO cloud_providers (name) VALUES ('AWS'), ('GCP'), ('Azure');
INSERT INTO services (cloud_provider_id, name) VALUES (1, 's3'), (1, 'ec2'), (1, 'rds'), (1, 'iam'), (1, 'lambda'), (1, 'sqs'), (1, 'sns'), (1, 'dynamodb'), (1, 'cloudwatch'), (1, 'ecs'), (1, 'eks'), (1, 'elasticache'), (1, 'route53'), (1, 'kms'), (1, 'ssm'), (1, 'waf'), (1, 'sts'), (1, 'organizations'), (1, 'elb'), (1, 'efs');
INSERT INTO roles(name) VALUES ('Admin'), ('Power User'), ('Moderator'), ('User Manager'), ('Auditor'), ('Scanner'), ('Remediator');
INSERT INTO permissions(name) VALUES ('create_account'), ('delete_account'), ('create_user'), ('delete_user'), ('update_user_permissions'), ('assign_role_to_user'), ('revoke_role_from_user'), ('scan_service'), ('remediate_findings'), ('read_only'), ('create_custom_role'), ('update_custom_role'), ('delete_custom_role');

-- Mapping Admin role (has all permissions)
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r
JOIN permissions p ON p.name IN ('create_account', 'delete_account', 'create_user', 'delete_user', 'update_user_permissions', 'scan_service', 'remediate_findings', 'read_only', 'assign_role_to_user', 'revoke_role_from_user', 'create_custom_role', 'update_custom_role', 'delete_custom_role')
WHERE r.name = 'Admin';

-- Mapping Power User role
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r
JOIN permissions p ON p.name IN ('create_user', 'delete_user', 'scan_service', 'remediate_findings', 'read_only')
WHERE r.name = 'Power User';

-- Mapping Moderator role
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r
JOIN permissions p ON p.name IN ('create_user', 'delete_user', 'scan_service', 'read_only')
WHERE r.name = 'Moderator';

-- Mapping User Manager role
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r
JOIN permissions p ON p.name IN ('create_user', 'delete_user', 'update_user_permissions', 'assign_role_to_user', 'revoke_role_from_user', 'read_only')
WHERE r.name = 'User Manager';

-- Mapping Auditor role (only read_only permission)
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r
JOIN permissions p ON p.name = 'read_only'
WHERE r.name = 'Auditor';

-- Mapping Scanner role
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r
JOIN permissions p ON p.name IN ('scan_service', 'read_only')
WHERE r.name = 'Scanner';

-- Mapping Remediator role
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r
JOIN permissions p ON p.name IN ('remediate_findings', 'read_only')
WHERE r.name = 'Remediator';
