{"info": {"_postman_id": "5f06b213-9952-4e8f-84a4-4082336da993", "name": "CloudAudit - Backend", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26779190", "_collection_link": "https://cloudaudit.postman.co/workspace/CloudAudit-Workspace~d320d495-8cc3-4f32-8e76-343da3265592/collection/26779190-5f06b213-9952-4e8f-84a4-4082336da993?action=share&source=collection_link&creator=26779190"}, "item": [{"name": "SignUp", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"first_name\": \"<PERSON>\",\r\n    \"last_name\": \"<PERSON><PERSON><PERSON><PERSON>\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"admin@123\",\r\n    \"workspace_name\": \"<PERSON><PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Host}}{{Port}}/api/signup", "host": ["{{Host}}{{Port}}"], "path": ["api", "signup"]}}, "response": []}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    let response = pm.response.json();", "", "    if (response.access_token) {", "        pm.environment.set(\"BearerToken\", response.access_token);", "        pm.environment.set(\"RefreshToken\", response.refresh_token);", "        console.log(\"Access token saved to environment variable.\");", "    } else {", "        console.error(\"No access_token found in response.\");", "    }", "} else {", "    console.error(`<PERSON><PERSON> failed with status code ${pm.response.code}`);", "}", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"admin@123\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Host}}{{Port}}/api/login", "host": ["{{Host}}{{Port}}"], "path": ["api", "login"]}}, "response": []}, {"name": "Refresh Access Token", "event": [{"listen": "prerequest", "script": {"exec": ["if (pm.response.code === 200) {", "    let response = pm.response.json();", "", "    if (response.access_token) {", "        pm.environment.set(\"BearerToken\", response.access_token);", "        pm.environment.set(\"RefreshToken\", response.refresh_token);", "        console.log(\"Access token saved to environment variable.\");", "    } else {", "        console.error(\"No access_token found in response.\");", "    }", "} else {", "    console.error(`<PERSON><PERSON> failed with status code ${pm.response.code}`);", "}", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"refresh_token\": \"{{RefreshToken}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Host}}{{Port}}/api/refresh-token", "host": ["{{Host}}{{Port}}"], "path": ["api", "refresh-token"]}}, "response": []}, {"name": "Logout", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"refresh_token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************.azh-r56xLfd2FRULi2N0vBjs00XlMpbQia_oRDaF2Oc\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/logout", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "logout"]}}, "response": []}, {"name": "Get Cloud Providers", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{<PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}], "url": {"raw": "{{Host}}{{Port}}/api/cloud-providers", "host": ["{{Host}}{{Port}}"], "path": ["api", "cloud-providers"]}}, "response": []}, {"name": "Get Services", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{<PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}], "url": {"raw": "{{Host}}{{Port}}/api/services?cloud_provider_id=1", "host": ["{{Host}}{{Port}}"], "path": ["api", "services"], "query": [{"key": "cloud_provider_id", "value": "1"}]}}, "response": []}, {"name": "Add New Account", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{<PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"cloud_provider_id\": 1,\r\n    \"account_name\": \"Maruti AWS Reader\",\r\n    \"is_organization\": false,\r\n    \"credentials\": {\r\n        \"access_key\": \"********************\",\r\n        \"secret_key\": \"/YHbzrjsd5EryKfY6KT6FVoyM3ih5XqBR8CtluCW\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Host}}{{Port}}/api/accounts", "host": ["{{Host}}{{Port}}"], "path": ["api", "accounts"]}}, "response": []}, {"name": "Create <PERSON><PERSON>", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{<PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": 1,\r\n    \"account_id\": 1,\r\n    \"cloud_provider_id\": 1,\r\n    \"cloud_provider_name\": \"aws\",\r\n    \"regions\": [\"ap-south-1\"],\r\n    \"services\": [2]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Host}}{{Port}}/api/scans", "host": ["{{Host}}{{Port}}"], "path": ["api", "scans"]}}, "response": []}, {"name": "<PERSON>", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{<PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}], "url": {"raw": "{{Host}}{{Port}}/api/scans?page=1&page_size=20", "host": ["{{Host}}{{Port}}"], "path": ["api", "scans"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "20"}]}}, "response": []}, {"name": "<PERSON>an Findings", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{<PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}], "url": {"raw": "{{Host}}{{Port}}/api/findings?scan_id=37&service_id=1", "host": ["{{Host}}{{Port}}"], "path": ["api", "findings"], "query": [{"key": "scan_id", "value": "37"}, {"key": "service_id", "value": "1"}, {"key": "status", "value": "pass", "disabled": true}, {"key": "severity", "value": "critical", "disabled": true}]}}, "response": []}, {"name": "Get Scan Finding Details", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{<PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}], "url": {"raw": "{{Host}}{{Port}}/api/findings/3175", "host": ["{{Host}}{{Port}}"], "path": ["api", "findings", "3175"]}}, "response": []}, {"name": "Finding Detail Remediation", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{<PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"details\": {\n        \"region\": \"us-east-1\",\n        \"compliance\": false,\n        \"bucket_name\": \"cloud-audit-test-bucket\",\n        \"block_public_access_enabled\": false\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Host}}{{Port}}/api/findings/3175/remediate", "host": ["{{Host}}{{Port}}"], "path": ["api", "findings", "3175", "remediate"]}}, "response": []}, {"name": "Regions", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{<PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}], "url": {"raw": "{{Host}}{{Port}}/api/regions?cloud_provider_id=1", "host": ["{{Host}}{{Port}}"], "path": ["api", "regions"], "query": [{"key": "cloud_provider_id", "value": "1"}]}}, "response": []}, {"name": "<PERSON> <PERSON>", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{<PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}], "url": {"raw": "{{Host}}{{Port}}/api/scans/41", "host": ["{{Host}}{{Port}}"], "path": ["api", "scans", "41"]}}, "response": []}, {"name": "Get Accounts", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{<PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}], "url": {"raw": "{{Host}}{{Port}}/api/accounts?cloud_provider_id=1", "host": ["{{Host}}{{Port}}"], "path": ["api", "accounts"], "query": [{"key": "cloud_provider_id", "value": "1"}]}}, "response": []}, {"name": "Get Account Details", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{<PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}], "url": {"raw": "{{Host}}{{Port}}/api/accounts/26", "host": ["{{Host}}{{Port}}"], "path": ["api", "accounts", "26"]}}, "response": []}, {"name": "Delete Account", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************.j0XOgPbDlyU0kuBRmriCVqx6c2A9qREb-gR54eIalRU", "type": "text"}], "url": {"raw": "http://127.0.0.1:8000/api/accounts/1", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "accounts", "1"]}}, "response": []}, {"name": "Create User by Ad<PERSON>", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************.iLZj-NqLdlwBFrloiuF8T8WfjNJqRItjSt9WNbcxhHU", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"********\",\r\n    \"role_id\": 2,\r\n    \"is_custom_role\": false\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/admin/users", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "admin", "users"]}}, "response": []}, {"name": "Create Custom Role", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************.prsbrFfy4I-fdgwXssXG3ig6GP4yFoNPeyR_0bwAo_o", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"MyRole-1\",\r\n    \"permissions\": [\r\n        {\r\n            \"id\": 3,\r\n            \"name\": \"create_user\"\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/custom-roles", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "custom-roles"]}}, "response": []}, {"name": "Assign Role to user", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************.iLZj-NqLdlwBFrloiuF8T8WfjNJqRItjSt9WNbcxhHU", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": 6,\r\n    \"role_id\": 4,\r\n    \"is_custom_role\": false\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/assign-role", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "assign-role"]}}, "response": []}, {"name": "Revoke Role from user", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************.HwCXF9x-SygyunV6yIJ-ZyxQO8iBVEn9Jw9AVSz7xXo", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": 6,\r\n    \"role_id\": 4,\r\n    \"is_custom_role\": false\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/revoke-role", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "revoke-role"]}}, "response": []}, {"name": "Update Custom Role", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "PUT", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************.prsbrFfy4I-fdgwXssXG3ig6GP4yFoNPeyR_0bwAo_o", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"id\": 2,\r\n    \"name\": \"MyRole-1x\",\r\n    \"permissions\": [\r\n        {\r\n            \"id\": 3,\r\n            \"name\": \"create_user\"\r\n        },\r\n        {\r\n            \"id\": 1,\r\n            \"name\": \"create_account\"\r\n        },\r\n        {\r\n            \"id\": 2,\r\n            \"name\": \"delete_account\"\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/custom-roles", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "custom-roles"]}}, "response": []}, {"name": "Delete Custom Role", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************.prsbrFfy4I-fdgwXssXG3ig6GP4yFoNPeyR_0bwAo_o", "type": "text"}], "url": {"raw": "http://127.0.0.1:8000/api/custom-roles?custom_role_id=1", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "custom-roles"], "query": [{"key": "custom_role_id", "value": "1"}]}}, "response": []}, {"name": "Get Roles", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************.DbRZagJuO5kDpSO4O_YPvQL7vGUdNW6Ij_RmPcZOwmg", "type": "text"}], "url": {"raw": "http://127.0.0.1:8000/api/roles?user_id=1", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "roles"], "query": [{"key": "user_id", "value": "1"}]}}, "response": []}, {"name": "Delete User by Admin", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{<PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}], "url": {"raw": "{{Host}}{{Port}}/api/admin/users?user_to_delete=10", "host": ["{{Host}}{{Port}}"], "path": ["api", "admin", "users"], "query": [{"key": "user_to_delete", "value": "10"}]}}, "response": []}, {"name": "Get Permissions", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************.v7yoSf5MKBKxXuZnONqYgDNqNAZgR7sPLkjjFLowKy8", "type": "text"}], "url": {"raw": "http://127.0.0.1:8000/api/user-permissions", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "user-permissions"]}}, "response": []}, {"name": "User Info", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{<PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}], "url": {"raw": "{{Host}}{{Port}}/api/user-info", "host": ["{{Host}}{{Port}}"], "path": ["api", "user-info"]}}, "response": []}, {"name": "Update User Info", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{<PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"first_name\": \"Admin\",\n    \"last_name\": \"User\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Host}}{{Port}}/api/update-user-info", "host": ["{{Host}}{{Port}}"], "path": ["api", "update-user-info"]}}, "response": []}, {"name": "Update user Password", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{<PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"current_password\": \"<EMAIL>\",\n    \"new_password\": \"<EMAIL>\",\n    \"confirm_password\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Host}}{{Port}}/api/update-password", "host": ["{{Host}}{{Port}}"], "path": ["api", "update-password"]}}, "response": []}, {"name": "User List", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{<PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}], "url": {"raw": "{{Host}}{{Port}}/api/team-members", "host": ["{{Host}}{{Port}}"], "path": ["api", "team-members"]}}, "response": []}, {"name": "Update Team Member details", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{<PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"user_id\": 26,\n    \"role_id\": 1,\n    \"is_custom_role\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Host}}{{Port}}/api/team-member", "host": ["{{Host}}{{Port}}"], "path": ["api", "team-member"]}}, "response": []}, {"name": "Update Team-Member Password", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{<PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"user_id\": 37,\n    \"password\": \"tester@123\",\n    \"confirm_password\": \"tester@123\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Host}}{{Port}}/api/change-password", "host": ["{{Host}}{{Port}}"], "path": ["api", "change-password"]}}, "response": []}]}