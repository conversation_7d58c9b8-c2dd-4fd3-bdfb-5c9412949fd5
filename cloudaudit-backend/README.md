# CloudAudit Backend Service

## Overview
This service scans cloud services of various cloud providers such as AWS, GCP, or Azure and provides remediation based on compliance requirements.

## Requirements
- Python 3.11
- pip (Python package installer)

## Setup
1. Clone the repository:
    ```sh
    <NAME_EMAIL>:Maruti-Techlabs/cloudaudit-backend.git
    cd cloudaudit-backend
    ```

2. Create a virtual environment:
    ```sh
    python -m venv venv
    source venv/bin/activate  # On Windows use `venv\Scripts\activate`
    ```

3. Install the dependencies:
    ```sh
    pip install --upgrade pip
    pip install -r requirements.txt
    ```

## Usage
1. Run the application:
    ```sh
    python run.py
    ```

2. Access the APIs:
    - Login: `http://<host>:<port>/login`
    - Readiness: `http://<host>:<port>/readiness`
    - Liveness: `http://<host>:<port>/liveness`

## Configuration
- Ensure sensitive information is not tracked by version control by adding it to `.gitignore`.

## Contributing
1. Fork the repository.
2. Create a new branch (`git checkout -b feature-branch`).
3. Commit your changes (`git commit -am 'Add new feature'`).
4. Push to the branch (`git push origin feature-branch`).
5. Create a new Pull Request.

## License
This project is licensed under the MIT License.
