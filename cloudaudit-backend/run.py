import uvicorn
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

if __name__ == "__main__":
    try:
        from app import app
        uvicorn.run(
            "app:app",
            host="0.0.0.0",
            port=app.config.SERVICE_PORT,
            reload=app.config.DEBUG,
            log_level=app.config.LOG_LEVEL.lower()
        )

    except Exception as e:
        print(f"Server exit with error: {e}")
