import json
from app import app
from ..models.mysql import (add_custom_role, add_custom_role_permissions, add_user_custom_role, get_custom_role,
                            get_custom_role_permissions, update_custom_role_name, delete_custom_role_permissions,
                            delete_custom_role, get_workspace_custom_roles, get_roles, get_user_by_id,
                            add_user_role, remove_user_role, get_roles_of_user, get_custom_roles_of_user,
                            fetch_permissions)
from app.common import (InvalidPermissionException, CustomRoleNotFoundException, InsufficientPermissionsException,
                        ResourceNotFoundException, BadRequestException, UserPermissionEnum)

__all__ = ['GetRolesService', 'CreateCustomRoleService', 'UpdateCustomRoleService', 'DeleteCustomRoleService',
           'AssignRoleService', 'RevokeRoleService']


class GetRolesService:
    def __init__(self, user, req_user_id):
        self.workspace_id = user['workspace_id']
        self.user_id = user['user_id']
        self.req_user_id = req_user_id
        self.conn_pool = app.state.connection_pool

    @staticmethod
    def process_roles(data):
        if not data:
            return []
        roles_data = {}
        for role in data:
            role_id = role["role_id"]
            if role_id not in roles_data:
                roles_data[role_id] = {
                    "id": role_id,
                    "name": role["role_name"],
                    "permissions": []
                }
            roles_data[role_id]["permissions"].append({
                "id": role["permission_id"],
                "name": role["permission_name"]
            })
        return list(roles_data.values())

    async def process(self):
        if not self.req_user_id:
            custom_roles = await get_workspace_custom_roles(self.conn_pool, self.workspace_id)
            roles = await get_roles(self.conn_pool)
            if custom_roles:
                for role in custom_roles:
                    role["permissions"] = json.loads(role["permissions"])
            if roles:
                for role in roles:
                    role["permissions"] = json.loads(role["permissions"])
            return {
                "custom_roles": custom_roles,
                "roles": roles
            }
        else:
            if self.req_user_id != self.user_id:
                raise InsufficientPermissionsException
            db_custom_roles = await get_custom_roles_of_user(self.conn_pool, self.req_user_id)
            db_roles = await get_roles_of_user(self.conn_pool, self.req_user_id)
            custom_roles = self.process_roles(db_custom_roles)
            roles = self.process_roles(db_roles)
            return {
                "custom_roles": custom_roles,
                "roles": roles
            }


class CreateCustomRoleService:
    def __init__(self, message, user):
        self.message = message
        self.user_id = user['user_id']
        self.workspace_id = user['workspace_id']
        self.sys_permissions = user["permissions"]
        self.conn_pool = app.state.connection_pool

    async def process(self):
        # check requested permissions with system permissions
        for permission in self.message.permissions:
            if permission['name'] not in self.sys_permissions:
                raise InvalidPermissionException

        # Always add read_only permission if not already included
        read_only_permission = next((p for p in self.message.permissions if p['name'] == UserPermissionEnum.READ_ONLY.value), None)
        if not read_only_permission:
            # Find read_only permission ID from existing permissions
            all_permissions = await fetch_permissions(self.conn_pool)
            read_only_permission_id = next((p['id'] for p in all_permissions if p['name'] == UserPermissionEnum.READ_ONLY.value), None)
            
            if read_only_permission_id:
                # Add to message permissions
                self.message.permissions.append({"id": read_only_permission_id, "name": UserPermissionEnum.READ_ONLY.value})

        # add custom role to custom_roles table
        custom_role_id = await add_custom_role(self.conn_pool, self.message.name, self.workspace_id)
        # add custom role permissions to custom_role_permissions table
        await add_custom_role_permissions(self.conn_pool, custom_role_id, self.message.permissions)
        # add user custom role to user_custom_roles table
        # await add_user_custom_role(self.conn_pool, self.user_id, custom_role_id)


class UpdateCustomRoleService:
    def __init__(self, message, user):
        self.message = message
        self.user_id = user['user_id']
        self.workspace_id = user['workspace_id']
        self.sys_permissions = user["permissions"]
        self.db_custom_role = None
        self.db_custom_role_permissions = set()
        self.requested_permissions = set()
        self.conn_pool = app.state.connection_pool

    async def check_workspace(self):
        if not self.message.id:
            raise CustomRoleNotFoundException

        self.db_custom_role = await get_custom_role(self.conn_pool, self.message.id)
        if not self.db_custom_role or self.db_custom_role['workspace_id'] != self.workspace_id:
            raise InsufficientPermissionsException

    async def process(self):
        await self.check_workspace()

        # check requested permissions with system permissions
        for permission in self.message.permissions:
            self.requested_permissions.add(permission['id'])
            if permission['name'] not in self.sys_permissions:
                raise InvalidPermissionException

        # Always add read_only permission
        read_only_permission = next((p for p in self.message.permissions if p['name'] == UserPermissionEnum.READ_ONLY.value), None)
        if not read_only_permission:
            # Find read_only permission ID from existing permissions
            all_permissions = await fetch_permissions(self.conn_pool)
            read_only_permission_id = next((p['id'] for p in all_permissions if p['name'] == UserPermissionEnum.READ_ONLY.value), None)

            if read_only_permission_id:
                self.requested_permissions.add(read_only_permission_id)
                # Add to message permissions for logging/reference
                self.message.permissions.append({"id": read_only_permission_id, "name": UserPermissionEnum.READ_ONLY.value})

        self.db_custom_role_permissions = await get_custom_role_permissions(self.conn_pool, self.message.id)
        permissions_to_insert = self.requested_permissions - self.db_custom_role_permissions
        permissions_to_delete = self.db_custom_role_permissions - self.requested_permissions

        if self.db_custom_role['name'] != self.message.name:
            await update_custom_role_name(self.conn_pool, self.message.id, self.message.name)

        if permissions_to_insert:
            await add_custom_role_permissions(self.conn_pool, self.message.id,
                                              [{"id": val} for val in permissions_to_insert])

        if permissions_to_delete:
            await delete_custom_role_permissions(self.conn_pool, self.message.id,
                                                 [{"id": val} for val in permissions_to_delete])


class DeleteCustomRoleService:
    def __init__(self, custom_role_id, user):
        self.custom_role_id = custom_role_id
        self.workspace_id = user['workspace_id']
        self.conn_pool = app.state.connection_pool

    async def process(self):
        db_custom_role = await get_custom_role(self.conn_pool, self.custom_role_id)
        if not db_custom_role:
            raise CustomRoleNotFoundException
        if db_custom_role['workspace_id'] != self.workspace_id:
            raise InsufficientPermissionsException
        await delete_custom_role(self.conn_pool, self.custom_role_id)


class AssignRoleService:
    def __init__(self, message, user):
        self.message = message
        self.requester_user_id = user['user_id']
        self.requester_workspace_id = user['workspace_id']
        self.conn_pool = app.state.connection_pool

    async def process(self):
        # the assignee user should be part of the same workspace
        user_info = await get_user_by_id(self.conn_pool, self.message.user_id)
        if not user_info:
            raise ResourceNotFoundException
        if user_info['workspace_id'] != self.requester_workspace_id:
            raise BadRequestException
        await add_user_role(self.conn_pool, self.message.user_id, self.message.role_id, self.message.is_custom_role)


class RevokeRoleService:
    def __init__(self, message, user):
        self.message = message
        self.requester_user_id = user['user_id']
        self.requester_workspace_id = user['workspace_id']
        self.conn_pool = app.state.connection_pool

    async def process(self):
        # the assignee user should be part of the same workspace
        user_info = await get_user_by_id(self.conn_pool, self.message.user_id)
        if not user_info:
            raise ResourceNotFoundException
        if user_info['workspace_id'] != self.requester_workspace_id:
            raise BadRequestException
        await remove_user_role(self.conn_pool, self.message.user_id, self.message.role_id, self.message.is_custom_role)
