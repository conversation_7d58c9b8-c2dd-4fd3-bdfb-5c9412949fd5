import os
from dotenv import load_dotenv
from celery import Celery
from app.config import Configurations
from app import app


__all__ = ["celery_obj"]

# Load .env file if it exists (for local development)
dotenv_path = os.path.join(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))), ".env"
)
if os.path.exists(dotenv_path):
    load_dotenv(dotenv_path)

app.config = Configurations()


def get_celery():
    return Celery("cloud-audit", broker=app.config.CELERY_BROKER_URL, backend=app.config.CELERY_RESULT_BACKEND)

celery_obj = get_celery()
