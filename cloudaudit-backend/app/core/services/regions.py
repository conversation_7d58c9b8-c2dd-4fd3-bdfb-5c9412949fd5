from app import app
from ..models.mysql import get_cloud_provider_by_id
from app.common.enums import CloudProviderNameEnum, AWSRegionNameEnum


__all__ = ['CloudProviderRegionsService']


class CloudProviderRegionsService:
    def __init__(self, cloud_provider_id):
        self.cloud_provider_id = cloud_provider_id
        self.conn_pool = app.state.connection_pool

    async def process(self):
        # Get cloud provider details
        cloud_provider = await get_cloud_provider_by_id(self.conn_pool, self.cloud_provider_id)

        print(cloud_provider)
        
        if not cloud_provider:
            return {"regions": []}
            
        # Get regions based on cloud provider
        regions = []
        if cloud_provider['name'].lower() == CloudProviderNameEnum.AWS.value:
            regions = self._get_aws_regions()
        # elif cloud_provider['name'] == CloudProviderNameEnum.AZURE.value:
        #     regions = self._get_azure_regions()
        # Add more cloud providers as needed
            
        return {"regions": regions}
        
    def _get_aws_regions(self):
        """Get list of AWS regions"""
        regions = []
        for region in AWSRegionNameEnum:
            region_id = region.value
            # Format region name for display (e.g., us-east-1 -> US East (N. Virginia))
            region_name = region_id.replace('-', ' ').title()
            
            regions.append({
                "id": region_id,
                "name": region_name,
                "description": region.description
            })
            
        return regions
        
    # def _get_azure_regions(self):
    #     """Get list of Azure regions"""
    #     regions = []
    #     for region in AzureRegionNameEnum:
    #         region_id = region.value
    #         # Format region name for display
    #         region_name = region_id.replace('-', ' ').title()
    #
    #         # Add descriptive names for well-known regions
    #         descriptions = {
    #             # Add Azure region descriptions
    #         }
    #
    #         description = descriptions.get(region_id, None)
    #
    #         regions.append({
    #             "id": region_id,
    #             "name": region_name,
    #             "description": description
    #         })
    #
    #     return regions