from app import app
from ..models.mysql import (get_user_data, add_new_user, fetch_permissions, delete_user, add_user_role,
                            get_workspace_name, get_team_members, get_accounts_by_workspace, add_user_account,
                            get_user_by_id, get_roles_of_user, remove_user_role, get_custom_roles_of_user,
                            update_user_password, update_user_info)
from app.common import (UserExistsException, get_encrypted_password, get_utc_timestamp, BadRequestException,
                        ResourceNotFoundException, AdminUserNotDeletedException, CurrentPasswordNotValidException,
                        AdminUserPasswordNotChangedException, AdminUserInfoNotUpdatedException, CannotUpdateYourselfException)
from .roles import GetRolesService

__all__ = ["CreateUserService", "DeleteUserService", "GetPermissionsService", "GetUserInfoService",
           "ListTeamMembersService", "UpdateTeamMemberService", "ChangePasswordService", "UpdateUserInfoService",
           "UpdatePasswordService"]


class CreateUserService:
    def __init__(self, message, user):
        self.message = message
        self.creator = user
        self.conn_pool = app.state.connection_pool

    async def process(self):
        # add user to users table
        user = await get_user_data(self.conn_pool, self.message)
        if user and user.get('email'):
            raise UserExistsException

        password_hash = get_encrypted_password(self.message.password)
        user_id = await add_new_user(
            self.conn_pool, 
            self.message.email, 
            password_hash, 
            self.creator["workspace_id"],
            get_utc_timestamp(), 
            self.creator["user_id"],
            self.message.first_name,
            self.message.last_name
        )

        # map user with role
        await add_user_role(self.conn_pool, user_id, self.message.role_id, self.message.is_custom_role)
        
        # Sync user with all accounts in the workspace
        await self._sync_user_accounts(user_id)
        
    async def _sync_user_accounts(self, user_id):
        """
        Sync the newly created user with all accounts in the workspace.
        This ensures the user has access to all accounts in their workspace.
        """
        # Get all accounts in the workspace
        workspace_accounts = await get_accounts_by_workspace(self.conn_pool, self.creator["workspace_id"])
        
        # Add user to each account
        for account in workspace_accounts:
            await add_user_account(self.conn_pool, user_id, account["id"])


class DeleteUserService:
    def __init__(self, user_to_delete, user):
        self.user = user
        self.user_to_delete = user_to_delete
        self.conn_pool = app.state.connection_pool

    async def process(self):
        # Prevent users from deleting themselves
        if self.user["user_id"] == self.user_to_delete:
            raise BadRequestException

        # Get user info to verify workspace
        user_info = await get_user_by_id(self.conn_pool, self.user_to_delete)

        # Check if user exists and belongs to the same workspace
        if not user_info or user_info['workspace_id'] != self.user['workspace_id']:
            raise ResourceNotFoundException
        elif user_info['created_by'] == self.user_to_delete:
            raise AdminUserNotDeletedException

        # Delete the user
        await delete_user(self.conn_pool, self.user_to_delete)


class GetPermissionsService:
    def __init__(self):
        self.conn_pool = app.state.connection_pool

    async def process(self):
        return await fetch_permissions(self.conn_pool)


class GetUserInfoService:
    def __init__(self, user):
        self.user = user
        self.conn_pool = app.state.connection_pool

    async def process(self):
        workspace_name = await get_workspace_name(self.conn_pool, self.user["workspace_id"])
        data = await GetRolesService(self.user, self.user["user_id"]).process()
        user_info = await get_user_by_id(self.conn_pool, self.user["user_id"])
        
        return {
            "user_id": self.user["user_id"],
            "email": self.user["email"],
            "first_name": user_info.get("first_name"),
            "last_name": user_info.get("last_name"),
            "workspace_id": self.user["workspace_id"],
            "workspace_name": workspace_name,
            "roles": data["roles"],
            "custom_roles": data["custom_roles"]
        }


class ListTeamMembersService:
    def __init__(self, user):
        self.user = user
        self.workspace_id = user["workspace_id"]
        self.conn_pool = app.state.connection_pool

    async def process(self):
        team_members = await get_team_members(self.conn_pool, self.workspace_id)

        # Enhanced team members with roles information
        enhanced_members = []
        for member in team_members:
            if self.user["user_id"] == member["id"]:
                continue

            # Get user's roles
            user_roles = await get_roles_of_user(self.conn_pool, member["id"])
            user_custom_roles = await get_custom_roles_of_user(self.conn_pool, member["id"])

            # Determine role information
            role_name = None
            is_custom_role = False

            if user_custom_roles and len(user_custom_roles) > 0:
                role_name = user_custom_roles[0]["role_name"]
                is_custom_role = True
            elif user_roles and len(user_roles) > 0:
                role_name = user_roles[0]["role_name"]

            member_info = {
                "id": member["id"],
                "email": member["email"],
                "first_name": member.get("first_name"),
                "last_name": member.get("last_name"),
                "is_owner": member["created_by"] == member["id"],
                "role_name": role_name,
                "is_custom_role": is_custom_role,
                "created_at": member["created_at"],
            }
            enhanced_members.append(member_info)

        return enhanced_members


class UpdateTeamMemberService:
    def __init__(self, message, user):
        self.message = message
        self.updater = user
        self.conn_pool = app.state.connection_pool

    async def process(self):
        # Get user info to verify workspace
        user_to_update = await get_user_by_id(self.conn_pool, self.message.user_id)

        # Check if user exists and belongs to the same workspace
        if not user_to_update or user_to_update['workspace_id'] != self.updater['workspace_id']:
            raise ResourceNotFoundException

        # Prevent users from updating themselves
        elif self.updater["user_id"] == self.message.user_id:
            raise CannotUpdateYourselfException

        # Prevent updating admin users (who created themselves)
        elif user_to_update['created_by'] == self.message.user_id:
            raise AdminUserInfoNotUpdatedException

        # Update user info if provided
        updates = {}
        if self.message.first_name is not None:
            updates["first_name"] = self.message.first_name
            
        if self.message.last_name is not None:
            updates["last_name"] = self.message.last_name
            
        if updates:
            await update_user_info(self.conn_pool, self.message.user_id, updates)

        # Update role if provided
        if self.message.role_id is not None:
            # First remove existing roles
            existing_roles = await get_roles_of_user(self.conn_pool, self.message.user_id)
            for role in existing_roles:
                await remove_user_role(self.conn_pool, self.message.user_id, role["role_id"], False)
            
            # Also remove any existing custom roles
            existing_custom_roles = await get_custom_roles_of_user(self.conn_pool, self.message.user_id)
            for role in existing_custom_roles:
                await remove_user_role(self.conn_pool, self.message.user_id, role["role_id"], True)

            # Add new role
            await add_user_role(self.conn_pool, self.message.user_id, self.message.role_id, self.message.is_custom_role)

        return {
            "user_id": self.message.user_id,
            "updated_by": self.updater["user_id"]
        }


class ChangePasswordService:
    def __init__(self, message, user):
        self.message = message
        self.updater = user
        self.conn_pool = app.state.connection_pool

    async def process(self):
        # Get user info to verify workspace
        user_to_update = await get_user_by_id(self.conn_pool, self.message.user_id)

        # Check if user exists and belongs to the same workspace
        if not user_to_update or user_to_update['workspace_id'] != self.updater['workspace_id']:
            raise ResourceNotFoundException

        # Prevent admin users from being updated by others
        if user_to_update['created_by'] == self.message.user_id and self.updater["user_id"] != self.message.user_id:
            raise AdminUserPasswordNotChangedException

        # Update the password
        password_hash = get_encrypted_password(self.message.password)
        await update_user_password(self.conn_pool, self.message.user_id, password_hash)

        return {
            "user_id": self.message.user_id,
            "updated_by": self.updater["user_id"]
        }


class UpdateUserInfoService:
    def __init__(self, message, user):
        self.message = message
        self.user = user
        self.conn_pool = app.state.connection_pool

    async def process(self):
        updates = {}

        # Check which fields need to be updated
        if self.message.first_name is not None:
            updates["first_name"] = self.message.first_name

        if self.message.last_name is not None:
            updates["last_name"] = self.message.last_name

        if self.message.email is not None and self.message.email != self.user["email"]:
            # Check if email already exists for a different user
            existing_user = await get_user_data(self.conn_pool, self.message)
            if existing_user and existing_user.get('id') != self.user["user_id"]:
                raise UserExistsException
            updates["email"] = self.message.email

        # If there are updates to make
        if updates:
            await update_user_info(self.conn_pool, self.user["user_id"], updates)

        return {
            "user_id": self.user["user_id"],
            "updated": bool(updates)
        }


class UpdatePasswordService:
    def __init__(self, message, user):
        self.message = message
        self.user = user
        self.conn_pool = app.state.connection_pool

    async def process(self):
        # Get the user's current password hash
        user_info = await get_user_by_id(self.conn_pool, self.user["user_id"])
        
        if not user_info:
            raise ResourceNotFoundException
            
        # Verify the current password
        current_password_hash = get_encrypted_password(self.message.current_password)
        if current_password_hash != user_info.get("password_hash"):
            raise CurrentPasswordNotValidException
            
        # Update to the new password
        new_password_hash = get_encrypted_password(self.message.new_password)
        await update_user_password(self.conn_pool, self.user["user_id"], new_password_hash)
            
        return {
            "user_id": self.user["user_id"],
            "updated": True
        }
