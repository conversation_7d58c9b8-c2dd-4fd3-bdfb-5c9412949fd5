from datetime import datetime, timedelta
from app import app
from app.core.models.mysql import get_user_data, replace_refresh_token
from app.common import (InvalidCredentialsException, InternalServerException, get_encrypted_password,
                        create_access_token, create_refresh_token)


__all__ = ['LoginService']


class LoginService:
    def __init__(self, message):
        self.message = message
        self.conn_pool = app.state.connection_pool

    async def process(self):
        user = await get_user_data(self.conn_pool, self.message)
        if not user or user.get("password_hash") != get_encrypted_password(self.message.password):
            raise InvalidCredentialsException

        access_token = create_access_token(
            data={"sub": self.message.email, "user_id": user["id"], "workspace_id": user["workspace_id"]},
            expires_delta=timedelta(minutes=app.config.ACCESS_TOKEN_EXPIRE_MINUTES)
        )

        refresh_token = create_refresh_token(
            data={"sub": self.message.email, "user_id": user["id"], "workspace_id": user["workspace_id"]},
            expires_delta=timedelta(days=app.config.REFRESH_TOKEN_EXPIRE_DAYS)
        )

        try:
            await replace_refresh_token(
                conn_pool=self.conn_pool,
                user_id=user.get("id"),
                refresh_token=refresh_token,
                expires_at=datetime.utcnow() + timedelta(days=app.config.REFRESH_TOKEN_EXPIRE_DAYS))

            return {
                "access_token": access_token,
                "refresh_token": refresh_token
            }
        except Exception as error:
            raise InternalServerException
