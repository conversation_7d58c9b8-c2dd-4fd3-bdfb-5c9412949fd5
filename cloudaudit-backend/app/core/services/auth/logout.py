from app import app
from app.core.models.mysql import delete_refresh_token
from app.common import InvalidCredentialsException


__all__ = ['LogoutService']


class LogoutService:
    def __init__(self, message):
        self.message = message
        self.conn_pool = app.state.connection_pool

    async def process(self):
        result = await delete_refresh_token(self.conn_pool, self.message.refresh_token)
        if not result.get("row_count"):
            raise InvalidCredentialsException
