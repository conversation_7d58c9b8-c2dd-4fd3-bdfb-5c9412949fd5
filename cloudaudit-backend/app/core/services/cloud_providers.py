from app import app
from ..models.mysql import get_cloud_providers


__all__ = ['CloudProviderService']


class CloudProviderService:
    def __init__(self):
        self.conn_pool = app.state.connection_pool

    async def process(self):
        result = await get_cloud_providers(self.conn_pool)
        cloud_providers = {'cloud_providers': [{'id': row.get('id'), 'name': row.get('name'), 'is_enable': row.get('is_enable')} for row in result]}
        return cloud_providers
