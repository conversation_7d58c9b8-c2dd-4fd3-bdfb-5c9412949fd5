from app import app
from ..models.mysql import (
    check_active_running_scans,
    create_scan,
    check_scan_from_account,
    get_services,
    update_scan,
    get_user_scans,
    get_user_scans_count,
    get_scan_detail,
    get_scan_services,
    sync_scan_services,
)
from app.common.exception import (
    MaxScanAccountsException,
    ScanAlreadyRunningException,
    ServiceRequiredException,
    ServiceNotValidException,
    ScanNotFoundException
)
from app.common.enums import ScanStatusEnum, ScanServiceStatusEnum
from app.core.services.celery_conf import scan_service_task


__all__ = ["CreateScanService", "GetScansService", "GetScanDetailService"]


class CreateScanService:
    def __init__(self, message, user):
        self.message = message
        self.user_id = user["user_id"]
        self.conn_pool = app.state.connection_pool

    async def validate_scan_status_for_account(self):
        # Check if the scan is already running for the given account
        active_running_accounts, given_account_running_scans = await check_active_running_scans(
            self.conn_pool, self.message, self.user_id
        )
        if active_running_accounts >= app.config.MAX_SCAN_ACCOUNTS:
            raise MaxScanAccountsException
        if given_account_running_scans:
            raise ScanAlreadyRunningException

    async def validate_services(self):
        # check services for the given account valid for the given cloud provider
        if not self.message.services:
            raise ServiceRequiredException

        cloud_provider_services = await get_services(self.conn_pool, self.message.cloud_provider_id)
        # Validate if message.services are valid with respect to cloud_provider_services
        valid_service_ids = {service["id"] for service in cloud_provider_services}
        invalid_services = [service_id for service_id in self.message.services if service_id not in valid_service_ids]

        if invalid_services:
            raise ServiceNotValidException
        # return valid services
        return [service for service in cloud_provider_services if service["id"] in self.message.services]

    async def get_account_scan_id(self):
        # Check if the scan is already exists for the given account
        scan_id = await check_scan_from_account(self.conn_pool, self.message)
        if scan_id:
            # set scan status to running
            await update_scan(self.conn_pool, scan_id, ScanStatusEnum.RUNNING.value, update_start=True)
        else:
            # Create a new scan if it doesn't exist
            scan_id = await create_scan(self.conn_pool, self.message)
        return scan_id

    async def process(self):
        services = await self.validate_services()
        await self.validate_scan_status_for_account()
        scan_id = await self.get_account_scan_id()

        # sync scan_services for all the service (either add or update the scan_service status to pending)
        for service in services:
            await sync_scan_services(self.conn_pool, scan_id, service["id"], ScanServiceStatusEnum.PENDING.value)

        # Start the scan for each service
        for service in services:
            scan_service_task.apply_async(args=(scan_id, service, self.message.__dict__))


class GetScansService:
    def __init__(self, user, page=1, page_size=10):
        self.user_id = user["user_id"]
        self.conn_pool = app.state.connection_pool
        self.page = page
        self.page_size = page_size

    async def process(self):
        total_scans = await get_user_scans_count(self.conn_pool, self.user_id)
        scans = await get_user_scans(self.conn_pool, self.user_id, self.page, self.page_size)

        total_pages = (total_scans + self.page_size - 1) // self.page_size  # Ceiling division

        return {
            "data": scans,
            "pagination": {"total": total_scans, "page": self.page, "page_size": self.page_size, "total_pages": total_pages},
        }


class GetScanDetailService:
    def __init__(self, user, scan_id):
        self.user_id = user["user_id"]
        self.scan_id = scan_id
        self.conn_pool = app.state.connection_pool

    async def process(self):
        # Get basic scan information
        scan = await get_scan_detail(self.conn_pool, self.scan_id, self.user_id)
        if not scan:
            raise ScanNotFoundException

        # Get services for this scan
        scan_services = await get_scan_services(self.conn_pool, self.scan_id)

        # Combine data
        scan["services"] = scan_services

        return scan
