import json
from app import app
from ..models.mysql import (get_cloud_provider_by_id, add_account, get_accounts, delete_account,
                            add_user_account, get_account_detail, get_account_recent_scans, get_accounts_with_findings,
                            get_team_members)
from app.common import (CloudProviderNameEnum, get_utc_timestamp, hide_sensitive_info, ResourceNotFoundException,
                        ResourceNotFoundOrInsufficientPermissionsException)
from .api_cloud_providers import AWSService


__all__ = ['AddAccountService', 'GetAccountsService', 'DeleteAccountService', 'GetAccountDetailService']


class AddAccountService:
    def __init__(self, message, user):
        self.message = message
        self.user_id = user['user_id']
        self.workspace_id = user['workspace_id']
        self.service = None
        self.cloud_provider_id = None
        self.cloud_provider_name = None
        self.conn_pool = app.state.connection_pool

    async def add_account_db(self, acc_name_prefix=""):
        return await add_account(self.conn_pool, {
            # "account_name": (acc_name_prefix or "Management") + " : " + self.message.account_name,
            "account_name": self.message.account_name,
            "user_id": self.user_id,
            "cloud_provider_id": self.message.cloud_provider_id,
            "workspace_id": self.workspace_id,
            "credential_data": json.dumps(self.message.credentials),
            "created_at": get_utc_timestamp()
        })

    async def process(self):
        cloud_provider_data = await get_cloud_provider_by_id(self.conn_pool, self.message.cloud_provider_id)
        self.cloud_provider_name = cloud_provider_data['name'].lower()

        if self.cloud_provider_name == CloudProviderNameEnum.AWS.value:
            self.service = AWSService(self.message.credentials)
        # elif self.cloud_provider_name == CloudProviderNameEnum.GCP.value:

        cloud_provider_info = await self.service.test_connection()
        self.message.credentials.update(cloud_provider_info)
        await self.service.verify_account_exists(self.conn_pool, self.message, self.user_id)
        account_id = await self.add_account_db()
        
        # Sync all workspace users with this new account
        await self._sync_account_with_workspace_users(account_id)

        # Check if given account is management account or not
        if self.message.is_organization:
            await self.service.process_child_accounts(self.conn_pool, self.message, self.user_id, self.add_account_db)

    async def _sync_account_with_workspace_users(self, account_id):
        """
        Sync the newly created account with all users in the workspace.
        This ensures all workspace users have access to the new account.
        """
        # Get all team members in the workspace
        team_members = await get_team_members(self.conn_pool, self.workspace_id)
        
        # Add each user to the account
        for member in team_members:
            await add_user_account(self.conn_pool, member["id"], account_id)


class GetAccountsService:
    def __init__(self, cloud_provider_id, workspace_id):
        self.cloud_provider_id = cloud_provider_id
        self.workspace_id = workspace_id
        self.conn_pool = app.state.connection_pool

    async def process(self):
        # Get accounts with finding counts in a single query
        accounts_with_findings = await get_accounts_with_findings(
            self.conn_pool,
            self.cloud_provider_id,
            self.workspace_id
        )

        # Format the response
        accounts = []
        for row in accounts_with_findings:
            cloud_provider = await get_cloud_provider_by_id(self.conn_pool, row['cloud_provider_id'])
            account_data = {
                'id': row.get('id'),
                'account_name': row.get('account_name'),
                'account_id': row.get('account_id'),
                'cloud_provider_id': row['cloud_provider_id'],
                'cloud_provider_name': cloud_provider['name'] if cloud_provider else "Unknown",
                'created_at': row.get('created_at'),
                'last_scan_date': row.get('last_scan_date'),
                'failed_findings': row.get('failed_findings', 0),
                'total_scans': row.get('total_scans', 0),
            }
            accounts.append(account_data)

        return {"accounts": accounts}


class DeleteAccountService:
    def __init__(self, account_id, user):
        self.account_id = account_id
        self.user_id = user.get('user_id')
        self.workspace_id = user.get('workspace_id')
        self.conn_pool = app.state.connection_pool

    async def process(self):
        # Check if user has access to this account
        account_access = await get_account_detail(self.conn_pool, self.account_id, self.user_id)
        if not account_access:
            raise ResourceNotFoundOrInsufficientPermissionsException

        return await delete_account(self.conn_pool, self.account_id)


class GetAccountDetailService:
    def __init__(self, account_id, user):
        self.account_id = account_id
        self.user_id = user.get('user_id')
        self.workspace_id = user.get('workspace_id')
        self.conn_pool = app.state.connection_pool

    async def process(self):
        # Get account details
        account = await get_account_detail(self.conn_pool, self.account_id, self.user_id)
        if not account:
            raise ResourceNotFoundException

        # Get cloud provider name
        cloud_provider = await get_cloud_provider_by_id(self.conn_pool, account['cloud_provider_id'])
        account['cloud_provider_name'] = cloud_provider['name'] if cloud_provider else "Unknown"

        # Parse credential data
        if 'credential_data' in account and account['credential_data']:
            account['credential_data'] = hide_sensitive_info(json.loads(account['credential_data']))

        # Get recent scans (last 5)
        recent_scans = await get_account_recent_scans(self.conn_pool, self.account_id, limit=1)
        account['recent_scans'] = recent_scans

        return account
