import asyncio
from celery.utils.log import get_task_logger
from ..config import BaseChecksProcessor
from app.common import (AWSServiceNameEnum, SeverityEnum, ResourceComplianceStatusEnum,
                        EKSChecksDescriptionEnum)

__all__ = ['ChecksProcessor']

logger = get_task_logger(__name__)


class ChecksProcessor(BaseChecksProcessor):
    def __init__(self, credentials, regions=[]):
        super().__init__(credentials, regions)
        self.clusters = None
        self.replication_groups = None
        self.findings = {
            "private_cluster_endpoint": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EKSChecksDescriptionEnum.PRIVATE_CLUSTER_ENDPOINT.value,
                "severity": SeverityEnum.HIGH.value
            },
            "supported_kubernetes_version": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EKSChecksDescriptionEnum.SUPPORTED_KUBERNETES_VERSION.value,
                "severity": SeverityEnum.HIGH.value
            },
            "encrypted_kubernetes_secrets": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EKSChecksDescriptionEnum.ENCRYPTED_KUBERNETES_SECRETS.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "audit_logging_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EKSChecksDescriptionEnum.AUDIT_LOGGING_ENABLED.value,
                "severity": SeverityEnum.MEDIUM.value
            }
        }

    async def check_private_cluster_endpoint(self):
        """
        EKS cluster endpoints should not be publicly accessible.
        """
        all_compliant = True  # Track overall compliance status

        for cluster_arn in self.clusters.get("clusters", []):
            cluster_name = cluster_arn.split("/")[-1]
            cluster_details = await self.client.describe_cluster(name=cluster_name)
            endpoint_public_access = cluster_details.get("cluster", {}).get("resourcesVpcConfig", {}).get(
                "endpointPublicAccess", True)

            private_endpoint = not endpoint_public_access

            resource_status = ResourceComplianceStatusEnum.PASS.value if private_endpoint else ResourceComplianceStatusEnum.FAIL.value

            self.findings["private_cluster_endpoint"]["details"].append({
                "cluster_name": cluster_name,
                "region": self.client.meta.region_name,
                "endpoint_public_access": endpoint_public_access,
                "private_endpoint": private_endpoint,
                "status": resource_status,
                "compliance": private_endpoint
            })

            if not private_endpoint:
                all_compliant = False

        # Set the main status for the check
        if self.findings["private_cluster_endpoint"]["status"] and not all_compliant:
            self.findings["private_cluster_endpoint"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_supported_kubernetes_version(self):
        """
        EKS clusters should run on a supported Kubernetes version.
        """
        all_compliant = True  # Track overall compliance status
        #TODO: Fetch supported versions from AWS API or configuration
        supported_versions = ["1.30", "1.31", "1.32"]  # Define supported Kubernetes versions

        for cluster_arn in self.clusters.get("clusters", []):
            cluster_name = cluster_arn.split("/")[-1]
            cluster_details = await self.client.describe_cluster(name=cluster_name)
            kubernetes_version = cluster_details.get("cluster", {}).get("version", "")

            is_supported_version = kubernetes_version in supported_versions

            resource_status = ResourceComplianceStatusEnum.PASS.value if is_supported_version else ResourceComplianceStatusEnum.FAIL.value

            self.findings["supported_kubernetes_version"]["details"].append({
                "cluster_name": cluster_name,
                "region": self.client.meta.region_name,
                "kubernetes_version": kubernetes_version,
                "is_supported_version": is_supported_version,
                "status": resource_status,
                "compliance": is_supported_version
            })

            if not is_supported_version:
                all_compliant = False

        # Set the main status for the check
        if self.findings["supported_kubernetes_version"]["status"] and not all_compliant:
            self.findings["supported_kubernetes_version"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_encrypted_kubernetes_secrets(self):
        """
        EKS clusters should use encrypted Kubernetes secrets.
        """
        all_compliant = True  # Track overall compliance status

        for cluster_arn in self.clusters.get("clusters", []):
            cluster_name = cluster_arn.split("/")[-1]
            cluster_details = await self.client.describe_cluster(name=cluster_name)
            encryption_config = cluster_details.get("cluster", {}).get("encryptionConfig", [])

            has_secrets_encryption = any(
                "secrets" in provider.get("resources", [])
                for config in encryption_config
                for provider in config.get("provider", {}).values()
            )

            resource_status = ResourceComplianceStatusEnum.PASS.value if has_secrets_encryption else ResourceComplianceStatusEnum.FAIL.value

            self.findings["encrypted_kubernetes_secrets"]["details"].append({
                "cluster_name": cluster_name,
                "region": self.client.meta.region_name,
                "encryption_config": encryption_config,
                "has_secrets_encryption": has_secrets_encryption,
                "status": resource_status,
                "compliance": has_secrets_encryption
            })

            if not has_secrets_encryption:
                all_compliant = False

        # Set the main status for the check
        if self.findings["encrypted_kubernetes_secrets"]["status"] and not all_compliant:
            self.findings["encrypted_kubernetes_secrets"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_audit_logging_enabled(self):
        """
        EKS clusters should have audit logging enabled.
        """
        all_compliant = True  # Track overall compliance status

        for cluster_arn in self.clusters.get("clusters", []):
            cluster_name = cluster_arn.split("/")[-1]
            cluster_details = await self.client.describe_cluster(name=cluster_name)
            logging_config = cluster_details.get("cluster", {}).get("logging", {}).get("clusterLogging", [])

            audit_logging_enabled = any(
                log.get("types", []) == ["audit"] and log.get("enabled", False)
                for log in logging_config
            )

            resource_status = ResourceComplianceStatusEnum.PASS.value if audit_logging_enabled else ResourceComplianceStatusEnum.FAIL.value

            self.findings["audit_logging_enabled"]["details"].append({
                "cluster_name": cluster_name,
                "region": self.client.meta.region_name,
                "audit_logging_enabled": audit_logging_enabled,
                "status": resource_status,
                "compliance": audit_logging_enabled
            })

            if not audit_logging_enabled:
                all_compliant = False

            logger.info(f"all_compliant: {all_compliant}")

        logger.info(f"all_compliant-out: {all_compliant}")
        # Set the main status for the check
        if self.findings["audit_logging_enabled"]["status"] and not all_compliant:
            self.findings["audit_logging_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    def get_check_functions(self):
        return [
            self.check_private_cluster_endpoint,
            self.check_supported_kubernetes_version,
            self.check_encrypted_kubernetes_secrets,
            self.check_audit_logging_enabled
        ]

    async def run_checks(self):
        for region in self.regions:
            logger.info(f"Scanning region: {region}")
            session = self.get_session(region)
            if not await self.is_region_accessible():
                logger.warning(f"Region {region} is not accessible. Skipping checks.")
                continue
            async with session.client(AWSServiceNameEnum.EKS.value, region_name=region) as client:
                self.client = client

                self.clusters = await self.client.list_clusters()

                for check_function in self.get_check_functions():
                    await check_function()
            await asyncio.sleep(5)

        return self.findings
