import json
from celery.utils.log import get_task_logger
from ..config import BaseRemediationProcessor
from app.common import AWSServiceNameEnum, LAMBDA_SUPPORTED_RUNTIMES

__all__ = ['RemediationProcessor']

logger = get_task_logger(__name__)


class RemediationProcessor(BaseRemediationProcessor):
    """
    Processor for remediating Lambda compliance issues.
    Inherits from BaseRemediationProcessor to reuse AWS session management.
    """

    def __init__(self, credentials, regions=[]):
        super().__init__(credentials, regions)
        self.remediation_results = {
            "prohibit_public_access": {
                "status": False,
                "message": "",
                "field_updates": {"public_access": False}
            },
            "supported_runtimes": {
                "status": False,
                "message": "",
                "field_updates": {"is_supported": True}
            },
            "multi_az_vpc_operation": {
                "status": False,
                "message": "",
                "field_updates": {"multi_az": True}
            },
            "lambda_code_signing": {
                "status": False,
                "message": "",
                "field_updates": {"code_signing_enabled": True}
            },
            "lambda_dlq_configured": {
                "status": False,
                "message": "",
                "field_updates": {"dlq_configured": True}
            }
        }

    async def remediate_lambda_prohibit_public_access(self, details):
        """
        Remediate Lambda functions by removing public access from resource-based policies.
        """
        try:
            function_name = details.get("function_name", "")
            region = details.get("region", "")

            if not function_name or not region:
                return False, "Missing function name or region"

            # Get the current policy
            try:
                policy_response = await self.client.get_policy(
                    FunctionName=function_name
                )
                policy_json = json.loads(policy_response.get('Policy', '{}'))

                # Track if we made any changes
                changes_made = False

                # Remove any statements that allow public access
                updated_statements = []
                for statement in policy_json.get('Statement', []):
                    principal = statement.get('Principal', {})
                    if isinstance(principal, dict) and principal.get('AWS') == '*':
                        changes_made = True
                        continue
                    if principal == '*':
                        changes_made = True
                        continue
                    updated_statements.append(statement)

                if not changes_made:
                    return True, f"No public access statements found in Lambda function {function_name}"

                # This is a simplification - in reality, you'd need to update each permission individually
                # as Lambda doesn't support bulk policy updates
                for statement in policy_json.get('Statement', []):
                    if statement not in updated_statements:
                        await self.client.remove_permission(
                            FunctionName=function_name,
                            StatementId=statement.get('Sid', '')
                        )

                logger.info(f"Successfully removed public access from Lambda function {function_name}")
                return True, f"Successfully removed public access from Lambda function {function_name}"
            except self.client.exceptions.ResourceNotFoundException:
                # No policy exists, so nothing to remediate
                return True, f"No resource policy found for Lambda function {function_name}"

        except Exception as e:
            error_msg = f"Failed to remove public access from Lambda function {function_name}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_lambda_supported_runtimes(self, details):
        """
        Remediate Lambda functions by updating to supported runtimes.
        """
        try:
            function_name = details.get("function_name", "")
            current_runtime = details.get("runtime", "")
            region = details.get("region", "")

            if not function_name or not current_runtime or not region:
                return False, "Missing function name, runtime, or region"

            # Fetch the function's configuration and current runtime
            function_config = await self.client.get_function(
                FunctionName=function_name
            )
            current_runtime = function_config.get('Configuration', {}).get('Runtime', '')
            if not current_runtime:
                return False, f"Failed to determine current runtime for Lambda function {function_name}"

            # Updated supported runtimes list as per the latest AWS documentation
            if current_runtime in LAMBDA_SUPPORTED_RUNTIMES:
                return True, f"Lambda function {function_name} is already using a supported runtime"

            # Map of deprecated runtimes to their recommended replacements
            runtime_upgrades = {
                "nodejs14.x": "nodejs18.x",
                "nodejs16.x": "nodejs18.x",
                "python3.7": "python3.9",
                "python3.8": "python3.9",
                "java8": "java11",
                "dotnetcore3.1": "dotnet6",
                "ruby2.7": "ruby3.2"
            }

            if current_runtime in runtime_upgrades:
                new_runtime = runtime_upgrades[current_runtime]

                # Update the function configuration with the new runtime
                await self.client.update_function_configuration(
                    FunctionName=function_name,
                    Runtime=new_runtime
                )

                logger.info(
                    f"Successfully updated Lambda function {function_name} runtime from {current_runtime} to {new_runtime}")
                return True, f"Successfully updated Lambda function {function_name} runtime from {current_runtime} to {new_runtime}"
            else:
                return False, f"No upgrade path defined for runtime {current_runtime}"

        except Exception as e:
            error_msg = f"Failed to update runtime for Lambda function {function_name}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_lambda_multi_az_vpc_operation(self, details):
        """
        Remediate Lambda functions by ensuring they operate across multiple AZs when in a VPC.
        """
        try:
            function_name = details.get("function_name", "")
            region = details.get("region", "")

            if not function_name or not region:
                return False, "Missing function name or region"

            # Get current configuration
            function_config = await self.client.get_function(
                FunctionName=function_name
            )

            vpc_config = function_config.get('Configuration', {}).get('VpcConfig', {})
            if not vpc_config or not vpc_config.get('SubnetIds'):
                return False, f"Lambda function {function_name} is not configured to run in a VPC"

            # Get all available subnets in the VPC
            vpc_id = vpc_config.get('VpcId')
            if not vpc_id:
                return False, f"Could not determine VPC ID for Lambda function {function_name}"

            # Initialize EC2 client to get subnet information
            session = self.get_session()
            async with session.client('ec2', region_name=region) as ec2_client:
                subnets_response = await ec2_client.describe_subnets(
                    Filters=[
                        {
                            'Name': 'vpc-id',
                            'Values': [vpc_id]
                        }
                    ]
                )

                # Group subnets by AZ
                subnets_by_az = {}
                for subnet in subnets_response.get('Subnets', []):
                    az = subnet['AvailabilityZone']
                    subnet_id = subnet['SubnetId']
                    if az not in subnets_by_az:
                        subnets_by_az[az] = []
                    subnets_by_az[az].append(subnet_id)

                # Check if we already have subnets in multiple AZs
                current_subnets = set(vpc_config.get('SubnetIds', []))
                current_azs = set()

                for subnet in subnets_response.get('Subnets', []):
                    if subnet['SubnetId'] in current_subnets:
                        current_azs.add(subnet['AvailabilityZone'])

                if len(current_azs) >= 2:
                    return True, f"Lambda function {function_name} is already configured for multi-AZ operation"

                # If we have subnets in multiple AZs, update the function
                if len(subnets_by_az) >= 2:
                    # Select one subnet from each AZ (up to 3 AZs)
                    selected_subnets = []
                    for az, subnet_ids in list(subnets_by_az.items())[:3]:
                        selected_subnets.append(subnet_ids[0])

                    # Update the function configuration
                    await self.client.update_function_configuration(
                        FunctionName=function_name,
                        VpcConfig={
                            'SubnetIds': selected_subnets,
                            'SecurityGroupIds': vpc_config.get('SecurityGroupIds', [])
                        }
                    )

                    logger.info(f"Successfully updated Lambda function {function_name} to operate across multiple AZs")
                    return True, f"Successfully updated Lambda function {function_name} to operate across multiple AZs"
                else:
                    return False, f"Not enough AZs available in VPC {vpc_id} to configure multi-AZ operation"

        except Exception as e:
            error_msg = f"Failed to configure multi-AZ operation for Lambda function {function_name}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_lambda_code_signing(self, details):
        """
        Remediate Lambda functions by enabling code signing.
        """
        try:
            function_name = details.get("function_name", "")
            region = details.get("region", "")

            if not function_name or not region:
                return False, "Missing function name or region"

            # Check if code signing is already enabled
            function_config = await self.client.get_function(
                FunctionName=function_name
            )

            if function_config.get('Configuration', {}).get('CodeSigningConfig'):
                return True, f"Code signing is already enabled for Lambda function {function_name}"

            # Check if a code signing configuration exists
            signing_configs = await self.client.list_code_signing_configs(
                MaxItems=10
            )

            if not signing_configs.get('CodeSigningConfigs'):
                # Create a new code signing configuration
                # Note: This requires a signing profile to be set up in AWS Signer
                return False, "No code signing configurations available. Please create a signing profile in AWS Signer first."

            # Use the first available code signing configuration
            code_signing_config_arn = signing_configs['CodeSigningConfigs'][0]['CodeSigningConfigArn']

            # Associate the code signing configuration with the function
            await self.client.put_function_code_signing_config(
                FunctionName=function_name,
                CodeSigningConfigArn=code_signing_config_arn
            )

            logger.info(f"Successfully enabled code signing for Lambda function {function_name}")
            return True, f"Successfully enabled code signing for Lambda function {function_name}"

        except Exception as e:
            error_msg = f"Failed to enable code signing for Lambda function {function_name}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_lambda_dlq_configured(self, details):
        """
        Remediate Lambda functions by configuring a Dead Letter Queue.
        """
        try:
            function_name = details.get("function_name", "")
            region = details.get("region", "")

            if not function_name or not region:
                return False, "Missing function name or region"

            # Check if DLQ is already configured
            function_config = await self.client.get_function(
                FunctionName=function_name
            )

            existing_dlq = function_config.get('Configuration', {}).get('DeadLetterConfig', {}).get('TargetArn')
            if existing_dlq:
                return True, f"Lambda function {function_name} already has a DLQ configured"

            # Create an SQS queue to use as DLQ
            session = self.get_session()
            async with session.client('sqs', region_name=region) as sqs_client:
                queue_name = f"{function_name}-dlq"
                queue_response = await sqs_client.create_queue(
                    QueueName=queue_name
                )

                queue_url = queue_response.get('QueueUrl')
                if not queue_url:
                    return False, "Failed to create SQS queue for DLQ"

                # Get the queue ARN
                queue_attrs = await sqs_client.get_queue_attributes(
                    QueueUrl=queue_url,
                    AttributeNames=['QueueArn']
                )

                queue_arn = queue_attrs.get('Attributes', {}).get('QueueArn')
                if not queue_arn:
                    return False, "Failed to get ARN for SQS queue"

                # Update the Lambda function with the DLQ configuration
                await self.client.update_function_configuration(
                    FunctionName=function_name,
                    DeadLetterConfig={
                        'TargetArn': queue_arn
                    }
                )

                logger.info(f"Successfully configured DLQ for Lambda function {function_name}")
                return True, f"Successfully configured DLQ for Lambda function {function_name}"

        except Exception as e:
            error_msg = f"Failed to configure DLQ for Lambda function {function_name}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def get_remediation_functions(self):
        """
        Return a mapping of policy checks to remediation functions.
        """
        return {
            "prohibit_public_access": self.remediate_lambda_prohibit_public_access,
            "supported_runtimes": self.remediate_lambda_supported_runtimes,
            "multi_az_vpc_operation": self.remediate_lambda_multi_az_vpc_operation,
            "lambda_code_signing": self.remediate_lambda_code_signing,
            "lambda_dlq_configured": self.remediate_lambda_dlq_configured
        }

    async def remediate(self, policy_check, details):
        """
        Run the appropriate remediation function based on the policy check.
        Updates the details object with remediation results and returns it.
        """
        logger.info(f"Starting remediation for policy check: {policy_check}")
        logger.info(f"Details: {details}")

        # Create a copy of the details to update
        updated_details = details.copy()

        # Initialize AWS client
        session = self.get_session()
        region = details.get("region")
        if not region:
            error_msg = "Missing region in details"
            logger.error(error_msg)

            # Update remediation results
            if policy_check in self.remediation_results:
                self.remediation_results[policy_check]["status"] = False
                self.remediation_results[policy_check]["message"] = error_msg

            # Add remediation failure information
            updated_details["remediate"] = {
                "status": "fail",
                "message": f"Error: {error_msg}"
            }

            return False, error_msg, updated_details

        async with session.client(AWSServiceNameEnum.Lambda.value, region_name=region) as client:
            self.client = client

            # Get the appropriate remediation function
            remediation_functions = self.get_remediation_functions()
            if policy_check not in remediation_functions:
                error_msg = f"No remediation function found for policy check: {policy_check}"
                logger.warning(error_msg)

                # Update remediation results
                if policy_check in self.remediation_results:
                    self.remediation_results[policy_check]["status"] = False
                    self.remediation_results[policy_check]["message"] = error_msg

                # Add remediation failure information
                updated_details["remediate"] = {
                    "status": "fail",
                    "message": f"Error: {error_msg}"
                }

                return False, error_msg, updated_details

            # Call the appropriate remediation function
            remediation_function = remediation_functions[policy_check]
            try:
                success, message = await remediation_function(details)

                # Update remediation results
                if policy_check in self.remediation_results:
                    self.remediation_results[policy_check]["status"] = success
                    self.remediation_results[policy_check]["message"] = message

                # Add remediation information to details
                updated_details["remediate"] = {
                    "status": "pass" if success else "fail",
                    "message": message
                }

                # Update compliance and field values if remediation was successful
                if success:
                    updated_details["compliance"] = True

                    # Apply field updates from remediation_results if available
                    if "field_updates" in self.remediation_results[policy_check]:
                        for field, value in self.remediation_results[policy_check]["field_updates"].items():
                            updated_details[field] = value

                return success, message, updated_details
            except Exception as e:
                error_msg = f"Exception during remediation of {policy_check}: {str(e)}"
                logger.error(error_msg)

                # Update remediation results
                if policy_check in self.remediation_results:
                    self.remediation_results[policy_check]["status"] = False
                    self.remediation_results[policy_check]["message"] = error_msg

                # Add remediation failure information
                updated_details["remediate"] = {
                    "status": "fail",
                    "message": f"Error: {error_msg}"
                }

                return False, error_msg, updated_details