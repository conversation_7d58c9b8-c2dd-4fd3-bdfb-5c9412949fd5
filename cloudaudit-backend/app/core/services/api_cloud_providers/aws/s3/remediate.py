import json
from celery.utils.log import get_task_logger
from ..config import BaseRemediationProcessor
from app.common import AWSServiceNameEnum, AWSRegionNameEnum

__all__ = ['RemediationProcessor']

logger = get_task_logger(__name__)


class RemediationProcessor(BaseRemediationProcessor):
    """
    Processor for remediating S3 compliance issues.
    Inherits from BaseChecksProcessor to reuse AWS session management.
    """

    def __init__(self, credentials, regions=[]):
        super().__init__(credentials, regions)
        self.s3control_client = None
        self.remediation_results = {
            "block_public_access_settings_enabled": {
                "status": False,
                "message": "",
                "field_updates": {"block_public_access_enabled": True}
            },
            "s3_acl_security": {
                "status": False,
                "message": "",
                "field_updates": {"acls_used": False, "public_read_access": False, "public_write_access": False}
            },
            "lifecycle_configurations_required": {
                "status": False,
                "message": "",
                "field_updates": {"lifecycle_configuration": True}
            },
            "access_points_block_public_access": {
                "status": False,
                "message": "",
                "field_updates": {"block_public_access_enabled": True}
            },
            "multi_region_access_points_block_public_access": {
                "status": False,
                "message": "",
                "field_updates": {"block_public_access_enabled": True}
            },
            "require_ssl_for_requests": {
                "status": False,
                "message": "",
                "field_updates": {"ssl_required": True}
            },
            "restrict_access_to_other_aws_accounts": {
                "status": False,
                "message": "",
                "field_updates": {"unrestricted_access": False}
            },
            "server_access_logging_enabled": {
                "status": False,
                "message": "",
                "field_updates": {"logging_enabled": True}
            }
        }

    async def remediate_block_public_access_settings_enabled(self, bucket_name, region):
        """
        Remediate S3 general-purpose buckets by enabling block public access settings.
        """
        try:
            # Enable block public access settings for the bucket
            await self.client.put_public_access_block(
                Bucket=bucket_name,
                PublicAccessBlockConfiguration={
                    'BlockPublicAcls': True,
                    'IgnorePublicAcls': True,
                    'BlockPublicPolicy': True,
                    'RestrictPublicBuckets': True
                }
            )

            logger.info(f"Successfully enabled block public access for bucket {bucket_name}")
            return True, f"Successfully enabled block public access for bucket {bucket_name}"
        except Exception as e:
            error_msg = f"Failed to enable block public access for bucket {bucket_name}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_s3_acl_security(self, bucket_name, region):
        """
        Remediate S3 general-purpose buckets by securing ACL configurations:
        - Disable ACLs by setting Object Ownership to BucketOwnerEnforced
        - Block public read/write access
        """
        try:
            # First, set bucket ACL to private to ensure it's in a clean state
            # This blocks public read/write access
            await self.client.put_bucket_acl(
                Bucket=bucket_name,
                ACL='private'
            )

            # Enable block public access settings
            await self.client.put_public_access_block(
                Bucket=bucket_name,
                PublicAccessBlockConfiguration={
                    'BlockPublicAcls': True,
                    'IgnorePublicAcls': True,
                    'BlockPublicPolicy': True,
                    'RestrictPublicBuckets': True
                }
            )

            # Now disable ACLs by setting Object Ownership to "BucketOwnerEnforced"
            await self.client.put_bucket_ownership_controls(
                Bucket=bucket_name,
                OwnershipControls={
                    'Rules': [
                        {
                            'ObjectOwnership': 'BucketOwnerEnforced'
                        }
                    ]
                }
            )

            logger.info(f"Successfully secured ACL configurations for bucket {bucket_name}")
            return True, f"Successfully secured ACL configurations for bucket {bucket_name}"
        except Exception as e:
            error_msg = f"Failed to secure ACL configurations for bucket {bucket_name}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_lifecycle_configurations_required(self, bucket_name, region):
        """
        Remediate S3 general-purpose buckets by adding lifecycle configurations.
        """
        try:
            # Add a basic lifecycle configuration
            await self.client.put_bucket_lifecycle_configuration(
                Bucket=bucket_name,
                LifecycleConfiguration={
                    'Rules': [
                        {
                            'Status': 'Enabled',
                            'ID': 'AutoRemediation',
                            'Filter': {
                                'Prefix': ''
                            },
                            'Transitions': [
                                {
                                    'Days': 90,
                                    'StorageClass': 'STANDARD_IA'
                                }
                            ]
                        }
                    ]
                }
            )

            logger.info(f"Successfully added lifecycle configuration for bucket {bucket_name}")
            return True, f"Successfully added lifecycle configuration for bucket {bucket_name}"
        except Exception as e:
            error_msg = f"Failed to add lifecycle configuration for bucket {bucket_name}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_access_points_block_public_access(self, access_point_name, region):
        """
        Remediate S3 access points by enabling block public access settings.
        """
        try:
            # Enable block public access settings for the access point
            await self.s3control_client.put_access_point_policy(
                AccountId=self.credentials["aws_account_id"],
                Name=access_point_name,
                Policy=json.dumps({
                    "Version": "2012-10-17",
                    "Statement": [
                        {
                            "Effect": "Deny",
                            "Principal": "*",
                            "Action": "s3:*",
                            "Resource": "*",
                            "Condition": {
                                "Bool": {
                                    "aws:SecureTransport": "false"
                                }
                            }
                        }
                    ]
                })
            )

            logger.info(f"Successfully enabled block public access for access point {access_point_name}")
            return True, f"Successfully enabled block public access for access point {access_point_name}"
        except Exception as e:
            error_msg = f"Failed to enable block public access for access point {access_point_name}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_multi_region_access_points_block_public_access(self, access_point_name, region):
        """
        Remediate S3 Multi-Region Access Points by enabling block public access settings.

        Note: Block public access settings for Multi-Region Access Points can only be set
        during creation and cannot be modified afterward. This remediation will apply
        a restrictive policy as a workaround.
        """
        try:
            # Apply a restrictive policy that denies all public access
            await self.s3control_client.put_multi_region_access_point_policy(
                AccountId=self.credentials["aws_account_id"],
                Name=access_point_name,
                Policy=json.dumps({
                    "Version": "2012-10-17",
                    "Statement": [
                        {
                            "Effect": "Deny",
                            "Principal": "*",
                            "Action": "s3:*",
                            "Resource": "*",
                            "Condition": {
                                "StringNotEquals": {
                                    "aws:PrincipalAccount": self.credentials["aws_account_id"]
                                }
                            }
                        },
                        {
                            "Effect": "Deny",
                            "Principal": "*",
                            "Action": "s3:*",
                            "Resource": "*",
                            "Condition": {
                                "Bool": {
                                    "aws:SecureTransport": "false"
                                }
                            }
                        }
                    ]
                })
            )

            logger.info(f"Successfully applied restrictive policy for multi-region access point {access_point_name}")
            return True, f"Applied restrictive policy for multi-region access point {access_point_name} (Note: Block public access settings can only be set during creation)"
        except Exception as e:
            error_msg = f"Failed to apply restrictive policy for multi-region access point {access_point_name}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_require_ssl_for_requests(self, bucket_name, region):
        """
        Remediate S3 general-purpose buckets by requiring SSL for requests.
        """
        try:
            # Define the SSL requirement policy statement
            ssl_requirement_statement = {
                "Sid": "RequireSSLOnly",
                "Effect": "Deny",
                "Principal": "*",
                "Action": "s3:*",
                "Resource": [
                    f"arn:aws:s3:::{bucket_name}",
                    f"arn:aws:s3:::{bucket_name}/*"
                ],
                "Condition": {
                    "Bool": {
                        "aws:SecureTransport": "false"
                    }
                }
            }

            # Initialize policy with just the SSL requirement
            policy = {
                "Version": "2012-10-17",
                "Statement": [ssl_requirement_statement]
            }

            # Try to get existing policy
            try:
                existing_policy_response = await self.client.get_bucket_policy(Bucket=bucket_name)
                existing_policy = json.loads(existing_policy_response.get("Policy", "{}"))

                # If there's an existing policy, merge it with our SSL requirement
                if "Statement" in existing_policy:
                    # Check if a similar SSL requirement already exists
                    ssl_already_exists = False
                    for statement in existing_policy["Statement"]:
                        # Check if this is already an SSL requirement statement
                        if (statement.get("Effect") == "Deny" and
                            "aws:SecureTransport" in statement.get("Condition", {}).get("Bool", {}) and
                            statement["Condition"]["Bool"]["aws:SecureTransport"] == "false"):
                            ssl_already_exists = True
                            break

                    # If no SSL requirement exists, add our statement to the existing policy
                    if not ssl_already_exists:
                        existing_policy["Statement"].append(ssl_requirement_statement)
                        policy = existing_policy
                    # Otherwise, use the existing policy as is
                    else:
                        policy = existing_policy

            except Exception:
                # No existing policy, just use our new policy with SSL requirement
                pass

            # Apply the policy
            await self.client.put_bucket_policy(
                Bucket=bucket_name,
                Policy=json.dumps(policy)
            )

            logger.info(f"Successfully added SSL requirement policy for bucket {bucket_name}")
            return True, f"Successfully added SSL requirement policy for bucket {bucket_name}"
        except Exception as e:
            error_msg = f"Failed to add SSL requirement policy for bucket {bucket_name}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_restrict_access_to_other_aws_accounts(self, bucket_name, region):
        """
        Remediate S3 general-purpose buckets by restricting access to other AWS accounts.
        """
        account_id = self.credentials["aws_account_id"]

        try:
            # Get existing policy if any
            try:
                bucket_policy_response = await self.client.get_bucket_policy(Bucket=bucket_name)
                policy_str = bucket_policy_response.get("Policy", "{}")
                policy = json.loads(policy_str)

                modified = False

                if "Statement" in policy:
                    for statement in policy["Statement"]:
                        # Only process Allow statements with AWS principals
                        if statement.get("Effect") != "Allow" or not isinstance(statement.get("Principal"), dict):
                            continue

                        principal = statement.get("Principal", {})
                        if "AWS" not in principal:
                            continue

                        aws_principal = principal["AWS"]

                        # Handle string AWS principal
                        if isinstance(aws_principal, str):
                            # If it's a wildcard or another account, remove it
                            if aws_principal == "*":
                                # Replace wildcard with current account ID to maintain some access
                                statement["Principal"]["AWS"] = account_id
                                modified = True
                            elif aws_principal != account_id and aws_principal.startswith("arn:aws:iam::"):
                                # Replace other account with current account ID
                                statement["Principal"]["AWS"] = account_id
                                modified = True

                        # Handle list AWS principal
                        elif isinstance(aws_principal, list):
                            filtered_principals = []
                            for principal_entry in aws_principal:
                                # Keep only the current account ID
                                if principal_entry == account_id or not (
                                    principal_entry == "*" or
                                    (isinstance(principal_entry, str) and principal_entry.startswith("arn:aws:iam::"))
                                ):
                                    filtered_principals.append(principal_entry)

                            # If we filtered anything out, update the list
                            if len(filtered_principals) != len(aws_principal):
                                if not filtered_principals:
                                    # If no principals remain, add current account
                                    filtered_principals = [account_id]
                                statement["Principal"]["AWS"] = filtered_principals[0] if len(filtered_principals) == 1 else filtered_principals
                                modified = True

                # Only update if we made changes
                if modified:
                    await self.client.put_bucket_policy(
                        Bucket=bucket_name,
                        Policy=json.dumps(policy)
                    )

            except self.client.exceptions.NoSuchBucketPolicy:
                # No existing policy, nothing to do
                pass

            logger.info(f"Successfully restricted access to other AWS accounts for bucket {bucket_name}")
            return True, f"Successfully restricted access to other AWS accounts for bucket {bucket_name}"

        except Exception as e:
            error_msg = f"Failed to restrict access to other AWS accounts for bucket {bucket_name}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_server_access_logging_enabled(self, bucket_name, region):
        """
        Remediate S3 general-purpose buckets by enabling server access logging.
        """
        try:
            # Enable server access logging
            await self.client.put_bucket_logging(
                Bucket=bucket_name,
                BucketLoggingStatus={
                    'LoggingEnabled': {
                        'TargetBucket': bucket_name,
                        'TargetPrefix': f'{bucket_name}-logs/'
                    }
                }
            )

            logger.info(f"Successfully enabled server access logging for bucket {bucket_name}")
            return True, f"Successfully enabled server access logging for bucket {bucket_name}"
        except Exception as e:
            error_msg = f"Failed to enable server access logging for bucket {bucket_name}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def get_remediation_functions(self):
        """
        Return a mapping of policy checks to remediation functions.
        """
        return {
            "block_public_access_settings_enabled": self.remediate_block_public_access_settings_enabled,
            "s3_acl_security": self.remediate_s3_acl_security,
            "lifecycle_configurations_required": self.remediate_lifecycle_configurations_required,
            "access_points_block_public_access": self.remediate_access_points_block_public_access,
            "multi_region_access_points_block_public_access": self.remediate_multi_region_access_points_block_public_access,
            "require_ssl_for_requests": self.remediate_require_ssl_for_requests,
            "restrict_access_to_other_aws_accounts": self.remediate_restrict_access_to_other_aws_accounts,
            "server_access_logging_enabled": self.remediate_server_access_logging_enabled
        }

    async def remediate(self, policy_check, details):
        """
        Run the appropriate remediation function based on the policy check.
        Updates the details object with remediation results and returns it.
        """
        logger.info(f"Starting remediation for policy check: {policy_check}")
        logger.info(f"Details: {details}")

        # Create a copy of the details to update
        updated_details = details.copy()

        # Extract resource details
        resource_type = None
        resource_name = None
        region = details.get("region")

        if "bucket_name" in details:
            resource_type = "bucket"
            resource_name = details["bucket_name"]
        elif "access_point_name" in details:
            resource_type = "access_point"
            resource_name = details["access_point_name"]

        if not resource_name or not region:
            error_msg = "Missing required parameters: resource name or region"
            logger.error(error_msg)
            self.remediation_results[policy_check]["status"] = False
            self.remediation_results[policy_check]["message"] = error_msg
            return False, error_msg, details

        # Initialize AWS clients
        session = self.get_session()
        async with session.client(AWSServiceNameEnum.S3.value, region_name=region) as client, \
                session.client('s3control', region_name=region) as s3control_client:
            self.client = client
            self.s3control_client = s3control_client

            # Get the appropriate remediation function
            remediation_functions = self.get_remediation_functions()
            if policy_check not in remediation_functions:
                error_msg = f"No remediation function found for policy check: {policy_check}"
                logger.warning(error_msg)
                
                # Update remediation results
                if policy_check in self.remediation_results:
                    self.remediation_results[policy_check]["status"] = False
                    self.remediation_results[policy_check]["message"] = error_msg
                
                # Add remediation failure information
                updated_details["remediate"] = {
                    "status": "fail",
                    "message": f"Error: {error_msg}"
                }
                
                return False, error_msg, updated_details

            remediation_func = remediation_functions[policy_check]

            # Call the remediation function with the appropriate parameters
            success = False
            message = ""
            
            if resource_type == "bucket":
                success, message = await remediation_func(resource_name, region)
            elif resource_type == "access_point" and "access_points" in policy_check:
                success, message = await remediation_func(resource_name, region)
            else:
                error_msg = f"Resource type {resource_type} not supported for policy check {policy_check}"
                logger.error(error_msg)
                self.remediation_results[policy_check]["status"] = False
                self.remediation_results[policy_check]["message"] = error_msg
                
                updated_details["remediate"] = {
                    "status": "fail",
                    "message": f"Error: {error_msg}"
                }
                
                return False, error_msg, updated_details

            # Update remediation results
            self.remediation_results[policy_check]["status"] = success
            self.remediation_results[policy_check]["message"] = message

            # Add remediation information
            updated_details["remediate"] = {
                "status": "pass" if success else "fail",
                "message": message if success else f"Error: {message}"
            }
            
            # Update compliance and field values if remediation was successful
            if success:
                updated_details["compliance"] = True
                
                # Apply field updates from remediation_results if available
                if "field_updates" in self.remediation_results[policy_check]:
                    for field, value in self.remediation_results[policy_check]["field_updates"].items():
                        updated_details[field] = value

            return success, message, updated_details
