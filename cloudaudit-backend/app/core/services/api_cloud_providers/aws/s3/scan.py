import json

from celery.utils.log import get_task_logger
from ..config import BaseChecksProcessor
from app.common import (AWSServiceNameEnum, SeverityEnum, ResourceComplianceStatusEnum,
                        S3ChecksDescriptionEnum, AWSRegionNameEnum)

__all__ = ['ChecksProcessor']

logger = get_task_logger(__name__)


class ChecksProcessor(BaseChecksProcessor):
    def __init__(self, credentials, regions=[]):
        super().__init__(credentials, regions)
        self.buckets = None
        self.s3control_client = None
        self.findings = {
            "block_public_access_settings_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": S3ChecksDescriptionEnum.BLOCK_PUBLIC_ACCESS_SETTINGS_ENABLED.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "s3_acl_security": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": S3ChecksDescriptionEnum.S3_ACL_SECURITY.value,
                "severity": SeverityEnum.CRITICAL.value
            },
            "lifecycle_configurations_required": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": S3ChecksDescriptionEnum.LIFECYCLE_CONFIGURATIONS_REQUIRED.value,
                "severity": SeverityEnum.LOW.value
            },
            "access_points_block_public_access": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": S3ChecksDescriptionEnum.ACCESS_POINTS_BLOCK_PUBLIC_ACCESS.value,
                "severity": SeverityEnum.CRITICAL.value
            },
            "multi_region_access_points_block_public_access": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": S3ChecksDescriptionEnum.MULTI_REGION_ACCESS_POINTS_BLOCK_PUBLIC_ACCESS.value,
                "severity": SeverityEnum.HIGH.value
            },
            "require_ssl_for_requests": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": S3ChecksDescriptionEnum.REQUIRE_SSL_FOR_REQUESTS.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "restrict_access_to_other_aws_accounts": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": S3ChecksDescriptionEnum.RESTRICT_ACCESS_TO_OTHER_AWS_ACCOUNTS.value,
                "severity": SeverityEnum.HIGH.value
            },
            "server_access_logging_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": S3ChecksDescriptionEnum.SERVER_ACCESS_LOGGING_ENABLED.value,
                "severity": SeverityEnum.MEDIUM.value
            }
        }

    async def check_s3_block_public_access(self):
        """
        S3 general-purpose buckets should have block public access settings enabled.
        """

        findings = []
        all_compliant = True

        for bucket in self.buckets.get("Buckets", []):
            bucket_name = bucket["Name"]

            # Retrieve the bucket's public access block configuration
            try:
                public_access_block = await self.client.get_public_access_block(Bucket=bucket_name)
                block_settings = public_access_block.get("PublicAccessBlockConfiguration", {})

                # Check if all block public access settings are enabled
                block_public_access_enabled = all([
                    block_settings.get("BlockPublicAcls", False),
                    block_settings.get("IgnorePublicAcls", False),
                    block_settings.get("BlockPublicPolicy", False),
                    block_settings.get("RestrictPublicBuckets", False)
                ])
            except Exception:
                block_public_access_enabled = False

            # Add compliance field
            compliance = block_public_access_enabled

            findings.append({
                "bucket_name": bucket_name,
                "block_public_access_enabled": block_public_access_enabled,
                "region": self.client.meta.region_name,
                "compliance": compliance,
            })

            if not block_public_access_enabled:
                all_compliant = False

        self.findings["block_public_access_settings_enabled"]["details"] = findings
        self.findings["block_public_access_settings_enabled"]["status"] = (
            ResourceComplianceStatusEnum.PASS.value if all_compliant else ResourceComplianceStatusEnum.FAIL.value
        )

    async def check_s3_acl_security(self):
        """
        Comprehensive check for S3 ACL security issues:
        - ACLs should not be used to manage user access to S3 general-purpose buckets
        - S3 general-purpose buckets should block public read access
        - S3 general-purpose buckets should block public write access
        """
        findings = []
        all_compliant = True

        for bucket in self.buckets.get("Buckets", []):
            bucket_name = bucket["Name"]
            region = self.client.meta.region_name

            try:
                # Retrieve the bucket's ACL
                acl = await self.client.get_bucket_acl(Bucket=bucket_name)
                grants = acl.get("Grants", [])

                # Check if any ACL grants are present (for non-canonical users)
                acls_used = any(grant.get("Grantee", {}).get("Type") != "CanonicalUser" for grant in grants)

                # Check if any grant allows public read access
                public_read_access = any(
                    grant.get("Grantee", {}).get("Type") in ["Group"] and
                    grant.get("Grantee", {}).get("URI") == "http://acs.amazonaws.com/groups/global/AllUsers" and
                    grant.get("Permission") == "READ"
                    for grant in grants
                )

                # Check if any grant allows public write access
                public_write_access = any(
                    grant.get("Grantee", {}).get("Type") in ["Group"] and
                    grant.get("Grantee", {}).get("URI") == "http://acs.amazonaws.com/groups/global/AllUsers" and
                    grant.get("Permission") == "WRITE"
                    for grant in grants
                )
            except Exception as e:
                print(e, "=================")
                acls_used = False
                public_read_access = False
                public_write_access = False

            # Determine overall compliance
            compliance = not (acls_used or public_read_access or public_write_access)

            findings.append({
                "bucket_name": bucket_name,
                "acls_used": acls_used,
                "public_read_access": public_read_access,
                "public_write_access": public_write_access,
                "region": region,
                "compliance": compliance
            })

            if not compliance:
                all_compliant = False

        # Update findings
        self.findings["s3_acl_security"]["details"] = findings
        self.findings["s3_acl_security"]["status"] = (
            ResourceComplianceStatusEnum.PASS.value if all_compliant else ResourceComplianceStatusEnum.FAIL.value
        )

    async def check_s3_lifecycle_configurations(self):
        """
        S3 general-purpose buckets should have Lifecycle configurations.
        """

        findings = []
        all_compliant = True

        for bucket in self.buckets.get("Buckets", []):
            bucket_name = bucket["Name"]

            # Check if the bucket has a lifecycle configuration
            try:
                lifecycle_config = await self.client.get_bucket_lifecycle_configuration(Bucket=bucket_name)
                has_lifecycle_config = bool(lifecycle_config.get("Rules", []))
            except Exception:
                has_lifecycle_config = False

            # Add compliance field
            compliance = has_lifecycle_config

            findings.append({
                "bucket_name": bucket_name,
                "has_lifecycle_config": has_lifecycle_config,
                "region": self.client.meta.region_name,
                "compliance": compliance,
            })

            if not has_lifecycle_config:
                all_compliant = False

        self.findings["lifecycle_configurations_required"]["details"] = findings
        self.findings["lifecycle_configurations_required"]["status"] = (
            ResourceComplianceStatusEnum.PASS.value if all_compliant else ResourceComplianceStatusEnum.FAIL.value
        )

    async def check_s3_access_points_block_public_access(self):
        """
        S3 access points should have block public access settings enabled.
        """

        findings = []
        all_compliant = True

        # Retrieve the list of access points
        access_points_response = await self.s3control_client.list_access_points(
            AccountId=self.credentials["aws_account_id"]
        )
        access_points = access_points_response.get("AccessPointList", [])

        for access_point in access_points:
            access_point_name = access_point["Name"]

            # Retrieve the access point's public access block configuration
            try:
                public_access_block = await self.s3control_client.get_access_point_policy_status(
                    AccountId=self.credentials["aws_account_id"],
                    Name=access_point_name
                )
                block_settings = public_access_block.get("PublicAccessBlockConfiguration", {})

                # Check if all block public access settings are enabled
                block_public_access_enabled = all([
                    block_settings.get("BlockPublicAcls", False),
                    block_settings.get("IgnorePublicAcls", False),
                    block_settings.get("BlockPublicPolicy", False),
                    block_settings.get("RestrictPublicBuckets", False)
                ])
            except Exception:
                block_public_access_enabled = False

            # Add compliance field
            compliance = block_public_access_enabled

            findings.append({
                "access_point_name": access_point_name,
                "block_public_access_enabled": block_public_access_enabled,
                "region": self.s3control_client.meta.region_name,
                "compliance": compliance,
            })

            if not block_public_access_enabled:
                all_compliant = False

        self.findings["access_points_block_public_access"]["details"] = findings
        self.findings["access_points_block_public_access"]["status"] = (
            ResourceComplianceStatusEnum.PASS.value if all_compliant else ResourceComplianceStatusEnum.FAIL.value
        )

    async def check_s3_multi_region_access_points_block_public_access(self):
        """
        S3 Multi-Region Access Points should have block public access settings enabled.
        """

        findings = []
        all_compliant = True

        # Retrieve the list of Multi-Region Access Points
        multi_region_access_points_response = await self.s3control_client.list_multi_region_access_points(
            AccountId=self.credentials["aws_account_id"]
        )
        multi_region_access_points = multi_region_access_points_response.get("AccessPoints", [])

        for access_point in multi_region_access_points:
            access_point_name = access_point["Name"]

            # Retrieve the Multi-Region Access Point's public access block configuration
            try:
                public_access_block = await self.s3control_client.get_multi_region_access_point_policy_status(
                    AccountId=self.credentials["aws_account_id"],
                    Name=access_point_name
                )
                block_settings = public_access_block.get("PublicAccessBlockConfiguration", {})

                # Check if all block public access settings are enabled
                block_public_access_enabled = all([
                    block_settings.get("BlockPublicAcls", False),
                    block_settings.get("IgnorePublicAcls", False),
                    block_settings.get("BlockPublicPolicy", False),
                    block_settings.get("RestrictPublicBuckets", False)
                ])
            except Exception:
                block_public_access_enabled = False

            # Add compliance field
            compliance = block_public_access_enabled

            findings.append({
                "access_point_name": access_point_name,
                "block_public_access_enabled": block_public_access_enabled,
                "region": self.s3control_client.meta.region_name,
                "compliance": compliance,
            })

            if not block_public_access_enabled:
                all_compliant = False

        self.findings["multi_region_access_points_block_public_access"]["details"] = findings
        self.findings["multi_region_access_points_block_public_access"]["status"] = (
            ResourceComplianceStatusEnum.PASS.value if all_compliant else ResourceComplianceStatusEnum.FAIL.value
        )

    async def check_s3_require_ssl_for_requests(self):
        """
        S3 general-purpose buckets should require requests to use SSL.
        """

        findings = []
        all_compliant = True

        for bucket in self.buckets.get("Buckets", []):
            bucket_name = bucket["Name"]

            # Retrieve the bucket policy
            try:
                bucket_policy = await self.client.get_bucket_policy(Bucket=bucket_name)
                policy = json.loads(bucket_policy.get("Policy", '{}'))
                policy_statements = policy.get("Statement", [])

                # Check if any statement explicitly denies non-SSL requests
                ssl_required = any(
                    statement.get("Effect") == "Deny" and
                    "aws:SecureTransport" in statement.get("Condition", {}).get("Bool", {}) and
                    statement["Condition"]["Bool"]["aws:SecureTransport"] == "false"
                    for statement in policy_statements
                )
            except Exception as e:
                ssl_required = False

            # Add compliance field
            compliance = ssl_required

            findings.append({
                "bucket_name": bucket_name,
                "ssl_required": ssl_required,
                "region": self.client.meta.region_name,
                "compliance": compliance,
            })

            if not ssl_required:
                all_compliant = False

        self.findings["require_ssl_for_requests"]["details"] = findings
        self.findings["require_ssl_for_requests"]["status"] = (
            ResourceComplianceStatusEnum.PASS.value if all_compliant else ResourceComplianceStatusEnum.FAIL.value
        )

    async def check_s3_restrict_access_to_other_aws_accounts(self):
        """
        S3 general-purpose bucket policies should restrict access to other AWS accounts.
        """
        findings = []
        all_compliant = True
        account_id = self.credentials["aws_account_id"]

        for bucket in self.buckets.get("Buckets", []):
            bucket_name = bucket["Name"]
            region = self.client.meta.region_name
            unrestricted_access = False

            try:
                bucket_policy = await self.client.get_bucket_policy(Bucket=bucket_name)
                policy = json.loads(bucket_policy.get("Policy", '{}'))

                for statement in policy.get("Statement", []):
                    if statement.get("Effect") != "Allow":
                        continue

                    principal = statement.get("Principal", {})
                    
                    # Only check AWS principals (not service principals or other types)
                    if not isinstance(principal, dict) or "AWS" not in principal:
                        continue

                    aws_principal = principal["AWS"]
                    
                    # Check string principal that's another AWS account
                    if isinstance(aws_principal, str):
                        if aws_principal == "*" or (aws_principal != account_id and aws_principal.startswith("arn:aws:iam::")):
                            unrestricted_access = True
                            break
                    
                    # Check list principal for other AWS accounts
                    elif isinstance(aws_principal, list):
                        for principal_entry in aws_principal:
                            if principal_entry == "*" or (principal_entry != account_id and 
                                                         isinstance(principal_entry, str) and 
                                                         principal_entry.startswith("arn:aws:iam::")):
                                unrestricted_access = True
                                break
                        
                        if unrestricted_access:
                            break
                        
            except Exception:
                # No policy or error retrieving policy - considered compliant
                pass

            compliance = not unrestricted_access
            findings.append({
                "bucket_name": bucket_name,
                "unrestricted_access": unrestricted_access,
                "region": region,
                "compliance": compliance,
            })

            if unrestricted_access:
                all_compliant = False

        self.findings["restrict_access_to_other_aws_accounts"]["details"] = findings
        self.findings["restrict_access_to_other_aws_accounts"]["status"] = (
            ResourceComplianceStatusEnum.PASS.value if all_compliant else ResourceComplianceStatusEnum.FAIL.value
        )

    async def check_s3_server_access_logging_enabled(self):
        """
        S3 general-purpose buckets should have server access logging enabled.
        """

        findings = []
        all_compliant = True

        for bucket in self.buckets.get("Buckets", []):
            bucket_name = bucket["Name"]

            try:
                # Retrieve the bucket's logging configuration
                logging_config = await self.client.get_bucket_logging(Bucket=bucket_name)
                logging_enabled = "LoggingEnabled" in logging_config
            except Exception:
                logging_enabled = False

            # Add compliance field
            compliance = logging_enabled

            findings.append({
                "bucket_name": bucket_name,
                "logging_enabled": logging_enabled,
                "region": self.client.meta.region_name,
                "compliance": compliance,
            })

            if not logging_enabled:
                all_compliant = False

        self.findings["server_access_logging_enabled"]["details"] = findings
        self.findings["server_access_logging_enabled"]["status"] = (
            ResourceComplianceStatusEnum.PASS.value if all_compliant else ResourceComplianceStatusEnum.FAIL.value
        )

    def get_check_functions(self):
        return [
            self.check_s3_block_public_access,
            self.check_s3_acl_security,
            self.check_s3_lifecycle_configurations,
            self.check_s3_access_points_block_public_access,
            self.check_s3_multi_region_access_points_block_public_access,
            self.check_s3_require_ssl_for_requests,
            self.check_s3_restrict_access_to_other_aws_accounts,
            self.check_s3_server_access_logging_enabled
        ]

    async def run_checks(self):
        logger.info("AWS Account ID: %s", self.credentials.get("aws_account_id"))
        session = self.get_session()

        # some operation in s3control client is only supported in us-west-2 region.
        async with session.client(AWSServiceNameEnum.S3.value) as client, \
                session.client('s3control', region_name=AWSRegionNameEnum.US_WEST_2.value) as s3control_client:
            self.client = client
            self.s3control_client = s3control_client

            self.buckets = await self.client.list_buckets()

            for check_function in self.get_check_functions():
                await check_function()

        return self.findings
