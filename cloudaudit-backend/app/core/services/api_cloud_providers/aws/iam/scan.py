import datetime
from celery.utils.log import get_task_logger
from ..config import BaseChecksProcessor
from app.common import (AWSServiceNameEnum, SeverityEnum, ResourceComplianceStatusEnum,
                        IAMChecksDescriptionEnum)


__all__ = ['ChecksProcessor']

logger = get_task_logger(__name__)


class ChecksProcessor(BaseChecksProcessor):
    def __init__(self, credentials, regions=[]):
        super().__init__(credentials, regions)
        self.policies = None
        self.users = None
        self.findings = {
            "no_full_admin_privileges": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": IAMChecksDescriptionEnum.NO_FULL_ADMIN_PRIVILEGES.value,
                "severity": SeverityEnum.HIGH.value
            },
            "no_iam_policies_for_users": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": IAMChecksDescriptionEnum.NO_IAM_POLICIES_FOR_USERS.value,
                "severity": SeverityEnum.LOW.value
            },
            "no_wild_card_actions_in_custom_policies": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": IAMChecksDescriptionEnum.NO_WILDCARD_ACTIONS_IN_CUSTOM_POLICIES.value,
                "severity": SeverityEnum.LOW.value
            },
            "access_key_rotation": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": IAMChecksDescriptionEnum.ACCESS_KEY_ROTATION.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "no_root_user_access_key": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": IAMChecksDescriptionEnum.NO_ROOT_USER_ACCESS_KEY.value,
                "severity": SeverityEnum.CRITICAL.value
            },
            "mfa_enabled_for_console_users": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": IAMChecksDescriptionEnum.MFA_ENABLED_FOR_CONSOLE_USERS.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "hardware_mfa_for_root_user": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": IAMChecksDescriptionEnum.HARDWARE_MFA_FOR_ROOT_USER.value,
                "severity": SeverityEnum.CRITICAL.value
            },
            "strong_password_policies": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": IAMChecksDescriptionEnum.STRONG_PASSWORD_POLICIES.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "remove_unused_user_credentials": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": IAMChecksDescriptionEnum.REMOVE_UNUSED_USER_CREDENTIALS.value,
                "severity": SeverityEnum.MEDIUM.value
            }
        }

    async def check_no_full_admin_privileges(self):
        """
        IAM policies should not allow full '*' administrative privileges.
        """

        all_compliant = True

        for policy in self.policies.get("Policies", []):
            policy_arn = policy["Arn"]
            policy_name = policy["PolicyName"]

            # Retrieve policy version details
            policy_version = await self.client.get_policy_version(
                PolicyArn=policy_arn,
                VersionId=policy["DefaultVersionId"]
            )
            statements = policy_version["PolicyVersion"]["Document"].get("Statement", [])

            has_full_admin_privileges = False

            # Check each statement for full administrative privileges
            if not isinstance(statements, list):
                statements = [statements]

            for statement in statements:
                actions = statement.get("Action", [])
                resources = statement.get("Resource", [])
                effect = statement.get("Effect", "")

                if not isinstance(actions, list):
                    actions = [actions]
                if not isinstance(resources, list):
                    resources = [resources]

                if effect == "Allow" and "*" in actions and "*" in resources:
                    has_full_admin_privileges = True
                    break

            # Add compliance field - compliant if no full admin privileges
            compliance = not has_full_admin_privileges

            if (self.findings["no_full_admin_privileges"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and has_full_admin_privileges):
                self.findings["no_full_admin_privileges"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["no_full_admin_privileges"]["details"].append({
                "policy_name": policy_name,
                "policy_arn": policy_arn,
                "has_full_admin_privileges": has_full_admin_privileges,
                "compliance": compliance
            })

            if has_full_admin_privileges:
                all_compliant = False

        if self.findings["no_full_admin_privileges"]["status"] and not all_compliant:
            self.findings["no_full_admin_privileges"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_no_iam_policies_for_users(self):
        """
        IAM users should not have IAM policies attached.
        """

        all_compliant = True

        for user in self.users.get("Users", []):
            user_name = user["UserName"]
            user_arn = user["Arn"]

            # Check for attached policies
            attached_policies = await self.client.list_attached_user_policies(UserName=user_name)
            has_attached_policies = len(attached_policies.get("AttachedPolicies", [])) > 0

            # Add compliance field - compliant if no attached policies
            compliance = not has_attached_policies

            if (self.findings["no_iam_policies_for_users"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and has_attached_policies):
                self.findings["no_iam_policies_for_users"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["no_iam_policies_for_users"]["details"].append({
                "user_name": user_name,
                "user_arn": user_arn,
                "has_attached_policies": has_attached_policies,
                "compliance": compliance
            })

            if has_attached_policies:
                all_compliant = False

        if self.findings["no_iam_policies_for_users"]["status"] and not all_compliant:
            self.findings["no_iam_policies_for_users"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_no_wildcard_actions_in_custom_policies(self):
        """
        IAM customer managed policies that you create should not allow wildcard actions for services.
        """

        all_compliant = True

        for policy in self.policies.get("Policies", []):
            policy_arn = policy["Arn"]
            policy_name = policy["PolicyName"]

            # Retrieve policy version details
            policy_version = await self.client.get_policy_version(
                PolicyArn=policy_arn,
                VersionId=policy["DefaultVersionId"]
            )
            statements = policy_version["PolicyVersion"]["Document"].get("Statement", [])

            has_wildcard_actions = False

            # Check each statement for wildcard actions
            if not isinstance(statements, list):
                statements = [statements]

            for statement in statements:
                actions = statement.get("Action", [])
                effect = statement.get("Effect", "")

                if not isinstance(actions, list):
                    actions = [actions]

                if effect == "Allow" and any("*" in action for action in actions):
                    has_wildcard_actions = True
                    break

            # Add compliance field - compliant if no wildcard actions
            compliance = not has_wildcard_actions

            if (self.findings["no_wild_card_actions_in_custom_policies"][
                "status"] == ResourceComplianceStatusEnum.PASS.value
                    and has_wildcard_actions):
                self.findings["no_wild_card_actions_in_custom_policies"][
                    "status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["no_wild_card_actions_in_custom_policies"]["details"].append({
                "policy_name": policy_name,
                "policy_arn": policy_arn,
                "has_wildcard_actions": has_wildcard_actions,
                "compliance": compliance
            })

            if has_wildcard_actions:
                all_compliant = False

        if self.findings["no_wild_card_actions_in_custom_policies"]["status"] and not all_compliant:
            self.findings["no_wild_card_actions_in_custom_policies"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_access_key_rotation(self):
        """
        IAM users' access keys should be rotated every 90 days or less.
        """

        all_compliant = True

        for user in self.users.get("Users", []):
            user_name = user["UserName"]
            user_arn = user["Arn"]

            # Retrieve access keys for the user
            access_keys = await self.client.list_access_keys(UserName=user_name)

            for access_key in access_keys.get("AccessKeyMetadata", []):
                access_key_id = access_key["AccessKeyId"]
                create_date = access_key["CreateDate"]
                age_in_days = (datetime.datetime.now(datetime.timezone.utc) - create_date).days

                is_key_old = age_in_days > 90
                
                # Add compliance field - compliant if key is not old
                compliance = not is_key_old

                if (self.findings["access_key_rotation"]["status"] == ResourceComplianceStatusEnum.PASS.value
                        and is_key_old):
                    self.findings["access_key_rotation"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                self.findings["access_key_rotation"]["details"].append({
                    "user_name": user_name,
                    "user_arn": user_arn,
                    "access_key_id": access_key_id,
                    "create_date": create_date.isoformat(),
                    "age_in_days": age_in_days,
                    "is_key_old": is_key_old,
                    "compliance": compliance
                })

                if is_key_old:
                    all_compliant = False

        if self.findings["access_key_rotation"]["status"] and not all_compliant:
            self.findings["access_key_rotation"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_no_root_user_access_key(self):
        """
        IAM root user access key should not exist.
        """
        # Retrieve account summary
        account_summary = await self.client.get_account_summary()
        root_access_key_count = account_summary.get("SummaryMap", {}).get("AccountAccessKeysPresent", 0)

        has_root_access_key = root_access_key_count > 0
        
        # Add compliance field - compliant if no root access key
        compliance = not has_root_access_key

        if (self.findings["no_root_user_access_key"]["status"] == ResourceComplianceStatusEnum.PASS.value
                and has_root_access_key):
            self.findings["no_root_user_access_key"]["status"] = ResourceComplianceStatusEnum.FAIL.value

        self.findings["no_root_user_access_key"]["details"].append({
            "has_root_access_key": has_root_access_key,
            "compliance": compliance
        })

    async def check_mfa_enabled_for_console_users(self):
        """
        MFA should be enabled for all IAM users that have a console password.
        """

        all_compliant = True

        for user in self.users.get("Users", []):
            user_name = user["UserName"]
            user_arn = user["Arn"]

            # Check if the user has a console password
            login_profile = None
            try:
                login_profile = await self.client.get_login_profile(UserName=user_name)
            except self.client.exceptions.NoSuchEntityException:
                pass  # User does not have a console password

            has_console_password = login_profile is not None

            # Check if MFA is enabled
            mfa_devices = await self.client.list_mfa_devices(UserName=user_name)
            has_mfa_enabled = len(mfa_devices.get("MFADevices", [])) > 0

            # Add compliance field - compliant if either no console password or has MFA enabled
            compliance = not has_console_password or has_mfa_enabled

            if has_console_password and not has_mfa_enabled:
                self.findings["mfa_enabled_for_console_users"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["mfa_enabled_for_console_users"]["details"].append({
                "user_name": user_name,
                "user_arn": user_arn,
                "has_console_password": has_console_password,
                "has_mfa_enabled": has_mfa_enabled,
                "compliance": compliance
            })

            if has_console_password and not has_mfa_enabled:
                all_compliant = False

        if self.findings["mfa_enabled_for_console_users"]["status"] and not all_compliant:
            self.findings["mfa_enabled_for_console_users"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_hardware_mfa_for_root_user(self):
        """
        Hardware MFA should be enabled for the root user.
        """
        # Retrieve MFA devices for the account
        mfa_devices = await self.client.list_mfa_devices()

        has_hardware_mfa = any(
            device["Type"] == "Hardware" and device["SerialNumber"].startswith("arn:aws:iam::")
            for device in mfa_devices.get("MFADevices", [])
        )
        
        # Add compliance field - compliant if hardware MFA is enabled
        compliance = has_hardware_mfa

        if (self.findings["hardware_mfa_for_root_user"]["status"] == ResourceComplianceStatusEnum.PASS.value
                and not has_hardware_mfa):
            self.findings["hardware_mfa_for_root_user"]["status"] = ResourceComplianceStatusEnum.FAIL.value

        self.findings["hardware_mfa_for_root_user"]["details"].append({
            "has_hardware_mfa": has_hardware_mfa,
            "compliance": compliance
        })

    async def check_strong_password_policies(self):
        """
        Password policies for IAM users should have strong configurations.
        """
        # Retrieve the account password policy
        try:
            password_policy = await self.client.get_account_password_policy()
            policy = password_policy.get("PasswordPolicy", {})

            # Define strong password policy criteria
            strong_policy_criteria = {
                "MinimumPasswordLength": 12,
                "RequireSymbols": True,
                "RequireNumbers": True,
                "RequireUppercaseCharacters": True,
                "RequireLowercaseCharacters": True,
                "AllowUsersToChangePassword": True,
                "ExpirePasswords": True
            }

            # Check if the policy meets the criteria
            is_strong_policy = all(
                policy.get(key, False) == value for key, value in strong_policy_criteria.items()
            )

        except self.client.exceptions.NoSuchEntityException:
            # No password policy exists
            is_strong_policy = False
        
        # Add compliance field - compliant if password policy is strong
        compliance = is_strong_policy

        if (self.findings["strong_password_policies"]["status"] == ResourceComplianceStatusEnum.PASS.value
                and not is_strong_policy):
            self.findings["strong_password_policies"]["status"] = ResourceComplianceStatusEnum.FAIL.value

        self.findings["strong_password_policies"]["details"].append({
            "is_strong_policy": is_strong_policy,
            "compliance": compliance
        })

    async def check_and_remove_unused_user_credentials(self):
        """
        Unused IAM user credentials should be removed.
        """
        import datetime
        all_compliant = True

        for user in self.users.get("Users", []):
            user_name = user["UserName"]
            user_arn = user["Arn"]

            # Check access keys
            access_keys = await self.client.list_access_keys(UserName=user_name)
            for access_key in access_keys.get("AccessKeyMetadata", []):
                access_key_id = access_key["AccessKeyId"]
                last_used_response = await self.client.get_access_key_last_used(AccessKeyId=access_key_id)
                last_used_date = last_used_response.get("AccessKeyLastUsed", {}).get("LastUsedDate")

                if last_used_date:
                    age_in_days = (datetime.datetime.now(datetime.timezone.utc) - last_used_date).days
                    is_unused = age_in_days > 90
                    
                    # Add compliance field - compliant if key is used within 90 days
                    compliance = not is_unused
                    
                    if is_unused:
                        self.findings["remove_unused_user_credentials"][
                            "status"] = ResourceComplianceStatusEnum.FAIL.value
                        self.findings["remove_unused_user_credentials"]["details"].append({
                            "user_name": user_name,
                            "user_arn": user_arn,
                            "access_key_id": access_key_id,
                            "last_used_date": last_used_date.isoformat(),
                            "age_in_days": age_in_days,
                            "compliance": compliance
                        })
                        all_compliant = False


            # Check console password
            try:
                password_last_used = user.get("PasswordLastUsed")
                if password_last_used:
                    age_in_days = (datetime.datetime.now(datetime.timezone.utc) - password_last_used).days
                    is_unused = age_in_days > 90
                    
                    # Add compliance field - compliant if password is used within 90 days
                    compliance = not is_unused
                    
                    if is_unused:
                        self.findings["remove_unused_user_credentials"][
                            "status"] = ResourceComplianceStatusEnum.FAIL.value
                        self.findings["remove_unused_user_credentials"]["details"].append({
                            "user_name": user_name,
                            "user_arn": user_arn,
                            "password_last_used": password_last_used.isoformat(),
                            "age_in_days": age_in_days,
                            "compliance": compliance
                        })
                        all_compliant = False
            except self.client.exceptions.NoSuchEntityException:
                pass  # User does not have a console password

        if self.findings["remove_unused_user_credentials"]["status"] and not all_compliant:
            self.findings["remove_unused_user_credentials"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    def get_check_functions(self):
        return [
            self.check_no_full_admin_privileges,
            self.check_no_iam_policies_for_users,
            self.check_no_wildcard_actions_in_custom_policies,
            self.check_access_key_rotation,
            self.check_no_root_user_access_key,
            self.check_mfa_enabled_for_console_users,
            self.check_hardware_mfa_for_root_user,
            self.check_strong_password_policies,
            self.check_and_remove_unused_user_credentials
        ]

    async def run_checks(self):
        session = self.get_session()
        async with session.client(AWSServiceNameEnum.IAM.value) as client:
            self.client = client
            self.policies = await self.client.list_policies(Scope='Local')
            self.users = await self.client.list_users()

            for check_function in self.get_check_functions():
                await check_function()

        return self.findings
