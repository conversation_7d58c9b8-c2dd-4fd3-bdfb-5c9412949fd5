import json
from celery.utils.log import get_task_logger
from ..config import BaseRemediationProcessor
from app.common import AWSServiceNameEnum, AWSRegionNameEnum, ResourceComplianceStatusEnum

__all__ = ['RemediationProcessor']

logger = get_task_logger(__name__)


class RemediationProcessor(BaseRemediationProcessor):
    """
    Processor for remediating ELB compliance issues.
    Inherits from BaseRemediationProcessor to reuse AWS session management.
    """

    def __init__(self, credentials, regions=[]):
        super().__init__(credentials, regions)
        self.remediation_results = {
            "http_to_https_redirect": {
                "status": False,
                "message": "",
                "field_updates": {"redirect_to_https": True}
            },
            "classic_load_balancer_az_span": {
                "status": False,
                "message": "",
                "field_updates": {"spans_multiple_azs": True, "availability_zones": []}
            },
            "alb_desync_mitigation_mode": {
                "status": False,
                "message": "",
                "field_updates": {"desync_mitigation_mode": "defensive"}
            },
            "load_balancers_az_span": {
                "status": False,
                "message": "",
                "field_updates": {"spans_multiple_azs": True, "availability_zones": []}
            },
            "classic_load_balancer_desync_mitigation_mode": {
                "status": False,
                "message": "",
                "field_updates": {"desync_mitigation_mode": "defensive"}
            },
            "classic_load_balancer_secure_listener_configuration": {
                "status": False,
                "message": "",
                "field_updates": {
                    "has_https_listener": True,
                    "uses_acm_certificate": True
                }
            },
            "alb_drop_invalid_http_headers": {
                "status": False,
                "message": "",
                "field_updates": {"drop_invalid_headers_enabled": True}
            },
            "load_balancers_logging_enabled": {
                "status": False,
                "message": "",
                "field_updates": {"logging_enabled": True}
            },
            "load_balancers_deletion_protection": {
                "status": False,
                "message": "",
                "field_updates": {"deletion_protection_enabled": True}
            },
            "classic_load_balancer_connection_draining": {
                "status": False,
                "message": "",
                "field_updates": {"connection_draining_enabled": True}
            },
            "classic_load_balancer_ssl_security_policy": {
                "status": False,
                "message": "",
                "field_updates": {"uses_strong_policy": True}
            },
            "classic_load_balancer_cross_zone_load_balancing": {
                "status": False,
                "message": "",
                "field_updates": {"cross_zone_load_balancing_enabled": True}
            }
        }

    async def remediate_http_to_https_redirect(self, details):
        """
        Remediate ALB by configuring HTTP to HTTPS redirect.
        """
        try:
            load_balancer_arn = details.get("load_balancer_arn", "")
            region = details.get("region", "")

            if not load_balancer_arn or not region:
                return False, "Missing load balancer ARN or region"

            # Pre-check: Verify if remediation is needed
            listeners = await self.client.describe_listeners(
                LoadBalancerArn=load_balancer_arn
            )

            redirect_to_https = True
            http_listeners = []

            for listener in listeners.get("Listeners", []):
                if listener["Protocol"] == "HTTP":
                    http_listeners.append(listener)
                    has_https_redirect = False
                    for action in listener.get("DefaultActions", []):
                        if action["Type"] == "redirect" and action["RedirectConfig"].get("Protocol") == "HTTPS":
                            has_https_redirect = True
                            break
                    if not has_https_redirect:
                        redirect_to_https = False

            if redirect_to_https:
                return True, "HTTP to HTTPS redirect is already configured"

            if not http_listeners:
                return True, "No HTTP listeners found that need to be redirected to HTTPS"

            # Find or create an HTTPS listener
            https_listeners = [listener for listener in listeners.get('Listeners', [])
                               if listener.get('Protocol') == 'HTTPS']

            https_listener_port = 443
            if https_listeners:
                https_listener_port = https_listeners[0]['Port']
            else:
                # We need to create an HTTPS listener
                # First, check if we have a certificate
                session = self.get_session()
                async with session.client('acm', region_name=region) as acm_client:
                    certificates = await acm_client.list_certificates(
                        CertificateStatuses=['ISSUED']
                    )

                    if not certificates.get('CertificateSummaryList'):
                        return False, "No SSL/TLS certificates found in ACM. Please create a certificate first."

                    # Use the first available certificate
                    certificate_arn = certificates['CertificateSummaryList'][0]['CertificateArn']

                # Get the VPC ID for the load balancer to find a target group
                load_balancer = await self.client.describe_load_balancers(
                    LoadBalancerArns=[load_balancer_arn]
                )

                vpc_id = load_balancer['LoadBalancers'][0]['VpcId']

                # Find or create a target group
                target_groups = await self.client.describe_target_groups(
                    LoadBalancerArn=load_balancer_arn
                )

                target_group_arn = None
                if target_groups.get('TargetGroups'):
                    target_group_arn = target_groups['TargetGroups'][0]['TargetGroupArn']
                else:
                    # Create a new target group
                    target_group = await self.client.create_target_group(
                        Name=f"default-tg-{load_balancer_arn.split('/')[-1]}",
                        Protocol='HTTP',
                        Port=80,
                        VpcId=vpc_id,
                        TargetType='instance',
                        HealthCheckProtocol='HTTP',
                        HealthCheckPath='/',
                        HealthCheckEnabled=True
                    )
                    target_group_arn = target_group['TargetGroups'][0]['TargetGroupArn']

                # Create the HTTPS listener
                await self.client.create_listener(
                    LoadBalancerArn=load_balancer_arn,
                    Protocol='HTTPS',
                    Port=https_listener_port,
                    Certificates=[
                        {
                            'CertificateArn': certificate_arn
                        }
                    ],
                    SslPolicy='ELBSecurityPolicy-TLS-1-2-2017-01',
                    DefaultActions=[
                        {
                            'Type': 'forward',
                            'TargetGroupArn': target_group_arn
                        }
                    ]
                )

            # Update all HTTP listeners to redirect to HTTPS
            for http_listener in http_listeners:
                await self.client.modify_listener(
                    ListenerArn=http_listener['ListenerArn'],
                    Port=http_listener['Port'],
                    Protocol='HTTP',
                    DefaultActions=[
                        {
                            'Type': 'redirect',
                            'RedirectConfig': {
                                'Protocol': 'HTTPS',
                                'Port': str(https_listener_port),
                                'StatusCode': 'HTTP_301'
                            }
                        }
                    ]
                )

            logger.info(f"Successfully configured HTTP to HTTPS redirect for load balancer {load_balancer_arn}")
            return True, f"Successfully configured HTTP to HTTPS redirect for load balancer {load_balancer_arn}"

        except Exception as e:
            error_msg = f"Failed to configure HTTP to HTTPS redirect: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_classic_load_balancer_az_span(self, details):
        """
        Remediate Classic Load Balancer by enabling multiple Availability Zones.
        """
        try:
            load_balancer_name = details.get("load_balancer_name", "")
            region = details.get("region", "")

            if not load_balancer_name or not region:
                return False, "Missing load balancer name or region"

            # Pre-check: Verify if remediation is needed
            session = self.get_session()
            async with session.client('elb', region_name=region) as elb_client:
                lb_info = await elb_client.describe_load_balancers(
                    LoadBalancerNames=[load_balancer_name]
                )

                if not lb_info.get("LoadBalancerDescriptions"):
                    return False, f"Load balancer {load_balancer_name} not found"

                lb = lb_info["LoadBalancerDescriptions"][0]
                current_azs = lb.get("AvailabilityZones", [])

                if len(current_azs) > 1:
                    # Update the field_updates with actual values
                    self.remediation_results["classic_load_balancer_az_span"]["field_updates"][
                        "availability_zones"] = current_azs
                    return True, f"Load balancer {load_balancer_name} already spans multiple AZs ({len(current_azs)})"

                # Get all available AZs in the region
                async with session.client('ec2', region_name=region) as ec2_client:
                    az_response = await ec2_client.describe_availability_zones(
                        Filters=[
                            {
                                'Name': 'region-name',
                                'Values': [region]
                            },
                            {
                                'Name': 'state',
                                'Values': ['available']
                            }
                        ]
                    )

                    available_azs = [az['ZoneName'] for az in az_response.get('AvailabilityZones', [])]

                    if len(available_azs) <= 1:
                        return False, f"Region {region} does not have multiple available AZs"

                    # Filter out the AZ that's already enabled
                    azs_to_enable = [az for az in available_azs if az not in current_azs]

                    if not azs_to_enable:
                        return False, "No additional AZs available to enable"

                    # Enable at least one more AZ (preferably two if available)
                    azs_to_add = azs_to_enable[:2] if len(azs_to_enable) > 1 else azs_to_enable

                    # Enable the additional AZs
                    await elb_client.enable_availability_zones_for_load_balancer(
                        LoadBalancerName=load_balancer_name,
                        AvailabilityZones=azs_to_add
                    )

                    # update the availability_zones field with all AZs (current + new)
                    all_azs = current_azs + azs_to_add
                    self.remediation_results["classic_load_balancer_az_span"]["field_updates"][
                        "availability_zones"] = all_azs

            logger.info(f"Successfully enabled multiple AZs for Classic Load Balancer {load_balancer_name}")
            return True, f"Successfully enabled multiple AZs for Classic Load Balancer {load_balancer_name}"

        except Exception as e:
            error_msg = f"Failed to enable multiple AZs: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_alb_desync_mitigation_mode(self, details):
        """
        Remediate ALB by configuring defensive or strictest desync mitigation mode.
        """
        try:
            load_balancer_arn = details.get("load_balancer_arn", "")
            region = details.get("region", "")

            if not load_balancer_arn or not region:
                return False, "Missing load balancer ARN or region"

            # Pre-check: Verify if remediation is needed
            attributes = await self.client.describe_load_balancer_attributes(
                LoadBalancerArn=load_balancer_arn
            )

            desync_mitigation_mode = next(
                (attr["Value"] for attr in attributes.get("Attributes", [])
                 if attr["Key"] == "routing.http.desync_mitigation_mode"),
                None
            )

            if desync_mitigation_mode in ["defensive", "strictest"]:
                return True, f"Desync mitigation mode is already set to {desync_mitigation_mode}"

            # Apply remediation
            await self.client.modify_load_balancer_attributes(
                LoadBalancerArn=load_balancer_arn,
                Attributes=[
                    {
                        'Key': 'routing.http.desync_mitigation_mode',
                        'Value': 'defensive'
                    }
                ]
            )

            logger.info(f"Successfully set desync mitigation mode to defensive for ALB {load_balancer_arn}")
            return True, f"Successfully set desync mitigation mode to defensive for ALB {load_balancer_arn}"

        except Exception as e:
            error_msg = f"Failed to set desync mitigation mode: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_load_balancers_az_span(self, details):
        """
        Remediate Application/Network Load Balancer by enabling multiple Availability Zones.
        """
        try:
            load_balancer_arn = details.get("load_balancer_arn", "")
            region = details.get("region", "")

            if not load_balancer_arn or not region:
                return False, "Missing load balancer ARN or region"

            # Pre-check: Verify if remediation is needed
            lb_info = await self.client.describe_load_balancers(
                LoadBalancerArns=[load_balancer_arn]
            )

            if not lb_info.get("LoadBalancers"):
                return False, f"Load balancer with ARN {load_balancer_arn} not found"

            lb = lb_info["LoadBalancers"][0]
            current_azs = [az_mapping["ZoneName"] for az_mapping in lb.get("AvailabilityZones", [])]

            if len(current_azs) > 1:
                # Update the field_updates with actual values
                self.remediation_results["load_balancers_az_span"]["field_updates"][
                    "availability_zones"] = current_azs
                return True, f"Load balancer already spans multiple AZs ({len(current_azs)})"

            # Get all available AZs in the region
            session = self.get_session()
            async with session.client('ec2', region_name=region) as ec2_client:
                az_response = await ec2_client.describe_availability_zones(
                    Filters=[
                        {
                            'Name': 'region-name',
                            'Values': [region]
                        },
                        {
                            'Name': 'state',
                            'Values': ['available']
                        }
                    ]
                )

                available_azs = [az['ZoneName'] for az in az_response.get('AvailabilityZones', [])]

                if len(available_azs) <= 1:
                    return False, f"Region {region} does not have multiple available AZs"

                # Filter out the AZ that's already enabled
                azs_to_enable = [az for az in available_azs if az not in current_azs]

                if not azs_to_enable:
                    return True, "No additional AZs available to enable"

                # For ALB/NLB, we need to add subnets in the additional AZs
                vpc_id = lb.get("VpcId")

                if not vpc_id:
                    return False, "Could not determine VPC ID for the load balancer"

                # Get subnets in the VPC for the additional AZs
                subnet_response = await ec2_client.describe_subnets(
                    Filters=[
                        {
                            'Name': 'vpc-id',
                            'Values': [vpc_id]
                        },
                        {
                            'Name': 'availability-zone',
                            'Values': azs_to_enable
                        }
                    ]
                )

                subnets = subnet_response.get('Subnets', [])

                if not subnets:
                    return False, f"No subnets found in the additional AZs for VPC {vpc_id}"

                # Get current subnets
                current_subnets = [az_mapping["SubnetId"] for az_mapping in lb.get("AvailabilityZones", [])]

                # Add subnets from additional AZs
                new_subnets = [subnet['SubnetId'] for subnet in subnets]
                all_subnets = current_subnets + new_subnets

                # Update the load balancer with the additional subnets
                await self.client.set_subnets(
                    LoadBalancerArn=load_balancer_arn,
                    Subnets=all_subnets
                )

                # Update the availability_zones field with all AZs (current + new)
                # We'll get the updated AZ list after adding subnets
                updated_lb_info = await self.client.describe_load_balancers(
                    LoadBalancerArns=[load_balancer_arn]
                )
                updated_lb = updated_lb_info["LoadBalancers"][0]
                updated_azs = [az_mapping["ZoneName"] for az_mapping in updated_lb.get("AvailabilityZones", [])]

                self.remediation_results["load_balancers_az_span"]["field_updates"][
                    "availability_zones"] = updated_azs

            logger.info(f"Successfully enabled multiple AZs for load balancer {load_balancer_arn}")
            return True, f"Successfully enabled multiple AZs for load balancer {load_balancer_arn}"

        except Exception as e:
            error_msg = f"Failed to enable multiple AZs: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_classic_load_balancer_desync_mitigation_mode(self, details):
        """
        Remediate Classic Load Balancer by configuring defensive desync mitigation mode.
        """
        try:
            load_balancer_name = details.get("load_balancer_name", "")
            region = details.get("region", "")

            if not load_balancer_name or not region:
                return False, "Missing load balancer name or region"

            # Pre-check: Verify if remediation is needed
            session = self.get_session()
            async with session.client('elb', region_name=region) as elb_client:
                # Check current desync mitigation mode
                attributes = await elb_client.describe_load_balancer_attributes(
                    LoadBalancerName=load_balancer_name
                )

                desync_mitigation_mode = next(
                    (attr["Value"] for attr in attributes.get("LoadBalancerAttributes", [])
                     if attr["Key"] == "routing.http.desync_mitigation_mode"),
                    None
                )

                if desync_mitigation_mode in ["defensive", "strictest"]:
                    return True, f"Desync mitigation mode is already set to {desync_mitigation_mode}"

                # Apply remediation - set desync mitigation mode to defensive
                try:
                    await elb_client.modify_load_balancer_attributes(
                        LoadBalancerName=load_balancer_name,
                        LoadBalancerAttributes={
                            'AdditionalAttributes': [
                                {
                                    'Key': 'routing.http.desync_mitigation_mode',
                                    'Value': 'defensive'
                                }
                            ]
                        }
                    )

                    logger.info(
                        f"Successfully set desync mitigation mode to defensive for Classic Load Balancer {load_balancer_name}")
                    return True, f"Successfully set desync mitigation mode to defensive for Classic Load Balancer {load_balancer_name}"

                except Exception as e:
                    # If setting the attribute fails, it might not be supported for this Classic Load Balancer
                    # In that case, we'll apply other security measures as a best practice
                    logger.warning(f"Could not set desync mitigation mode directly: {str(e)}")

                    # Apply a strong SSL policy as an alternative security measure
                    lb_info = await elb_client.describe_load_balancers(
                        LoadBalancerNames=[load_balancer_name]
                    )

                    if not lb_info.get("LoadBalancerDescriptions"):
                        return False, f"Load balancer {load_balancer_name} not found"

                    lb = lb_info["LoadBalancerDescriptions"][0]
                    https_listeners = []

                    for listener_desc in lb.get("ListenerDescriptions", []):
                        listener = listener_desc["Listener"]
                        if listener["Protocol"] in ["HTTPS", "SSL"]:
                            https_listeners.append({
                                "port": listener["LoadBalancerPort"],
                                "protocol": listener["Protocol"]
                            })

                    if not https_listeners:
                        return False, "No HTTPS/SSL listeners found and cannot set desync mitigation mode directly"

                    # Apply a strong SSL policy to all HTTPS/SSL listeners
                    for listener in https_listeners:
                        await elb_client.set_load_balancer_policies_of_listener(
                            LoadBalancerName=load_balancer_name,
                            LoadBalancerPort=listener["port"],
                            PolicyNames=["ELBSecurityPolicy-TLS-1-2-2017-01"]  # Strong security policy
                        )

                    logger.info(
                        f"Applied strong SSL policy as an alternative security measure for {load_balancer_name}")
                    return True, f"Applied strong SSL policy as an alternative security measure for {load_balancer_name}"

        except Exception as e:
            error_msg = f"Failed to set desync mitigation mode: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_classic_load_balancer_secure_listener_configuration(self, details):
        """
        Remediate Classic Load Balancer by ensuring:
        1. HTTPS/SSL listeners exist
        2. All HTTPS/SSL listeners use ACM certificates
        3. Strong SSL policies are applied
        """
        try:
            load_balancer_name = details.get("load_balancer_name", "")
            region = details.get("region", "")

            if not load_balancer_name or not region:
                return False, "Missing load balancer name or region"

            # Pre-check: Get current load balancer configuration
            session = self.get_session()
            async with session.client('elb', region_name=region) as elb_client:
                lb_info = await elb_client.describe_load_balancers(
                    LoadBalancerNames=[load_balancer_name]
                )

                if not lb_info.get("LoadBalancerDescriptions"):
                    return False, f"Load balancer {load_balancer_name} not found"

                lb = lb_info["LoadBalancerDescriptions"][0]
                https_listeners = []
                http_listeners = []

                for listener_desc in lb.get("ListenerDescriptions", []):
                    listener = listener_desc["Listener"]
                    if listener["Protocol"] in ["HTTPS", "SSL"]:
                        https_listeners.append(listener)
                    elif listener["Protocol"] in ["HTTP", "TCP"]:
                        http_listeners.append(listener)

                # Check if we need to add HTTPS listeners
                needs_https_listener = len(https_listeners) == 0

                # Check if any HTTPS listeners need ACM certificate updates
                needs_acm_update = False
                for listener in https_listeners:
                    cert_id = listener.get("SSLCertificateId", "")
                    if not cert_id.startswith("arn:aws:acm:"):
                        needs_acm_update = True
                        break

                # If everything is already configured correctly, return success
                if not needs_https_listener and not needs_acm_update:
                    return True, "Load balancer already has HTTPS listeners with ACM certificates"

                # Get an ACM certificate if we need to add or update HTTPS listeners
                certificate_arn = None
                if needs_https_listener or needs_acm_update:
                    async with session.client('acm', region_name=region) as acm_client:
                        certificates = await acm_client.list_certificates(
                            CertificateStatuses=['ISSUED']
                        )

                        if not certificates.get('CertificateSummaryList'):
                            return False, "No SSL/TLS certificates found in ACM. Please create a certificate first."

                        # Use the first available certificate
                        certificate_arn = certificates['CertificateSummaryList'][0]['CertificateArn']

                # 1. Add HTTPS listener if needed
                if needs_https_listener:
                    # Use the first HTTP listener's instance port as the backend for our new HTTPS listener
                    instance_port = 80
                    instance_protocol = "HTTP"

                    if http_listeners:
                        instance_port = http_listeners[0]["InstancePort"]
                        instance_protocol = http_listeners[0]["InstanceProtocol"]

                    # Create the HTTPS listener
                    await elb_client.create_load_balancer_listeners(
                        LoadBalancerName=load_balancer_name,
                        Listeners=[
                            {
                                'Protocol': 'HTTPS',
                                'LoadBalancerPort': 443,
                                'InstanceProtocol': instance_protocol,
                                'InstancePort': instance_port,
                                'SSLCertificateId': certificate_arn
                            }
                        ]
                    )

                    # Apply a strong SSL policy
                    policy_name = f"ELBSecurityPolicy-TLS-1-2-2017-01-{load_balancer_name}-443"

                    await elb_client.create_load_balancer_policy(
                        LoadBalancerName=load_balancer_name,
                        PolicyName=policy_name,
                        PolicyTypeName="SSLNegotiationPolicyType",
                        PolicyAttributes=[
                            {"AttributeName": "Reference-Security-Policy",
                             "AttributeValue": "ELBSecurityPolicy-TLS-1-2-2017-01"}
                        ]
                    )

                    await elb_client.set_load_balancer_policies_of_listener(
                        LoadBalancerName=load_balancer_name,
                        LoadBalancerPort=443,
                        PolicyNames=[policy_name]
                    )

                    logger.info(f"Added HTTPS listener to Classic Load Balancer {load_balancer_name}")

                # 2. Update existing HTTPS listeners to use ACM certificates if needed
                if needs_acm_update:
                    for listener in https_listeners:
                        cert_id = listener.get("SSLCertificateId", "")
                        if not cert_id.startswith("arn:aws:acm:"):
                            port = listener["LoadBalancerPort"]
                            instance_port = listener["InstancePort"]
                            instance_protocol = listener["InstanceProtocol"]
                            protocol = listener["Protocol"]

                            # Delete the old listener
                            await elb_client.delete_load_balancer_listeners(
                                LoadBalancerName=load_balancer_name,
                                LoadBalancerPorts=[port]
                            )

                            # Create a new listener with the ACM certificate
                            await elb_client.create_load_balancer_listeners(
                                LoadBalancerName=load_balancer_name,
                                Listeners=[
                                    {
                                        'Protocol': protocol,
                                        'LoadBalancerPort': port,
                                        'InstanceProtocol': instance_protocol,
                                        'InstancePort': instance_port,
                                        'SSLCertificateId': certificate_arn
                                    }
                                ]
                            )

                            # Apply a strong SSL policy
                            policy_name = f"ELBSecurityPolicy-TLS-1-2-2017-01-{load_balancer_name}-{port}"

                            await elb_client.create_load_balancer_policy(
                                LoadBalancerName=load_balancer_name,
                                PolicyName=policy_name,
                                PolicyTypeName="SSLNegotiationPolicyType",
                                PolicyAttributes=[
                                    {"AttributeName": "Reference-Security-Policy",
                                     "AttributeValue": "ELBSecurityPolicy-TLS-1-2-2017-01"}
                                ]
                            )

                            await elb_client.set_load_balancer_policies_of_listener(
                                LoadBalancerName=load_balancer_name,
                                LoadBalancerPort=port,
                                PolicyNames=[policy_name]
                            )

                            logger.info(
                                f"Updated listener on port {port} to use ACM certificate for Classic Load Balancer {load_balancer_name}")

                return True, f"Successfully configured secure listeners for Classic Load Balancer {load_balancer_name}"

        except Exception as e:
            error_msg = f"Failed to configure secure listeners: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_alb_drop_invalid_http_headers(self, details):
        """
        Remediate ALB by enabling dropping of invalid HTTP headers.
        """
        try:
            load_balancer_arn = details.get("load_balancer_arn", "")
            region = details.get("region", "")

            if not load_balancer_arn or not region:
                return False, "Missing load balancer ARN or region"

            # Pre-check: Verify if remediation is needed
            attributes = await self.client.describe_load_balancer_attributes(
                LoadBalancerArn=load_balancer_arn
            )

            drop_invalid_headers_enabled = next(
                (attr["Value"] == "true" for attr in attributes.get("Attributes", [])
                 if attr["Key"] == "routing.http.drop_invalid_header_fields.enabled"),
                False
            )

            if drop_invalid_headers_enabled:
                return True, "Dropping of invalid HTTP headers is already enabled"

            # Check if this is an Application Load Balancer
            load_balancer_type = self._get_load_balancer_type(load_balancer_arn)
            if load_balancer_type != 'application':
                return False, "This remediation only applies to Application Load Balancers"

            # Apply remediation
            await self.client.modify_load_balancer_attributes(
                LoadBalancerArn=load_balancer_arn,
                Attributes=[
                    {
                        'Key': 'routing.http.drop_invalid_header_fields.enabled',
                        'Value': 'true'
                    }
                ]
            )

            logger.info(f"Successfully enabled dropping of invalid HTTP headers for ALB {load_balancer_arn}")
            return True, f"Successfully enabled dropping of invalid HTTP headers for ALB {load_balancer_arn}"

        except Exception as e:
            error_msg = f"Failed to enable dropping of invalid HTTP headers: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_load_balancers_logging_enabled(self, details):
        """
        Remediate load balancer by enabling access logs.
        """
        try:
            load_balancer_arn = details.get("load_balancer_arn", "")
            load_balancer_name = details.get("load_balancer_name", "")
            region = details.get("region", "")
            lb_type = details.get("type", "")

            if not region:
                return False, "Missing region"

            if lb_type == "classic" and not load_balancer_name:
                return False, "Missing load balancer name for Classic Load Balancer"

            if lb_type != "classic" and not load_balancer_arn:
                return False, "Missing load balancer ARN for Application/Network Load Balancer"

            # Pre-check: Verify if logging is already enabled
            session = self.get_session()

            if lb_type == "classic":
                async with session.client('elb', region_name=region) as elb_client:
                    attributes = await elb_client.describe_load_balancer_attributes(
                        LoadBalancerName=load_balancer_name
                    )
                    logging_enabled = attributes.get("LoadBalancerAttributes", {}).get("AccessLog", {}).get("Enabled",
                                                                                                            False)

                    if logging_enabled:
                        return True, f"Access logs are already enabled for Classic Load Balancer {load_balancer_name}"
            else:
                # For Application or Network Load Balancers
                async with session.client('elbv2', region_name=region) as elbv2_client:
                    attributes = await elbv2_client.describe_load_balancer_attributes(
                        LoadBalancerArn=load_balancer_arn
                    )

                    logging_enabled = next(
                        (attr["Value"] == "true" for attr in attributes.get("Attributes", [])
                         if attr["Key"] == "access_logs.s3.enabled"),
                        False
                    )

                    if logging_enabled:
                        return True, f"Access logs are already enabled for load balancer {load_balancer_arn}"

            # Create an S3 bucket for access logs if needed
            async with session.client('s3', region_name=region) as s3_client:
                bucket_name = f"elb-access-logs-{self.credentials['aws_account_id']}-{region}"

                # Check if bucket exists
                try:
                    await s3_client.head_bucket(Bucket=bucket_name)
                    bucket_exists = True
                except:
                    bucket_exists = False

                if not bucket_exists:
                    # Create the bucket
                    if region == 'us-east-1':
                        await s3_client.create_bucket(
                            Bucket=bucket_name
                        )
                    else:
                        await s3_client.create_bucket(
                            Bucket=bucket_name,
                            CreateBucketConfiguration={
                                'LocationConstraint': region
                            }
                        )

                    # Set bucket policy to allow ELB to write logs
                    # Get the AWS ELB logging service principal for this region
                    elb_principal = self._get_elb_logging_principal(region)

                    bucket_policy = {
                        "Version": "2012-10-17",
                        "Statement": [
                            {
                                "Effect": "Allow",
                                "Principal": {
                                    "Service": elb_principal
                                },
                                "Action": "s3:PutObject",
                                "Resource": f"arn:aws:s3:::{bucket_name}/AWSLogs/{self.credentials['aws_account_id']}/*"
                            }
                        ]
                    }

                    await s3_client.put_bucket_policy(
                        Bucket=bucket_name,
                        Policy=json.dumps(bucket_policy)
                    )

            # Apply remediation based on load balancer type
            if lb_type == "classic":
                async with session.client('elb', region_name=region) as elb_client:
                    # Apply remediation for Classic Load Balancer
                    await elb_client.modify_load_balancer_attributes(
                        LoadBalancerName=load_balancer_name,
                        LoadBalancerAttributes={
                            'AccessLog': {
                                'Enabled': True,
                                'S3BucketName': bucket_name,
                                'EmitInterval': 60,
                                'S3BucketPrefix': load_balancer_name or 'elb-logs'
                            }
                        }
                    )
            else:
                # For Application or Network Load Balancers
                async with session.client('elbv2', region_name=region) as elbv2_client:
                    # Apply remediation for ALB/NLB
                    prefix = load_balancer_name or (f"alb-logs" if lb_type == "application" else "nlb-logs")

                    await elbv2_client.modify_load_balancer_attributes(
                        LoadBalancerArn=load_balancer_arn,
                        Attributes=[
                            {
                                'Key': 'access_logs.s3.enabled',
                                'Value': 'true'
                            },
                            {
                                'Key': 'access_logs.s3.bucket',
                                'Value': bucket_name
                            },
                            {
                                'Key': 'access_logs.s3.prefix',
                                'Value': prefix
                            }
                        ]
                    )

            logger.info(f"Successfully enabled access logs for load balancer {load_balancer_name or load_balancer_arn}")
            return True, f"Successfully enabled access logs for load balancer {load_balancer_name or load_balancer_arn}"

        except Exception as e:
            error_msg = f"Failed to enable access logs: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_load_balancers_deletion_protection(self, details):
        """
        Remediate load balancer by enabling deletion protection.
        """
        try:
            load_balancer_arn = details.get("load_balancer_arn", "")
            region = details.get("region", "")

            if not load_balancer_arn or not region:
                return False, "Missing load balancer ARN or region"

            # Pre-check: Verify if remediation is needed
            attributes = await self.client.describe_load_balancer_attributes(
                LoadBalancerArn=load_balancer_arn
            )

            deletion_protection_enabled = next(
                (attr["Value"] == "true" for attr in attributes.get("Attributes", [])
                 if attr["Key"] == "deletion_protection.enabled"),
                False
            )

            if deletion_protection_enabled:
                return True, "Deletion protection is already enabled"

            # Apply remediation
            await self.client.modify_load_balancer_attributes(
                LoadBalancerArn=load_balancer_arn,
                Attributes=[
                    {
                        'Key': 'deletion_protection.enabled',
                        'Value': 'true'
                    }
                ]
            )

            logger.info(f"Successfully enabled deletion protection for load balancer {load_balancer_arn}")
            return True, f"Successfully enabled deletion protection for load balancer {load_balancer_arn}"

        except Exception as e:
            error_msg = f"Failed to enable deletion protection: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_classic_load_balancer_connection_draining(self, details):
        """
        Remediate Classic Load Balancer by enabling connection draining.
        """
        try:
            load_balancer_name = details.get("load_balancer_name", "")
            region = details.get("region", "")

            if not load_balancer_name or not region:
                return False, "Missing load balancer name or region"

            # Pre-check: Verify if remediation is needed
            session = self.get_session()
            async with session.client('elb', region_name=region) as elb_client:
                attributes = await elb_client.describe_load_balancer_attributes(
                    LoadBalancerName=load_balancer_name
                )

                connection_draining_enabled = attributes.get("LoadBalancerAttributes", {}).get(
                    "ConnectionDraining", {}).get("Enabled", False)

                if connection_draining_enabled:
                    return True, "Connection draining is already enabled"

                # Apply remediation
                await elb_client.modify_load_balancer_attributes(
                    LoadBalancerName=load_balancer_name,
                    LoadBalancerAttributes={
                        'ConnectionDraining': {
                            'Enabled': True,
                            'Timeout': 300  # Default timeout in seconds
                        }
                    }
                )

            logger.info(f"Successfully enabled connection draining for Classic Load Balancer {load_balancer_name}")
            return True, f"Successfully enabled connection draining for Classic Load Balancer {load_balancer_name}"

        except Exception as e:
            error_msg = f"Failed to enable connection draining: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_classic_load_balancer_ssl_security_policy(self, details):
        """
        Remediate Classic Load Balancer by applying a strong SSL security policy.
        """
        try:
            load_balancer_name = details.get("load_balancer_name", "")
            region = details.get("region", "")

            if not load_balancer_name or not region:
                return False, "Missing load balancer name or region"

            # Pre-check: Verify if remediation is needed
            session = self.get_session()
            async with session.client('elb', region_name=region) as elb_client:
                # Get the load balancer details
                lb_info = await elb_client.describe_load_balancers(
                    LoadBalancerNames=[load_balancer_name]
                )

                if not lb_info.get("LoadBalancerDescriptions"):
                    return False, f"Load balancer {load_balancer_name} not found"

                # Find all SSL/HTTPS listeners
                ssl_listeners = []
                for lb in lb_info.get("LoadBalancerDescriptions", []):
                    for listener_desc in lb.get("ListenerDescriptions", []):
                        listener = listener_desc["Listener"]
                        if listener["Protocol"] in ["HTTPS", "SSL"]:
                            # Get the current policies for this listener
                            current_policies = listener_desc.get("PolicyNames", [])
                            ssl_listeners.append({
                                "port": listener["LoadBalancerPort"],
                                "protocol": listener["Protocol"],
                                "current_policies": current_policies
                            })

                if not ssl_listeners:
                    return True, "No SSL/HTTPS listeners found that need policy updates"

                # Get all policies for this load balancer
                all_policies = await elb_client.describe_load_balancer_policies(
                    LoadBalancerName=load_balancer_name
                )

                # Track if any changes were made
                changes_made = False

                # Apply remediation for each SSL listener
                for listener in ssl_listeners:
                    port = listener["port"]
                    current_policies = listener["current_policies"]

                    # Check if the listener already has a strong policy
                    has_strong_policy = False
                    for policy_name in current_policies:
                        # Get policy details
                        policy_details = next(
                            (p for p in all_policies.get("PolicyDescriptions", []) if p["PolicyName"] == policy_name),
                            None
                        )

                        if policy_details and policy_details.get("PolicyTypeName") == "SSLNegotiationPolicyType":
                            # Check if it's a strong policy
                            for attr in policy_details.get("PolicyAttributeDescriptions", []):
                                if (attr["AttributeName"] == "Reference-Security-Policy" and
                                        attr["AttributeValue"].startswith("ELBSecurityPolicy-TLS-1-2")):
                                    has_strong_policy = True
                                    break

                        if has_strong_policy:
                            break

                    if has_strong_policy:
                        logger.info(f"Listener on port {port} already has a strong SSL policy")
                        continue

                    # Create a new security policy
                    policy_name = f"ELBSecurityPolicy-TLS-1-2-2017-01-{load_balancer_name}-{port}"

                    # Create the policy
                    await elb_client.create_load_balancer_policy(
                        LoadBalancerName=load_balancer_name,
                        PolicyName=policy_name,
                        PolicyTypeName="SSLNegotiationPolicyType",
                        PolicyAttributes=[
                            {"AttributeName": "Reference-Security-Policy",
                             "AttributeValue": "ELBSecurityPolicy-TLS-1-2-2017-01"}
                        ]
                    )

                    # Set the policy for the listener
                    await elb_client.set_load_balancer_policies_of_listener(
                        LoadBalancerName=load_balancer_name,
                        LoadBalancerPort=port,
                        PolicyNames=[policy_name]
                    )

                    changes_made = True
                    logger.info(f"Applied strong SSL policy to listener on port {port}")

                if changes_made:
                    logger.info(
                        f"Successfully applied strong SSL security policy for Classic Load Balancer {load_balancer_name}")
                    return True, f"Successfully applied strong SSL security policy for Classic Load Balancer {load_balancer_name}"
                else:
                    return True, f"All SSL/HTTPS listeners already have strong SSL policies"

        except Exception as e:
            error_msg = f"Failed to apply SSL security policy: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_classic_load_balancer_cross_zone_load_balancing(self, details):
        """
        Remediate Classic Load Balancer by enabling cross-zone load balancing.
        """
        try:
            load_balancer_name = details.get("load_balancer_name", "")
            region = details.get("region", "")

            if not load_balancer_name or not region:
                return False, "Missing load balancer name or region"

            # Pre-check: Verify if remediation is needed
            session = self.get_session()
            async with session.client('elb', region_name=region) as elb_client:
                attributes = await elb_client.describe_load_balancer_attributes(
                    LoadBalancerName=load_balancer_name
                )

                cross_zone_load_balancing_enabled = attributes.get("LoadBalancerAttributes", {}).get(
                    "CrossZoneLoadBalancing", {}).get("Enabled", False)

                if cross_zone_load_balancing_enabled:
                    return True, "Cross-zone load balancing is already enabled"

                # Apply remediation
                await elb_client.modify_load_balancer_attributes(
                    LoadBalancerName=load_balancer_name,
                    LoadBalancerAttributes={
                        'CrossZoneLoadBalancing': {
                            'Enabled': True
                        }
                    }
                )

            logger.info(
                f"Successfully enabled cross-zone load balancing for Classic Load Balancer {load_balancer_name}")
            return True, f"Successfully enabled cross-zone load balancing for Classic Load Balancer {load_balancer_name}"

        except Exception as e:
            error_msg = f"Failed to enable cross-zone load balancing: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def _get_load_balancer_type(self, load_balancer_arn):
        """Helper method to determine the load balancer type from its ARN"""
        if ":loadbalancer/app/" in load_balancer_arn:
            return "application"
        elif ":loadbalancer/net/" in load_balancer_arn:
            return "network"
        elif ":loadbalancer/" in load_balancer_arn:
            return "classic"
        return None

    def _get_elb_logging_principal(self, region):
        """
        Returns the correct ELB logging service principal for the given region.

        For ELB/ALB/NLB, AWS uses region-specific service principals for writing logs.
        """
        # For most regions, the format is "elasticloadbalancing.amazonaws.com"
        # For AWS China, the format is "elasticloadbalancing.amazonaws.com.cn"
        # For AWS GovCloud, it's a different format

        if region.startswith('cn-'):
            return "elasticloadbalancing.amazonaws.com.cn"
        elif region.startswith('us-gov-'):
            return "elasticloadbalancing.amazonaws.com"
        else:
            return "elasticloadbalancing.amazonaws.com"

    def get_remediation_functions(self):
        """
        Return a mapping of policy checks to remediation functions.
        """
        return {
            "http_to_https_redirect": self.remediate_http_to_https_redirect,
            "classic_load_balancer_az_span": self.remediate_classic_load_balancer_az_span,
            "alb_desync_mitigation_mode": self.remediate_alb_desync_mitigation_mode,
            "load_balancers_az_span": self.remediate_load_balancers_az_span,
            "classic_load_balancer_desync_mitigation_mode": self.remediate_classic_load_balancer_desync_mitigation_mode,
            "classic_load_balancer_secure_listener_configuration": self.remediate_classic_load_balancer_secure_listener_configuration,
            "alb_drop_invalid_http_headers": self.remediate_alb_drop_invalid_http_headers,
            "load_balancers_logging_enabled": self.remediate_load_balancers_logging_enabled,
            "load_balancers_deletion_protection": self.remediate_load_balancers_deletion_protection,
            "classic_load_balancer_connection_draining": self.remediate_classic_load_balancer_connection_draining,
            "classic_load_balancer_ssl_security_policy": self.remediate_classic_load_balancer_ssl_security_policy,
            "classic_load_balancer_cross_zone_load_balancing": self.remediate_classic_load_balancer_cross_zone_load_balancing
        }

    async def remediate(self, policy_check, details):
        """
        Main remediation method that delegates to specific remediation functions.
        """
        logger.info(f"Starting remediation for ELB policy check: {policy_check}")

        # Initialize updated details with the original details
        updated_details = details.copy()

        # Initialize AWS client
        session = self.get_session()
        region = details.get("region", "us-east-1")

        # Determine which AWS client to use based on the load balancer type
        if policy_check.startswith("classic_load_balancer_"):
            async with session.client(AWSServiceNameEnum.ELB.value, region_name=region) as client:
                self.client = client

                # Get the appropriate remediation function
                remediation_functions = self.get_remediation_functions()
                if policy_check not in remediation_functions:
                    error_msg = f"No remediation function found for policy check: {policy_check}"
                    logger.warning(error_msg)

                    # Update remediation results
                    if policy_check in self.remediation_results:
                        self.remediation_results[policy_check]["status"] = False
                        self.remediation_results[policy_check]["message"] = error_msg

                    # Add remediation failure information
                    updated_details["remediate"] = {
                        "status": ResourceComplianceStatusEnum.FAIL.value,
                        "message": f"Error: {error_msg}"
                    }

                    return False, error_msg, updated_details

                # Call the appropriate remediation function
                success, message = await remediation_functions[policy_check](details)
        else:
            # Use 'elbv2' directly as that's how it's used in scan.py
            async with session.client('elbv2', region_name=region) as client:
                self.client = client

                # Get the appropriate remediation function
                remediation_functions = self.get_remediation_functions()
                if policy_check not in remediation_functions:
                    error_msg = f"No remediation function found for policy check: {policy_check}"
                    logger.warning(error_msg)

                    # Update remediation results
                    if policy_check in self.remediation_results:
                        self.remediation_results[policy_check]["status"] = False
                        self.remediation_results[policy_check]["message"] = error_msg

                    # Add remediation failure information
                    updated_details["remediate"] = {
                        "status": ResourceComplianceStatusEnum.FAIL.value,
                        "message": f"Error: {error_msg}"
                    }

                    return False, error_msg, updated_details

                # Call the appropriate remediation function
                success, message = await remediation_functions[policy_check](details)

        # Update remediation results
        if policy_check in self.remediation_results:
            self.remediation_results[policy_check]["status"] = success
            self.remediation_results[policy_check]["message"] = message

        # Update the details with remediation information
        updated_details["remediate"] = {
            "status": ResourceComplianceStatusEnum.PASS.value if success else ResourceComplianceStatusEnum.FAIL.value,
            "message": message
        }

        # If successful, update the field values
        if success and policy_check in self.remediation_results:
            for field, value in self.remediation_results[policy_check]["field_updates"].items():
                updated_details[field] = value

            # Update compliance status
            updated_details["compliance"] = True

        return success, message, updated_details
