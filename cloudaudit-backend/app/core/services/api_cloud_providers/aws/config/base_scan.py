from app.core.services.api_cloud_providers import AWSServiceFactory

__all__ = ["BaseChecksProcessor"]


class BaseChecksProcessor:
    def __init__(self, credentials, regions=None):
        if regions is None:
            regions = []
        self.credentials = credentials
        self.client = None
        self.regions = regions
        self.aws_service_factory = None

    def get_session(self, region_name=None):
        self.aws_service_factory = AWSServiceFactory(self.credentials, region_name)
        return self.aws_service_factory.get_session()

    async def is_region_accessible(self):
        return await self.aws_service_factory.is_region_accessible()

