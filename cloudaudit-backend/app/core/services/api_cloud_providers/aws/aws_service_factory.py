import aioboto3
from celery.utils.log import get_task_logger
from botocore.exceptions import ClientError, SSLError
from app import app
from app.common import (AWSRegionNameEnum, CloudProviderOrganizationIAMRoleARNEnum, AWSServiceNameEnum,
                        AWSChildAccountAccessRoleException)


__all__ = ['AWSServiceFactory']


logger = get_task_logger(__name__)


class AWSServiceFactory:
    def __init__(self, credentials, region_name=AWSRegionNameEnum.US_EAST_1.value):
        self.credentials = credentials
        self.region_name = region_name
        self.session = self.create_session()
        self.sts_client = None
        self.assume_role_data = None

    def create_session(self):
        logger.info("Creating AWS session with region: %s", self.region_name)
        return aioboto3.Session(
            aws_access_key_id=self.credentials['access_key'],
            aws_secret_access_key=self.credentials['secret_key'],
            aws_session_token=self.credentials.get('session_token', None),
            region_name=self.region_name
        )

    def get_session(self):
        # logger.info("session--session", self.session)
        return self.session

    async def aws_assume_role(self, account_id):
        return await self.sts_client.assume_role(
                RoleArn=CloudProviderOrganizationIAMRoleARNEnum.AWS.value.
                replace("#account_id#", account_id),
                RoleSessionName='AssumeRoleSession',
                DurationSeconds=app.config.AWS_ASSUME_ROLE_SESSION_DURATION  # 6 hours
            )

    async def get_child_account_temp_credentials(self, account_id):
        async with self.session.client(AWSServiceNameEnum.STS.value) as client:
            self.sts_client = client
            try:
                assumed_role = await self.aws_assume_role(account_id)
            except ClientError as error:
                raise AWSChildAccountAccessRoleException
        return assumed_role

    async def get_child_account_ids(self):
        async with self.session.client(AWSServiceNameEnum.ORGANIZATIONS.value) as client:
            pages = await client.get_paginator('list_accounts').paginate()
            accounts = []
            async for page in pages:
                accounts.extend(page['Accounts'])

        return [account['Id'] for account in accounts]

    async def is_region_accessible(self):
        try:
            async with self.session.client(AWSServiceNameEnum.STS.value) as sts:
                await sts.get_caller_identity()
            return True
        except (ClientError, SSLError) as e:
            logger.error(f"Skipping region {self.region_name}: {e.response['Error']['Message']}")
            return False
