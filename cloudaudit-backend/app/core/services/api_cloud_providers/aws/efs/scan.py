import asyncio
from celery.utils.log import get_task_logger
from ..config import BaseChecksProcessor
from app.common import (AWSServiceNameEnum, SeverityEnum, ResourceComplianceStatusEnum,
                        EFSChecksDescriptionEnum)

__all__ = ['ChecksProcessor']

logger = get_task_logger(__name__)


class ChecksProcessor(BaseChecksProcessor):
    def __init__(self, credentials, regions=[]):
        super().__init__(credentials, regions)
        self.file_systems = None
        self.findings = {
            "encrypted_with_kms": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EFSChecksDescriptionEnum.ENCRYPTED_WITH_KMS.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "included_in_backup_plans": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EFSChecksDescriptionEnum.INCLUDED_IN_BACKUP_PLANS.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "secure_access_points": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EFSChecksDescriptionEnum.SECURE_ACCESS_POINTS.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "mount_targets_not_in_public_subnet": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EFSChecksDescriptionEnum.MOUNT_TARGETS_NOT_IN_PUBLIC_SUBNET.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "automatic_backups_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EFSChecksDescriptionEnum.AUTOMATIC_BACKUPS_ENABLED.value,
                "severity": SeverityEnum.MEDIUM.value
            }
        }

    async def check_encrypted_with_kms(self):
        """
        Elastic File System should be configured to encrypt file data at-rest using AWS KMS.
        """
        all_compliant = True

        for fs in self.file_systems.get("FileSystems", []):
            fs_id = fs.get("FileSystemId")
            encrypted = fs.get("Encrypted", False)
            kms_key_id = fs.get("KmsKeyId")
            is_encrypted_kms = encrypted and kms_key_id is not None

            self.findings["encrypted_with_kms"]["details"].append({
                "file_system_id": fs_id,
                "encrypted": encrypted,
                "kms_key_id": kms_key_id,
                "compliance": is_encrypted_kms,
                "region": self.client.meta.region_name
            })

            if not is_encrypted_kms:
                all_compliant = False

        if self.findings["encrypted_with_kms"]["status"] and not all_compliant:
            self.findings["encrypted_with_kms"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_included_in_backup_plans(self):
        """
        Amazon EFS volumes should be in backup plans.
        """
        all_compliant = True

        # Fetch all protected resources from AWS Backup
        protected_resources = []
        session = self.get_session()
        async with session.client(AWSServiceNameEnum.BACKUP.value,
                                  region_name=self.client.meta.region_name) as backup_client:
            paginator = backup_client.get_paginator("list_protected_resources")
            async for page in paginator.paginate():
                protected_resources.extend(page.get("Results", []))

        for fs in self.file_systems.get("FileSystems", []):
            fs_id = fs.get("FileSystemId")
            is_in_backup_plan = any(
                resource.get("ResourceArn", "").endswith(fs_id) for resource in protected_resources
            )

            self.findings["included_in_backup_plans"]["details"].append({
                "file_system_id": fs_id,
                "is_in_backup_plan": is_in_backup_plan,
                "compliance": is_in_backup_plan,
                "region": self.client.meta.region_name
            })

            if not is_in_backup_plan:
                all_compliant = False

        if self.findings["included_in_backup_plans"]["status"] and not all_compliant:
            self.findings["included_in_backup_plans"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_secure_access_points(self):
        """
        EFS access points should enforce both a root directory and user identity.
        """
        all_compliant = True

        for fs in self.file_systems.get("FileSystems", []):
            fs_id = fs.get("FileSystemId")
            access_points = await self.client.describe_access_points(FileSystemId=fs_id)

            for ap in access_points.get("AccessPoints", []):
                ap_id = ap.get("AccessPointId")

                # Check root directory enforcement
                root_directory = ap.get("RootDirectory", {})
                path = root_directory.get("Path")
                is_root_directory_enforced = bool(path)

                # Check user identity enforcement
                posix_user = ap.get("PosixUser", {})
                uid = posix_user.get("Uid")
                gid = posix_user.get("Gid")
                is_user_identity_enforced = uid is not None and gid is not None

                # Both conditions must be met for compliance
                is_compliant = is_root_directory_enforced and is_user_identity_enforced

                self.findings["secure_access_points"]["details"].append({
                    "file_system_id": fs_id,
                    "access_point_id": ap_id,
                    "root_directory_path": path,
                    "is_root_directory_enforced": is_root_directory_enforced,
                    "uid": uid,
                    "gid": gid,
                    "is_user_identity_enforced": is_user_identity_enforced,
                    "compliance": is_compliant,
                    "region": self.client.meta.region_name
                })

                if not is_compliant:
                    all_compliant = False

        if self.findings["secure_access_points"]["status"] and not all_compliant:
            self.findings["secure_access_points"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_mount_targets_not_in_public_subnet(self):
        """
        EFS mount targets should not be associated with a public subnet.
        """
        all_compliant = True

        for fs in self.file_systems.get("FileSystems", []):
            fs_id = fs.get("FileSystemId")
            mount_targets = await self.client.describe_mount_targets(FileSystemId=fs_id)

            for mt in mount_targets.get("MountTargets", []):
                mt_id = mt.get("MountTargetId")
                subnet_id = mt.get("SubnetId")

                # Fetch subnet details
                session = self.get_session()
                async with session.client('ec2', region_name=self.client.meta.region_name) as ec2_client:
                    subnet_details = await ec2_client.describe_subnets(SubnetIds=[subnet_id])
                    is_public_subnet = any(
                        subnet.get("MapPublicIpOnLaunch", False) for subnet in subnet_details.get("Subnets", [])
                    )

                self.findings["mount_targets_not_in_public_subnet"]["details"].append({
                    "file_system_id": fs_id,
                    "mount_target_id": mt_id,
                    "subnet_id": subnet_id,
                    "is_public_subnet": is_public_subnet,
                    "compliance": not is_public_subnet,
                    "region": self.client.meta.region_name
                })

                if is_public_subnet:
                    all_compliant = False

        if self.findings["mount_targets_not_in_public_subnet"]["status"] and not all_compliant:
            self.findings["mount_targets_not_in_public_subnet"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_automatic_backups_enabled(self):
        """
        EFS file systems should have automatic backups enabled.
        """
        all_compliant = True

        for fs in self.file_systems.get("FileSystems", []):
            fs_id = fs.get("FileSystemId")
            backup_policy = await self.client.describe_backup_policy(FileSystemId=fs_id)
            is_backup_enabled = backup_policy.get("BackupPolicy", {}).get("Status") == "ENABLED"

            self.findings["automatic_backups_enabled"]["details"].append({
                "file_system_id": fs_id,
                "is_backup_enabled": is_backup_enabled,
                "compliance": is_backup_enabled,
                "region": self.client.meta.region_name
            })

            if not is_backup_enabled:
                all_compliant = False

        if self.findings["automatic_backups_enabled"]["status"] and not all_compliant:
            self.findings["automatic_backups_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    def get_check_functions(self):
        return [
            self.check_encrypted_with_kms,
            self.check_included_in_backup_plans,
            self.check_secure_access_points,
            self.check_mount_targets_not_in_public_subnet,
            self.check_automatic_backups_enabled
        ]

    async def run_checks(self):
        for region in self.regions:
            logger.info(f"Scanning region: {region}")
            session = self.get_session(region)
            if not await self.is_region_accessible():
                logger.warning(f"Region {region} is not accessible. Skipping checks.")
                continue
            async with session.client(AWSServiceNameEnum.EFS.value, region_name=region) as client:
                self.client = client

                self.file_systems = await self.client.describe_file_systems()

                for check_function in self.get_check_functions():
                    await check_function()
            await asyncio.sleep(5)

        return self.findings
