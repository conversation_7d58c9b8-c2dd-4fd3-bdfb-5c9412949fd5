import asyncio
from celery.utils.log import get_task_logger
from ..config import BaseRemediationProcessor
from app.common import AWSServiceNameEnum

__all__ = ['RemediationProcessor']

logger = get_task_logger(__name__)


class RemediationProcessor(BaseRemediationProcessor):
    """
    Processor for remediating EFS compliance issues.
    Inherits from BaseRemediationProcessor to reuse AWS session management.
    """

    def __init__(self, credentials, regions=[]):
        super().__init__(credentials, regions)
        self.remediation_results = {
            "encrypted_with_kms": {
                "status": False,
                "message": "",
                "field_updates": {"encrypted": True, "kms_key_id": "Updated"}
            },
            "included_in_backup_plans": {
                "status": False,
                "message": "",
                "field_updates": {"is_in_backup_plan": True}
            },
            "secure_access_points": {
                "status": False,
                "message": "",
                "field_updates": {
                    "is_root_directory_enforced": True,
                    "is_user_identity_enforced": True
                }
            },
            "mount_targets_not_in_public_subnet": {
                "status": False,
                "message": "",
                "field_updates": {"is_public_subnet": False}
            },
            "automatic_backups_enabled": {
                "status": False,
                "message": "",
                "field_updates": {"is_backup_enabled": True}
            }
        }

    async def remediate_encrypted_with_kms(self, details):
        """
        Remediate EFS by creating a new encrypted file system with KMS and migrating data.
        Note: EFS encryption cannot be enabled after creation, so we need to create a new file system.
        """
        try:
            file_system_id = details.get("file_system_id", "")
            region = details.get("region", "")

            if not file_system_id or not region:
                return False, "Missing file system ID or region"

            # AWS Precheck: Verify file system exists and check current encryption status
            try:
                # Check if file system exists
                file_system = await self.client.describe_file_systems(
                    FileSystemId=file_system_id
                )

                if not file_system.get("FileSystems"):
                    return False, f"File system {file_system_id} not found"

                fs_details = file_system["FileSystems"][0]

                # Check if already encrypted with KMS
                encrypted = fs_details.get("Encrypted", False)
                kms_key_id = fs_details.get("KmsKeyId")

                if encrypted and kms_key_id:
                    return True, f"File system {file_system_id} is already encrypted with KMS key {kms_key_id}"

            except Exception as e:
                return False, f"Failed to retrieve file system information: {str(e)}"

            # Create a KMS key for EFS encryption
            session = self.get_session()
            async with session.client('kms', region_name=region) as kms_client:
                # Create a new KMS key for EFS
                key_response = await kms_client.create_key(
                    Description=f'KMS key for EFS file system encryption',
                    KeyUsage='ENCRYPT_DECRYPT',
                    Origin='AWS_KMS',
                    Tags=[
                        {
                            'TagKey': 'Name',
                            'TagValue': f'efs-{file_system_id}-encryption'
                        },
                        {
                            'TagKey': 'Purpose',
                            'TagValue': 'EFS Encryption'
                        }
                    ]
                )

                key_id = key_response['KeyMetadata']['KeyId']

                # Create an alias for the key
                await kms_client.create_alias(
                    AliasName=f'alias/efs-{file_system_id}-encryption',
                    TargetKeyId=key_id
                )

            # Create a new encrypted file system
            new_fs = await self.client.create_file_system(
                PerformanceMode=fs_details.get("PerformanceMode", "generalPurpose"),
                Encrypted=True,
                KmsKeyId=key_id,
                ThroughputMode=fs_details.get("ThroughputMode", "bursting"),
                Tags=[
                    {
                        'Key': 'Name',
                        'Value': fs_details.get("Name", f"Encrypted-{file_system_id}")
                    },
                    {
                        'Key': 'OriginalFileSystemId',
                        'Value': file_system_id
                    }
                ]
            )

            new_file_system_id = new_fs["FileSystemId"]

            # Note: Data migration would need to be handled separately
            # This could involve AWS DataSync or a custom solution

            logger.info(f"Created new encrypted file system {new_file_system_id} with KMS key {key_id}")
            return True, f"Created new encrypted file system {new_file_system_id}. Manual data migration required from {file_system_id}."

        except Exception as e:
            error_msg = f"Failed to create encrypted file system: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_included_in_backup_plans(self, details):
        """
        Remediate EFS by including it in AWS Backup plans.
        """
        try:
            file_system_id = details.get("file_system_id", "")
            region = details.get("region", "")

            if not file_system_id or not region:
                return False, "Missing file system ID or region"

            # AWS Precheck: Verify file system exists
            try:
                file_system = await self.client.describe_file_systems(
                    FileSystemId=file_system_id
                )

                if not file_system.get("FileSystems"):
                    return False, f"File system {file_system_id} not found"

            except Exception as e:
                return False, f"Failed to retrieve file system information: {str(e)}"

            # AWS Precheck: Check if already in a backup plan
            session = self.get_session()
            async with session.client(AWSServiceNameEnum.BACKUP.value, region_name=region) as backup_client:
                # Check if the file system is already protected
                try:
                    protected_resources = []
                    paginator = backup_client.get_paginator("list_protected_resources")
                    async for page in paginator.paginate():
                        protected_resources.extend(page.get("Results", []))

                    is_protected = any(
                        resource.get("ResourceArn", "").endswith(file_system_id)
                        for resource in protected_resources
                    )

                    if is_protected:
                        return True, f"File system {file_system_id} is already included in a backup plan"

                except Exception as e:
                    logger.warning(f"Failed to check if file system is already protected: {str(e)}")
                    # Continue with remediation even if check fails

            # Create a backup vault if it doesn't exist
            vault_name = "EFSBackupVault"

            try:
                await backup_client.describe_backup_vault(
                    BackupVaultName=vault_name
                )
            except:
                # Create the vault if it doesn't exist
                await backup_client.create_backup_vault(
                    BackupVaultName=vault_name,
                    Tags={
                        'Purpose': 'EFS Backups'
                    }
                )

            # Create a backup plan
            plan_name = f"EFS-Backup-Plan-{file_system_id}"

            # Define backup plan with daily backups, 30-day retention
            backup_plan = {
                'BackupPlanName': plan_name,
                'Rules': [
                    {
                        'RuleName': 'DailyBackups',
                        'TargetBackupVaultName': vault_name,
                        'ScheduleExpression': 'cron(0 5 ? * * *)',  # Daily at 5:00 UTC
                        'StartWindowMinutes': 60,
                        'CompletionWindowMinutes': 120,
                        'Lifecycle': {
                            'DeleteAfterDays': 30
                        }
                    }
                ]
            }

            # Create the backup plan
            plan_response = await backup_client.create_backup_plan(
                BackupPlan=backup_plan,
                BackupPlanTags={
                    'FileSystemId': file_system_id
                }
            )

            backup_plan_id = plan_response['BackupPlanId']

            # Create a selection of resources to back up
            selection = {
                'SelectionName': f'EFS-Selection-{file_system_id}',
                'IamRoleArn': f'arn:aws:iam::{self.credentials["aws_account_id"]}:role/service-role/AWSBackupDefaultServiceRole',
                'Resources': [
                    f'arn:aws:elasticfilesystem:{region}:{self.credentials["aws_account_id"]}:file-system/{file_system_id}'
                ],
                'ListOfTags': []
            }

            # Create the backup selection
            await backup_client.create_backup_selection(
                BackupPlanId=backup_plan_id,
                BackupSelection=selection
            )

            logger.info(f"Successfully included EFS file system {file_system_id} in backup plan {plan_name}")
            return True, f"Successfully included EFS file system {file_system_id} in backup plan {plan_name}"

        except Exception as e:
            error_msg = f"Failed to include file system in backup plans: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_secure_access_points(self, details):
        """
        Remediate EFS by updating access points to enforce both root directory and user identity.
        """
        try:
            file_system_id = details.get("file_system_id", "")
            access_point_id = details.get("access_point_id", "")
            region = details.get("region", "")

            if not file_system_id or not access_point_id or not region:
                return False, "Missing file system ID, access point ID, or region"

            # AWS Precheck: Verify file system and access point exist
            try:
                # Check if file system exists
                file_system = await self.client.describe_file_systems(
                    FileSystemId=file_system_id
                )

                if not file_system.get("FileSystems"):
                    return False, f"File system {file_system_id} not found"

                # Check if access point exists and get current configuration
                access_point = await self.client.describe_access_points(
                    AccessPointId=access_point_id
                )

                if not access_point.get("AccessPoints"):
                    return False, f"Access point {access_point_id} not found"

                ap_details = access_point["AccessPoints"][0]

                # Check if root directory is already enforced
                root_directory = ap_details.get("RootDirectory", {})
                path = root_directory.get("Path")
                is_root_directory_enforced = bool(path)

                # Check if user identity is already enforced
                posix_user = ap_details.get("PosixUser", {})
                uid = posix_user.get("Uid")
                gid = posix_user.get("Gid")
                is_user_identity_enforced = uid is not None and gid is not None

                # If both are already enforced, no remediation needed
                if is_root_directory_enforced and is_user_identity_enforced:
                    # Update field_updates with actual values
                    self.remediation_results["secure_access_points"]["field_updates"]["root_directory_path"] = path
                    self.remediation_results["secure_access_points"]["field_updates"]["uid"] = uid
                    self.remediation_results["secure_access_points"]["field_updates"]["gid"] = gid

                    return True, f"Access point {access_point_id} already has both root directory and user identity enforced"

            except Exception as e:
                return False, f"Failed to retrieve access point information: {str(e)}"

            # Since access points cannot be modified after creation, we need to create a new one
            # Create a new access point with both root directory and user identity enforcement

            # Prepare root directory configuration
            root_dir_path = path if is_root_directory_enforced else '/restricted'
            root_dir_config = {
                'Path': root_dir_path,
            }

            # Add creation info if needed
            if not is_root_directory_enforced:
                root_dir_config['CreationInfo'] = {
                    'OwnerUid': 1000,
                    'OwnerGid': 1000,
                    'Permissions': '0755'
                }

            # Prepare POSIX user configuration
            new_uid = uid if is_user_identity_enforced else 1000
            new_gid = gid if is_user_identity_enforced else 1000
            posix_user_config = {
                'Uid': new_uid,
                'Gid': new_gid
            }

            # Create the new access point
            new_ap = await self.client.create_access_point(
                FileSystemId=file_system_id,
                PosixUser=posix_user_config,
                RootDirectory=root_dir_config,
                Tags=[
                    {
                        'Key': 'Name',
                        'Value': ap_details.get("Name", f"Secure-AP-{access_point_id}")
                    },
                    {
                        'Key': 'OriginalAccessPointId',
                        'Value': access_point_id
                    }
                ]
            )

            new_access_point_id = new_ap["AccessPointId"]

            # Update field_updates with actual values
            self.remediation_results["secure_access_points"]["field_updates"]["root_directory_path"] = root_dir_path
            self.remediation_results["secure_access_points"]["field_updates"]["uid"] = new_uid
            self.remediation_results["secure_access_points"]["field_updates"]["gid"] = new_gid
            self.remediation_results["secure_access_points"]["field_updates"]["access_point_id"] = new_access_point_id

            logger.info(
                f"Created new access point {new_access_point_id} with enforced root directory and user identity")
            return True, f"Created new access point {new_access_point_id} with enforced root directory and user identity. Please update applications to use the new access point."

        except Exception as e:
            error_msg = f"Failed to create secure access point: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_mount_targets_not_in_public_subnet(self, details):
        """
        Remediate EFS by moving mount targets from public to private subnets.
        """
        try:
            file_system_id = details.get("file_system_id", "")
            mount_target_id = details.get("mount_target_id", "")
            region = details.get("region", "")

            if not file_system_id or not mount_target_id or not region:
                return False, "Missing file system ID, mount target ID, or region"

            # AWS Precheck: Verify file system and mount target exist
            try:
                # Check if file system exists
                file_system = await self.client.describe_file_systems(
                    FileSystemId=file_system_id
                )

                if not file_system.get("FileSystems"):
                    return False, f"File system {file_system_id} not found"

                # Check if mount target exists and get current configuration
                mount_target = await self.client.describe_mount_targets(
                    MountTargetId=mount_target_id
                )

                if not mount_target.get("MountTargets"):
                    return False, f"Mount target {mount_target_id} not found"

                mt_details = mount_target["MountTargets"][0]
                current_subnet_id = mt_details.get("SubnetId")

                # Check if the current subnet is public
                session = self.get_session()
                async with session.client('ec2', region_name=region) as ec2_client:
                    subnet_details = await ec2_client.describe_subnets(SubnetIds=[current_subnet_id])

                    is_public_subnet = any(
                        subnet.get("MapPublicIpOnLaunch", False)
                        for subnet in subnet_details.get("Subnets", [])
                    )

                    if not is_public_subnet:
                        # Update field_updates with actual values
                        self.remediation_results["mount_targets_not_in_public_subnet"]["field_updates"][
                            "subnet_id"] = current_subnet_id

                        return True, f"Mount target {mount_target_id} is already in a private subnet"

            except Exception as e:
                return False, f"Failed to retrieve mount target information: {str(e)}"

            # Find a private subnet in the same VPC
            vpc_id = subnet_details["Subnets"][0]["VpcId"]
            all_subnets = await ec2_client.describe_subnets(
                Filters=[
                    {
                        'Name': 'vpc-id',
                        'Values': [vpc_id]
                    }
                ]
            )

            private_subnets = [
                subnet for subnet in all_subnets.get("Subnets", [])
                if not subnet.get("MapPublicIpOnLaunch", False)
            ]

            if not private_subnets:
                return False, f"No private subnets found in VPC {vpc_id}. Please create a private subnet first."

            # Select the first private subnet
            private_subnet_id = private_subnets[0]["SubnetId"]

            # Since mount targets cannot be modified after creation, we need to delete and recreate
            # Get the security groups from the current mount target
            security_groups = mt_details.get("SecurityGroups", [])

            # Delete the current mount target
            await self.client.delete_mount_target(
                MountTargetId=mount_target_id
            )

            # Wait for the mount target to be deleted
            import time
            time.sleep(30)  # Simple wait, in production use a proper waiter

            # Create a new mount target in the private subnet
            new_mt = await self.client.create_mount_target(
                FileSystemId=file_system_id,
                SubnetId=private_subnet_id,
                SecurityGroups=security_groups
            )

            new_mount_target_id = new_mt["MountTargetId"]

            # Update field_updates with actual values
            self.remediation_results["mount_targets_not_in_public_subnet"]["field_updates"][
                "subnet_id"] = private_subnet_id
            self.remediation_results["mount_targets_not_in_public_subnet"]["field_updates"][
                "mount_target_id"] = new_mount_target_id

            logger.info(f"Created new mount target {new_mount_target_id} in private subnet {private_subnet_id}")
            return True, f"Created new mount target {new_mount_target_id} in private subnet {private_subnet_id}. The old mount target {mount_target_id} has been deleted."

        except Exception as e:
            error_msg = f"Failed to move mount target to private subnet: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def remediate_automatic_backups_enabled(self, details):
        """
        Remediate EFS by enabling automatic backups.
        """
        try:
            file_system_id = details.get("file_system_id", "")
            region = details.get("region", "")

            if not file_system_id or not region:
                return False, "Missing file system ID or region"

            # Enable automatic backups by updating the backup policy
            await self.client.put_backup_policy(
                FileSystemId=file_system_id,
                BackupPolicy={
                    'Status': 'ENABLED'
                }
            )

            logger.info(f"Successfully enabled automatic backups for EFS file system {file_system_id}")
            return True, f"Successfully enabled automatic backups for EFS file system {file_system_id}"

        except Exception as e:
            error_msg = f"Failed to enable automatic backups: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def get_remediation_functions(self):
        """
        Return a mapping of policy checks to remediation functions.
        """
        return {
            "encrypted_with_kms": self.remediate_encrypted_with_kms,
            "included_in_backup_plans": self.remediate_included_in_backup_plans,
            "secure_access_points": self.remediate_secure_access_points,
            "mount_targets_not_in_public_subnet": self.remediate_mount_targets_not_in_public_subnet,
            "automatic_backups_enabled": self.remediate_automatic_backups_enabled
        }

    async def remediate(self, policy_check, details):
        """
        Main remediation method that delegates to specific remediation functions.
        """
        logger.info(f"Starting remediation for EFS policy check: {policy_check}")

        # Initialize updated details with the original details
        updated_details = details.copy()

        # Initialize AWS client
        session = self.get_session()
        async with session.client(AWSServiceNameEnum.EFS.value,
                                  region_name=details.get("region", "us-east-1")) as client:
            self.client = client

            # Get the appropriate remediation function
            remediation_functions = self.get_remediation_functions()
            if policy_check not in remediation_functions:
                error_msg = f"No remediation function found for policy check: {policy_check}"
                logger.warning(error_msg)

                # Update remediation results
                if policy_check in self.remediation_results:
                    self.remediation_results[policy_check]["status"] = False
                    self.remediation_results[policy_check]["message"] = error_msg

                # Add remediation failure information
                updated_details["remediate"] = {
                    "status": "fail",
                    "message": f"Error: {error_msg}"
                }

                return False, error_msg, updated_details

            # Call the appropriate remediation function
            success, message = await remediation_functions[policy_check](details)

            # Update remediation results
            if policy_check in self.remediation_results:
                self.remediation_results[policy_check]["status"] = success
                self.remediation_results[policy_check]["message"] = message

            # Update the details with remediation information
            updated_details["remediate"] = {
                "status": "pass" if success else "fail",
                "message": message
            }

            # If successful, update the field values
            if success and policy_check in self.remediation_results:
                for field, value in self.remediation_results[policy_check]["field_updates"].items():
                    updated_details[field] = value

                # Update compliance status
                updated_details["compliance"] = True

            return success, message, updated_details
