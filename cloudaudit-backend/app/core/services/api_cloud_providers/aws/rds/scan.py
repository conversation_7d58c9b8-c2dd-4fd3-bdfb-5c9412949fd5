import asyncio

from celery.utils.log import get_task_logger
from ..config import BaseChecksProcessor
from app.common import (AWSServiceNameEnum, SeverityEnum, ResourceComplianceStatusEnum,
                        RDSChecksDescriptionEnum, DEFAULT_RDS_ADMIN_USERNAMES)


__all__ = ['ChecksProcessor']


logger = get_task_logger(__name__)


class ChecksProcessor(BaseChecksProcessor):
    def __init__(self, credentials, regions=[]):
        super().__init__(credentials, regions)
        self.instances = None
        self.clusters = None
        self.db_snapshots = None
        self.findings = {
            "rds_custom_admin": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_CUSTOM_ADMIN.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "rds_mysql_encryption_in_transit": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_MYSQL_ENCRYPTION_IN_TRANSIT.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "rds_enhanced_monitoring": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_ENHANCED_MONITORING.value,
                "severity": SeverityEnum.LOW.value
            },
            "rds_custom_port": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_CUSTOM_PORT.value,
                "severity": SeverityEnum.LOW.value
            },
            "rds_cluster_copy_tags_to_snapshots": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_CLUSTER_COPY_TAGS_TO_SNAPSHOTS.value,
                "severity": SeverityEnum.LOW.value
            },
            "rds_instance_copy_tags_to_snapshots": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_INSTANCE_COPY_TAGS_TO_SNAPSHOTS.value,
                "severity": SeverityEnum.LOW.value
            },
            "rds_snapshot_privacy": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_SNAPSHOT_PRIVACY.value,
                "severity": SeverityEnum.CRITICAL.value
            },
            "rds_instance_public_access": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_INSTANCE_PUBLIC_ACCESS.value,
                "severity": SeverityEnum.CRITICAL.value
            },
            "rds_auto_minor_version_upgrade": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_AUTO_MINOR_VERSION_UPGRADE.value,
                "severity": SeverityEnum.HIGH.value
            },
            "rds_instance_vpc_deployment": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_INSTANCE_VPC_DEPLOYMENT.value,
                "severity": SeverityEnum.HIGH.value
            },
            "rds_iam_authentication": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_IAM_AUTHENTICATION.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "rds_automatic_backups": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_AUTOMATIC_BACKUPS.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "rds_cluster_iam_authentication": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_CLUSTER_IAM_AUTHENTICATION.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "aurora_cluster_backtracking": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.AURORA_CLUSTER_BACKTRACKING.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "rds_cluster_multi_az": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_CLUSTER_MULTI_AZ.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "rds_cluster_encryption": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_CLUSTER_ENCRYPTION.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "rds_instance_encryption": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_INSTANCE_ENCRYPTION.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "aurora_mysql_audit_logging": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.AURORA_MYSQL_AUDIT_LOGGING.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "rds_cluster_auto_minor_version_upgrade": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_CLUSTER_AUTO_MINOR_VERSION_UPGRADE.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "rds_postgresql_logging": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_POSTGRESQL_LOGGING.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "rds_postgresql_encryption_in_transit": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_POSTGRESQL_ENCRYPTION_IN_TRANSIT.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "rds_snapshot_encryption": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_SNAPSHOT_ENCRYPTION.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "rds_multi_az": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_MULTI_AZ.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "rds_event_notifications": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_EVENT_NOTIFICATIONS.value,
                "severity": SeverityEnum.LOW.value
            },
            "rds_instance_event_notifications": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_INSTANCE_EVENT_NOTIFICATIONS.value,
                "severity": SeverityEnum.LOW.value
            },
            "rds_parameter_group_event_notifications": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_PARAMETER_GROUP_EVENT_NOTIFICATIONS.value,
                "severity": SeverityEnum.LOW.value
            },
            "rds_security_group_event_notifications": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_SECURITY_GROUP_EVENT_NOTIFICATIONS.value,
                "severity": SeverityEnum.LOW.value
            },
            "rds_cluster_deletion_protection": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_CLUSTER_DELETION_PROTECTION.value,
                "severity": SeverityEnum.LOW.value
            },
            "rds_instance_deletion_protection": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_INSTANCE_DELETION_PROTECTION.value,
                "severity": SeverityEnum.LOW.value
            },
            "rds_instance_logs_to_cloudwatch": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_INSTANCE_LOGS_TO_CLOUDWATCH.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "rds_cluster_custom_admin": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_CLUSTER_CUSTOM_ADMIN.value,
                "severity": SeverityEnum.MEDIUM.value
            }
        }

    async def check_rds_custom_admin_username(self):
        """
        AWS RDS instances do not use the default administrative usernames (such as admin, root, postgres, sqladmin,
        etc.) for security reasons
        """
        for instance in self.instances["DBInstances"]:
            db_instance_id = instance["DBInstanceIdentifier"]
            master_username = instance["MasterUsername"]
            engine = instance["Engine"]
            region = self.client.meta.region_name

            using_default_admin = master_username.lower() in DEFAULT_RDS_ADMIN_USERNAMES

            if (self.findings["rds_custom_admin"]["status"] == ResourceComplianceStatusEnum.PASS.value and
                    using_default_admin):
                self.findings["rds_custom_admin"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_custom_admin"]["details"].append({
                "db_instance_id": db_instance_id,
                "master_username": master_username,
                "engine": engine,
                "region": region,
                "using_default_admin": using_default_admin,
                "compliance": not using_default_admin
            })

    async def check_rds_mysql_encryption_in_transit(self):
        """
        MySQL-based RDS instances enforce SSL/TLS encryption for database connections
        """
        for instance in self.instances["DBInstances"]:
            db_instance_id = instance["DBInstanceIdentifier"]
            engine = instance["Engine"]
            region = self.client.meta.region_name

            encrypted_in_transit = None
            if engine.startswith("mysql"):  # Apply check only for MySQL-based instances
                parameter_group_name = instance["DBParameterGroups"][0]["DBParameterGroupName"]
                parameters = await self.client.describe_db_parameters(DBParameterGroupName=parameter_group_name)

                for param in parameters["Parameters"]:
                    if param["ParameterName"] == "require_secure_transport":
                        encrypted_in_transit = param["ParameterValue"] == "1"
                        break

            if (self.findings["rds_mysql_encryption_in_transit"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and encrypted_in_transit is not None and not encrypted_in_transit):
                self.findings["rds_mysql_encryption_in_transit"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_mysql_encryption_in_transit"]["details"].append({
                "db_instance_id": db_instance_id,
                "engine": engine,
                "region": region,
                "encrypted_in_transit": encrypted_in_transit,
                "compliance": encrypted_in_transit if encrypted_in_transit is not None else True
            })

    async def check_rds_enhanced_monitoring(self):
        """
        Enhanced Monitoring is enabled for RDS instances
        """
        for instance in self.instances["DBInstances"]:
            db_instance_id = instance["DBInstanceIdentifier"]
            engine = instance["Engine"]
            region = self.client.meta.region_name
            monitoring_interval = instance.get("MonitoringInterval", 0)

            enhanced_monitoring_enabled = monitoring_interval > 0

            if (self.findings["rds_enhanced_monitoring"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and not enhanced_monitoring_enabled):
                self.findings["rds_enhanced_monitoring"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_enhanced_monitoring"]["details"].append({
                "db_instance_id": db_instance_id,
                "engine": engine,
                "region": region,
                "monitoring_interval": monitoring_interval,
                "enhanced_monitoring_enabled": enhanced_monitoring_enabled,
                "compliance": enhanced_monitoring_enabled
            })

    async def check_rds_custom_port(self):
        """
        RDS instances are configured with non-default ports for better security
        """
        default_rds_ports = {
            "mysql": 3306,
            "mariadb": 3306,
            "postgres": 5432,
            "oracle": 1521,
            "sqlserver": 1433,
            "aurora": 3306,  # Aurora MySQL default
            "aurora-mysql": 3306,
            "aurora-postgresql": 5432  # Aurora PostgreSQL default
        }
        for instance in self.instances["DBInstances"]:
            db_instance_id = instance["DBInstanceIdentifier"]
            engine = instance["Engine"]
            region = self.client.meta.region_name
            port = instance["Endpoint"]["Port"]

            using_default_port = port == default_rds_ports.get(engine, None)

            if (self.findings["rds_custom_port"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and using_default_port):
                self.findings["rds_custom_port"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_custom_port"]["details"].append({
                "db_instance_id": db_instance_id,
                "engine": engine,
                "region": region,
                "port": port,
                "using_default_port": using_default_port,
                "compliance": not using_default_port
            })

    async def check_rds_cluster_copy_tags_to_snapshots(self):
        """
        RDS clusters have the CopyTagsToSnapshot setting enabled
        """
        for cluster in self.clusters["DBClusters"]:
            cluster_id = cluster["DBClusterIdentifier"]
            engine = cluster["Engine"]
            region = self.client.meta.region_name
            copy_tags_enabled = cluster.get("CopyTagsToSnapshot", False)

            if (self.findings["rds_cluster_copy_tags_to_snapshots"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and not copy_tags_enabled):
                self.findings["rds_cluster_copy_tags_to_snapshots"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_cluster_copy_tags_to_snapshots"]["details"].append({
                "db_cluster_id": cluster_id,
                "engine": engine,
                "region": region,
                "copy_tags_enabled": copy_tags_enabled,
                "compliance": copy_tags_enabled
            })

    async def check_rds_instance_copy_tags_to_snapshots(self):
        """
        RDS instances have the CopyTagsToSnapshot setting enabled
        """
        for instance in self.instances["DBInstances"]:
            db_instance_id = instance["DBInstanceIdentifier"]
            engine = instance["Engine"]
            region = self.client.meta.region_name
            copy_tags_enabled = instance.get("CopyTagsToSnapshot", False)

            if (self.findings["rds_instance_copy_tags_to_snapshots"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and not copy_tags_enabled):
                self.findings["rds_instance_copy_tags_to_snapshots"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_instance_copy_tags_to_snapshots"]["details"].append({
                "db_instance_id": db_instance_id,
                "engine": engine,
                "region": region,
                "copy_tags_enabled": copy_tags_enabled,
                "compliance": copy_tags_enabled
            })

    async def check_rds_snapshot_privacy(self):
        """
        RDS snapshots are not publicly accessible. Public snapshots can be accessed by any AWS account, leading to
        potential data breaches
        """
        for snapshot in self.db_snapshots["DBSnapshots"]:
            snapshot_id = snapshot["DBSnapshotIdentifier"]
            engine = snapshot["Engine"]
            region = self.client.meta.region_name

            attributes = await self.client.describe_db_snapshot_attributes(DBSnapshotIdentifier=snapshot_id)
            public_snapshot = any(attr["AttributeValues"] for attr in attributes["DBSnapshotAttributesResult"]["DBSnapshotAttributes"]
                                  if attr["AttributeName"] == "restore")

            if (self.findings["rds_snapshot_privacy"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and public_snapshot):
                self.findings["rds_snapshot_privacy"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_snapshot_privacy"]["details"].append({
                "snapshot_id": snapshot_id,
                "engine": engine,
                "region": region,
                "public_snapshot": public_snapshot,
                "compliance": not public_snapshot
            })

    async def check_rds_instance_public_access(self):
        """
        RDS instances are not publicly accessible
        """
        for instance in self.instances["DBInstances"]:
            db_instance_id = instance["DBInstanceIdentifier"]
            engine = instance["Engine"]
            region = self.client.meta.region_name
            publicly_accessible = instance.get("PubliclyAccessible", False)

            if (self.findings["rds_instance_public_access"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and publicly_accessible):
                self.findings["rds_instance_public_access"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_instance_public_access"]["details"].append({
                "db_instance_id": db_instance_id,
                "engine": engine,
                "region": region,
                "publicly_accessible": publicly_accessible,
                "compliance": not publicly_accessible
            })

    async def check_rds_auto_minor_version_upgrade(self):
        """
        RDS instances have automatic minor version upgrades enabled
        """
        for instance in self.instances["DBInstances"]:
            db_instance_id = instance["DBInstanceIdentifier"]
            engine = instance["Engine"]
            region = self.client.meta.region_name
            auto_minor_version_upgrade = instance.get("AutoMinorVersionUpgrade", False)

            if (self.findings["rds_auto_minor_version_upgrade"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and not auto_minor_version_upgrade):
                self.findings["rds_auto_minor_version_upgrade"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_auto_minor_version_upgrade"]["details"].append({
                "db_instance_id": db_instance_id,
                "engine": engine,
                "region": region,
                "auto_minor_version_upgrade": auto_minor_version_upgrade,
                "compliance": auto_minor_version_upgrade
            })

    async def check_rds_instance_vpc_deployment(self):
        """
        all RDS instances are inside a Virtual Private Cloud (VPC). Running an RDS instance outside a VPC means it uses
        the EC2-Classic network, which lacks modern security features like private subnets, security groups, and NACLs
        """
        for instance in self.instances["DBInstances"]:
            db_instance_id = instance["DBInstanceIdentifier"]
            engine = instance["Engine"]
            region = self.client.meta.region_name
            in_vpc = "DBSubnetGroup" in instance and instance["DBSubnetGroup"].get("VpcId") is not None

            if (self.findings["rds_instance_vpc_deployment"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and not in_vpc):
                self.findings["rds_instance_vpc_deployment"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_instance_vpc_deployment"]["details"].append({
                "db_instance_id": db_instance_id,
                "engine": engine,
                "region": region,
                "in_vpc": in_vpc,
                "compliance": in_vpc
            })

    async def check_rds_iam_authentication(self):
        for instance in self.instances["DBInstances"]:
            db_instance_id = instance["DBInstanceIdentifier"]
            engine = instance["Engine"]
            region = self.client.meta.region_name
            iam_auth_enabled = instance.get("IAMDatabaseAuthenticationEnabled", False)

            if (self.findings["rds_iam_authentication"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and not iam_auth_enabled):
                self.findings["rds_iam_authentication"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_iam_authentication"]["details"].append({
                "db_instance_id": db_instance_id,
                "engine": engine,
                "region": region,
                "iam_auth_enabled": iam_auth_enabled,
                "compliance": iam_auth_enabled
            })

    async def check_rds_automatic_backups(self):
        """
        RDS instances have automated backups configured
        """
        for instance in self.instances["DBInstances"]:
            db_instance_id = instance["DBInstanceIdentifier"]
            engine = instance["Engine"]
            region = self.client.meta.region_name
            backup_retention_period = instance.get("BackupRetentionPeriod", 0)

            backups_enabled = backup_retention_period > 0

            if (self.findings["rds_automatic_backups"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and not backups_enabled):
                self.findings["rds_automatic_backups"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_automatic_backups"]["details"].append({
                "db_instance_id": db_instance_id,
                "engine": engine,
                "region": region,
                "backup_retention_period": backup_retention_period,
                "backups_enabled": backups_enabled,
                "compliance": backups_enabled
            })

    async def check_rds_cluster_iam_authentication(self):
        """
        IAM database authentication is enabled for RDS clusters
        """
        for cluster in self.clusters["DBClusters"]:
            cluster_id = cluster["DBClusterIdentifier"]
            engine = cluster["Engine"]
            region = self.client.meta.region_name
            iam_auth_enabled = cluster.get("IAMDatabaseAuthenticationEnabled", False)

            if (self.findings["rds_cluster_iam_authentication"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and not iam_auth_enabled):
                self.findings["rds_cluster_iam_authentication"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_cluster_iam_authentication"]["details"].append({
                "db_cluster_id": cluster_id,
                "engine": engine,
                "region": region,
                "iam_auth_enabled": iam_auth_enabled,
                "compliance": iam_auth_enabled
            })

    async def check_aurora_cluster_backtracking(self):
        """
        Aurora clusters have the backtracking feature turned on
        """
        for cluster in self.clusters["DBClusters"]:
            cluster_id = cluster["DBClusterIdentifier"]
            engine = cluster["Engine"]
            region = self.client.meta.region_name
            backtrack_window = cluster.get("BacktrackWindow", 0)

            backtracking_enabled = backtrack_window > 0

            if (self.findings["aurora_cluster_backtracking"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and not backtracking_enabled):
                self.findings["aurora_cluster_backtracking"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["aurora_cluster_backtracking"]["details"].append({
                "db_cluster_id": cluster_id,
                "engine": engine,
                "region": region,
                "backtrack_window": backtrack_window,
                "backtracking_enabled": backtracking_enabled,
                "compliance": backtracking_enabled
            })

    async def check_rds_cluster_multi_az(self):
        """
        RDS clusters are deployed across multiple Availability Zones (AZs)
        """
        for cluster in self.clusters["DBClusters"]:
            cluster_id = cluster["DBClusterIdentifier"]
            engine = cluster["Engine"]
            region = self.client.meta.region_name
            multi_az = cluster.get("MultiAZ", False)

            if (self.findings["rds_cluster_multi_az"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and not multi_az):
                self.findings["rds_cluster_multi_az"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_cluster_multi_az"]["details"].append({
                "db_cluster_id": cluster_id,
                "engine": engine,
                "region": region,
                "multi_az": multi_az,
                "compliance": multi_az
            })

    async def check_rds_cluster_encryption(self):
        """
        RDS clusters have encryption enabled to protect data at rest
        """
        for cluster in self.clusters["DBClusters"]:
            cluster_id = cluster["DBClusterIdentifier"]
            engine = cluster["Engine"]
            region = self.client.meta.region_name
            encrypted = cluster.get("StorageEncrypted", False)

            if (self.findings["rds_cluster_encryption"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and not encrypted):
                self.findings["rds_cluster_encryption"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_cluster_encryption"]["details"].append({
                "db_cluster_id": cluster_id,
                "engine": engine,
                "region": region,
                "encrypted": encrypted,
                "compliance": encrypted
            })

    async def check_rds_instance_encryption(self):
        """
        RDS instances have encryption enabled to protect stored data from unauthorized access
        """
        for instance in self.instances["DBInstances"]:
            db_instance_id = instance["DBInstanceIdentifier"]
            engine = instance["Engine"]
            region = self.client.meta.region_name
            encrypted = instance.get("StorageEncrypted", False)

            if (self.findings["rds_instance_encryption"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and not encrypted):
                self.findings["rds_instance_encryption"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_instance_encryption"]["details"].append({
                "db_instance_id": db_instance_id,
                "engine": engine,
                "region": region,
                "encrypted": encrypted,
                "compliance": encrypted
            })

    async def check_aurora_mysql_audit_logging(self):
        """
        Audit logging is enabled for Amazon Aurora MySQL clusters and logs are sent to CloudWatch Logs for monitoring
        and security analysis
        """
        for cluster in self.clusters["DBClusters"]:
            cluster_id = cluster["DBClusterIdentifier"]
            engine = cluster["Engine"]
            region = self.client.meta.region_name
            logs_exported = cluster.get("EnabledCloudwatchLogsExports", [])

            audit_logging_enabled = "audit" in logs_exported

            if (self.findings["aurora_mysql_audit_logging"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and not audit_logging_enabled):
                self.findings["aurora_mysql_audit_logging"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["aurora_mysql_audit_logging"]["details"].append({
                "db_cluster_id": cluster_id,
                "engine": engine,
                "region": region,
                "audit_logging_enabled": audit_logging_enabled,
                "logs_exported": logs_exported,
                "compliance": audit_logging_enabled
            })

    async def check_rds_cluster_auto_minor_version_upgrade(self):
        """
        RDS clusters are configured to receive automatic minor version updates for improved security, performance, and
        bug fixes
        """
        for cluster in self.clusters["DBClusters"]:
            cluster_id = cluster["DBClusterIdentifier"]
            engine = cluster["Engine"]
            region = self.client.meta.region_name
            auto_minor_version_upgrade = cluster.get("AutoMinorVersionUpgrade", False)

            if (self.findings["rds_cluster_auto_minor_version_upgrade"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and not auto_minor_version_upgrade):
                self.findings["rds_cluster_auto_minor_version_upgrade"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_cluster_auto_minor_version_upgrade"]["details"].append({
                "db_cluster_id": cluster_id,
                "engine": engine,
                "region": region,
                "auto_minor_version_upgrade": auto_minor_version_upgrade,
                "compliance": auto_minor_version_upgrade
            })

    async def check_rds_postgresql_logging(self):
        """
        PostgreSQL RDS / Aurora instances send logs to CloudWatch Logs for monitoring, security analysis, and troubleshooting
        """
        for instance in self.instances["DBInstances"]:
            db_instance_id = instance["DBInstanceIdentifier"]
            engine = instance["Engine"]
            region = self.client.meta.region_name
            logs_exported = instance.get("EnabledCloudwatchLogsExports", [])

            # PostgreSQL logs should be exported to CloudWatch
            logging_enabled = any(log in logs_exported for log in ["postgresql", "upgrade"])

            if (self.findings["rds_postgresql_logging"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and not logging_enabled):
                self.findings["rds_postgresql_logging"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_postgresql_logging"]["details"].append({
                "db_instance_id": db_instance_id,
                "engine": engine,
                "region": region,
                "logging_enabled": logging_enabled,
                "logs_exported": logs_exported,
                "compliance": logging_enabled
            })

    async def check_rds_postgresql_encryption_in_transit(self):
        """
        SSL/TLS encryption is enforced for PostgreSQL RDS instances, protecting data from unauthorized access
        during transmission
        """
        for instance in self.instances["DBInstances"]:
            db_instance_id = instance["DBInstanceIdentifier"]
            engine = instance["Engine"]
            region = self.client.meta.region_name
            parameter_groups = instance.get("DBParameterGroups", [])

            ssl_enforced = False

            for group in parameter_groups:
                param_group_name = group["DBParameterGroupName"]
                params = await self.client.describe_db_parameters(DBParameterGroupName=param_group_name)

                for param in params.get("Parameters", []):
                    if param["ParameterName"] == "rds.force_ssl" and param["ParameterValue"] == "1":
                        ssl_enforced = True
                        break

                if ssl_enforced:
                    break

            if (self.findings["rds_postgresql_encryption_in_transit"][
                "status"] == ResourceComplianceStatusEnum.PASS.value
                    and not ssl_enforced):
                self.findings["rds_postgresql_encryption_in_transit"][
                    "status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_postgresql_encryption_in_transit"]["details"].append({
                "db_instance_id": db_instance_id,
                "engine": engine,
                "region": region,
                "ssl_enforced": ssl_enforced,
                "parameter_groups": [pg["DBParameterGroupName"] for pg in parameter_groups],
                "compliance": ssl_enforced
            })

    async def check_rds_snapshot_encryption(self):
        """
        RDS snapshots (both cluster and instance snapshots) are encrypted to protect data from unauthorized access
        """
        # Check individual RDS DB instance snapshots
        for snapshot in self.db_snapshots.get("DBSnapshots", []):
            snapshot_id = snapshot["DBSnapshotIdentifier"]
            db_instance_id = snapshot["DBInstanceIdentifier"]
            region = self.client.meta.region_name
            encrypted = snapshot.get("Encrypted", False)

            if (self.findings["rds_snapshot_encryption"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and not encrypted):
                self.findings["rds_snapshot_encryption"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_snapshot_encryption"]["details"].append({
                "snapshot_id": snapshot_id,
                "db_instance_id": db_instance_id,
                "region": region,
                "encrypted": encrypted,
                "compliance": encrypted
            })

        # Check Aurora DB cluster snapshots
        cluster_snapshots = await self.client.describe_db_cluster_snapshots()
        for snapshot in cluster_snapshots.get("DBClusterSnapshots", []):
            snapshot_id = snapshot["DBClusterSnapshotIdentifier"]
            cluster_id = snapshot["DBClusterIdentifier"]
            region = self.client.meta.region_name
            encrypted = snapshot.get("Encrypted", False)

            if (self.findings["rds_snapshot_encryption"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and not encrypted):
                self.findings["rds_snapshot_encryption"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_snapshot_encryption"]["details"].append({
                "snapshot_id": snapshot_id,
                "db_cluster_id": cluster_id,
                "region": region,
                "encrypted": encrypted,
                "compliance": encrypted
            })

    async def check_rds_multi_az(self):
        """
        RDS instances are deployed in a Multi-AZ configuration for high availability and failover support
        """
        for instance in self.instances["DBInstances"]:
            db_instance_id = instance["DBInstanceIdentifier"]
            engine = instance["Engine"]
            region = self.client.meta.region_name
            multi_az = instance.get("MultiAZ", False)

            if (self.findings["rds_multi_az"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and not multi_az):
                self.findings["rds_multi_az"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_multi_az"]["details"].append({
                "db_instance_id": db_instance_id,
                "engine": engine,
                "region": region,
                "multi_az": multi_az,
                "compliance": multi_az
            })

    async def check_rds_event_notifications(self):
        """
        RDS event subscriptions are set up to notify administrators about critical cluster events
        (e.g., failovers, maintenance, instance crashes)
        """
        event_subscriptions = await self.client.describe_event_subscriptions()
        region = self.client.meta.region_name

        critical_events = {"failover", "maintenance", "configuration change", "deletion"}
        has_critical_subscription = False

        for subscription in event_subscriptions.get("EventSubscriptionsList", []):
            source_type = subscription.get("SourceType", "")
            event_categories = set(subscription.get("EventCategoriesList", []))

            if source_type == "db-cluster" and critical_events.intersection(event_categories):
                has_critical_subscription = True
                break

        if (self.findings["rds_event_notifications"]["status"] == ResourceComplianceStatusEnum.PASS.value
                and not has_critical_subscription):
            self.findings["rds_event_notifications"]["status"] = ResourceComplianceStatusEnum.FAIL.value

        self.findings["rds_event_notifications"]["details"].append({
            "region": region,
            "has_critical_subscription": has_critical_subscription,
            "compliance": has_critical_subscription
        })

    async def check_rds_instance_event_notifications(self):
        """
        RDS event subscriptions are properly set up to notify administrators about critical DB instance events
        (e.g., failovers, maintenance, crashes)
        """
        event_subscriptions = await self.client.describe_event_subscriptions()
        region = self.client.meta.region_name

        critical_events = {"failover", "maintenance", "configuration change", "deletion"}
        has_critical_subscription = False

        for subscription in event_subscriptions.get("EventSubscriptionsList", []):
            source_type = subscription.get("SourceType", "")
            event_categories = set(subscription.get("EventCategoriesList", []))

            if source_type == "db-instance" and critical_events.intersection(event_categories):
                has_critical_subscription = True
                break

        if (self.findings["rds_instance_event_notifications"]["status"] == ResourceComplianceStatusEnum.PASS.value
                and not has_critical_subscription):
            self.findings["rds_instance_event_notifications"]["status"] = ResourceComplianceStatusEnum.FAIL.value

        self.findings["rds_instance_event_notifications"]["details"].append({
            "region": region,
            "has_critical_subscription": has_critical_subscription,
            "compliance": has_critical_subscription
        })

    async def check_rds_parameter_group_event_notifications(self):
        """
        RDS event subscriptions are properly set up to notify administrators about critical parameter group events
        (e.g., modifications, changes that require reboot, etc.)
        """
        event_subscriptions = await self.client.describe_event_subscriptions()
        region = self.client.meta.region_name

        critical_events = {"configuration change", "pending reboot"}
        has_critical_subscription = False

        for subscription in event_subscriptions.get("EventSubscriptionsList", []):
            source_type = subscription.get("SourceType", "")
            event_categories = set(subscription.get("EventCategoriesList", []))

            if source_type == "db-parameter-group" and critical_events.intersection(event_categories):
                has_critical_subscription = True
                break

        if (self.findings["rds_parameter_group_event_notifications"]["status"] ==
                ResourceComplianceStatusEnum.PASS.value and not has_critical_subscription):
            self.findings["rds_parameter_group_event_notifications"]["status"] = ResourceComplianceStatusEnum.FAIL.value

        self.findings["rds_parameter_group_event_notifications"]["details"].append({
            "region": region,
            "has_critical_subscription": has_critical_subscription,
            "compliance": has_critical_subscription
        })

    async def check_rds_security_group_event_notifications(self):
        """
        RDS event subscriptions are set up to notify administrators about critical security group events, such as:
        - Changes to security group rules
        - Security group deletion
        """
        event_subscriptions = await self.client.describe_event_subscriptions()
        region = self.client.meta.region_name

        critical_events = {"configuration change", "deletion"}
        has_critical_subscription = False

        for subscription in event_subscriptions.get("EventSubscriptionsList", []):
            source_type = subscription.get("SourceType", "")
            event_categories = set(subscription.get("EventCategoriesList", []))

            if source_type == "db-security-group" and critical_events.intersection(event_categories):
                has_critical_subscription = True
                break

        if (self.findings["rds_security_group_event_notifications"]["status"] == ResourceComplianceStatusEnum.PASS.value
                and not has_critical_subscription):
            self.findings["rds_security_group_event_notifications"]["status"] = ResourceComplianceStatusEnum.FAIL.value

        self.findings["rds_security_group_event_notifications"]["details"].append({
            "region": region,
            "has_critical_subscription": has_critical_subscription,
            "compliance": has_critical_subscription
        })

    async def check_rds_cluster_deletion_protection(self):
        """
        Amazon RDS clusters have deletion protection enabled to prevent accidental or malicious deletions
        """
        clusters = await self.client.describe_db_clusters()
        region = self.client.meta.region_name

        for cluster in clusters.get("DBClusters", []):
            cluster_id = cluster["DBClusterIdentifier"]
            deletion_protection = cluster.get("DeletionProtection", False)

            if (self.findings["rds_cluster_deletion_protection"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and not deletion_protection):
                self.findings["rds_cluster_deletion_protection"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_cluster_deletion_protection"]["details"].append({
                "db_cluster_id": cluster_id,
                "deletion_protection": deletion_protection,
                "region": region,
                "compliance": deletion_protection
            })

    async def check_rds_instance_deletion_protection(self):
        """
        Amazon RDS instances have deletion protection enabled to prevent accidental or unauthorized deletions
        """
        region = self.client.meta.region_name

        for instance in self.instances.get("DBInstances", []):
            db_instance_id = instance["DBInstanceIdentifier"]
            deletion_protection = instance.get("DeletionProtection", False)

            if (self.findings["rds_instance_deletion_protection"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and not deletion_protection):
                self.findings["rds_instance_deletion_protection"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_instance_deletion_protection"]["details"].append({
                "db_instance_id": db_instance_id,
                "deletion_protection": deletion_protection,
                "region": region,
                "compliance": deletion_protection
            })

    async def check_rds_instance_logs_to_cloudwatch(self):
        """
        Amazon RDS instances are configured to send database logs to Amazon CloudWatch Logs for monitoring, auditing,
        and troubleshooting
        """
        region = self.client.meta.region_name
        for instance in self.instances.get("DBInstances", []):
            db_instance_id = instance["DBInstanceIdentifier"]
            log_config = instance.get("CloudwatchLogsExportConfiguration", {})
            enabled_logs = log_config.get("EnableLogTypes", [])

            has_logging_enabled = bool(enabled_logs)

            if (self.findings["rds_instance_logs_to_cloudwatch"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and not has_logging_enabled):
                self.findings["rds_instance_logs_to_cloudwatch"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_instance_logs_to_cloudwatch"]["details"].append({
                "db_instance_id": db_instance_id,
                "enabled_logs": enabled_logs,
                "region": region,
                "has_logging_enabled": has_logging_enabled,
                "compliance": has_logging_enabled
            })

    async def check_rds_cluster_custom_admin_username(self):
        """
        Amazon RDS clusters do not use the default administrative usernames (such as admin, root, postgres, sqladmin)
        """
        clusters = await self.client.describe_db_clusters()
        region = self.client.meta.region_name

        for cluster in clusters.get("DBClusters", []):
            db_cluster_id = cluster["DBClusterIdentifier"]
            master_username = cluster["MasterUsername"]
            engine = cluster["Engine"]

            using_default_admin = master_username.lower() in DEFAULT_RDS_ADMIN_USERNAMES

            if (self.findings["rds_cluster_custom_admin"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and using_default_admin):
                self.findings["rds_cluster_custom_admin"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            self.findings["rds_cluster_custom_admin"]["details"].append({
                "db_cluster_id": db_cluster_id,
                "master_username": master_username,
                "engine": engine,
                "region": region,
                "using_default_admin": using_default_admin,
                "compliance": not using_default_admin
            })

    def get_check_functions(self):
        return [
            self.check_rds_custom_admin_username,
            self.check_rds_mysql_encryption_in_transit,
            self.check_rds_enhanced_monitoring,
            self.check_rds_custom_port,
            self.check_rds_cluster_copy_tags_to_snapshots,
            self.check_rds_instance_copy_tags_to_snapshots,
            self.check_rds_snapshot_privacy,
            self.check_rds_instance_public_access,
            self.check_rds_auto_minor_version_upgrade,
            self.check_rds_instance_vpc_deployment,
            self.check_rds_iam_authentication,
            self.check_rds_automatic_backups,
            self.check_rds_cluster_iam_authentication,
            self.check_aurora_cluster_backtracking,
            self.check_rds_cluster_multi_az,
            self.check_rds_cluster_encryption,
            self.check_rds_instance_encryption,
            self.check_aurora_mysql_audit_logging,
            self.check_rds_cluster_auto_minor_version_upgrade,
            self.check_rds_postgresql_logging,
            self.check_rds_postgresql_encryption_in_transit,
            self.check_rds_snapshot_encryption,
            self.check_rds_multi_az,
            self.check_rds_event_notifications,
            self.check_rds_instance_event_notifications,
            self.check_rds_parameter_group_event_notifications,
            self.check_rds_security_group_event_notifications,
            self.check_rds_cluster_deletion_protection,
            self.check_rds_instance_deletion_protection,
            self.check_rds_instance_logs_to_cloudwatch,
            self.check_rds_cluster_custom_admin_username
        ]

    async def run_checks(self):
        for region in self.regions:
            logger.info(f"Scanning region: {region}")
            session = self.get_session(region)
            if not await self.is_region_accessible():
                logger.warning(f"Region {region} is not accessible. Skipping checks.")
                continue
            async with session.client(AWSServiceNameEnum.RDS.value, region_name=region) as client:
                self.client = client
                self.instances = await self.client.describe_db_instances()
                self.clusters = await self.client.describe_db_clusters()
                self.db_snapshots = await self.client.describe_db_snapshots()

                for check_function in self.get_check_functions():
                    await check_function()
            await asyncio.sleep(5)

        return self.findings
