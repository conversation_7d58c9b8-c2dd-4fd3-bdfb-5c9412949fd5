import asyncio
from celery.utils.log import get_task_logger
from ..config import BaseChecksProcessor
from app.common import (AWSServiceNameEnum, SeverityEnum, ResourceComplianceStatusEnum,
                        ElastiCacheChecksDescriptionEnum)

__all__ = ['ChecksProcessor']

logger = get_task_logger(__name__)


class ChecksProcessor(BaseChecksProcessor):
    def __init__(self, credentials, regions=[]):
        super().__init__(credentials, regions)
        self.clusters = None
        self.replication_groups = None
        self.findings = {
            "automatic_backups_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.AUTOMATIC_BACKUPS_ENABLED.value,
                "severity": SeverityEnum.HIGH.value
            },
            "automatic_minor_version_upgrades_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.AUTOMATIC_MINOR_VERSION_UPGRADES_ENABLED.value,
                "severity": SeverityEnum.HIGH.value
            },
            "automatic_failover_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.AUTOMATIC_FAILOVER_ENABLED.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "encrypted_at_rest": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.ENCRYPTED_AT_REST.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "encrypted_in_transit": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.ENCRYPTED_IN_TRANSIT.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "redis_auth_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.REDIS_AUTH_ENABLED.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "non_default_subnet_group": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ElastiCacheChecksDescriptionEnum.NON_DEFAULT_SUBNET_GROUP.value,
                "severity": SeverityEnum.HIGH.value
            }
        }

    async def check_automatic_backups_enabled(self):
        """
        ElastiCache (Redis OSS) clusters should have automatic backups enabled.
        """
        all_compliant = True  # Track overall compliance status

        for cluster in self.clusters.get("CacheClusters", []):
            if cluster["Engine"] == "redis":
                snapshot_retention_limit = cluster.get("SnapshotRetentionLimit", 0)
                backups_enabled = snapshot_retention_limit > 0

                self.findings["automatic_backups_enabled"]["details"].append({
                    "cluster_id": cluster["CacheClusterId"],
                    "region": self.client.meta.region_name,
                    "backups_enabled": backups_enabled,
                    "snapshot_retention_limit": snapshot_retention_limit,
                    "compliance": backups_enabled
                })

                if not backups_enabled:
                    all_compliant = False

        # Set the main status for the check
        if self.findings["automatic_backups_enabled"]["status"] and not all_compliant:
            self.findings["automatic_backups_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_automatic_minor_version_upgrades_enabled(self):
        """
        ElastiCache clusters should have automatic minor version upgrades enabled.
        """
        all_compliant = True  # Track overall compliance status

        for cluster in self.clusters.get("CacheClusters", []):
            auto_minor_version_upgrade = cluster.get("AutoMinorVersionUpgrade", False)

            self.findings["automatic_minor_version_upgrades_enabled"]["details"].append({
                "cluster_id": cluster["CacheClusterId"],
                "region": self.client.meta.region_name,
                "auto_minor_version_upgrade": auto_minor_version_upgrade,
                "compliance": auto_minor_version_upgrade
            })

            if not auto_minor_version_upgrade:
                all_compliant = False

        # Set the main status for the check
        if self.findings["automatic_minor_version_upgrades_enabled"]["status"] and not all_compliant:
            self.findings["automatic_minor_version_upgrades_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_automatic_failover_enabled(self):
        """
        ElastiCache replication groups should have automatic failover enabled.
        """
        all_compliant = True  # Track overall compliance status

        for group in self.replication_groups.get("ReplicationGroups", []):
            automatic_failover_status = group.get("AutomaticFailover", "disabled")
            failover_enabled = automatic_failover_status == "enabled"

            self.findings["automatic_failover_enabled"]["details"].append({
                "replication_group_id": group["ReplicationGroupId"],
                "region": self.client.meta.region_name,
                "automatic_failover_status": automatic_failover_status,
                "compliance": failover_enabled
            })

            if not failover_enabled:
                all_compliant = False

        # Set the main status for the check
        if self.findings["automatic_failover_enabled"]["status"] and not all_compliant:
            self.findings["automatic_failover_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_encrypted_at_rest(self):
        """
        ElastiCache replication groups should be encrypted at rest.
        """
        all_compliant = True  # Track overall compliance status

        for group in self.replication_groups.get("ReplicationGroups", []):
            at_rest_encryption_enabled = group.get("AtRestEncryptionEnabled", False)

            self.findings["encrypted_at_rest"]["details"].append({
                "replication_group_id": group["ReplicationGroupId"],
                "region": self.client.meta.region_name,
                "at_rest_encryption_enabled": at_rest_encryption_enabled,
                "compliance": at_rest_encryption_enabled
            })

            if not at_rest_encryption_enabled:
                all_compliant = False

        # Set the main status for the check
        if self.findings["encrypted_at_rest"]["status"] and not all_compliant:
            self.findings["encrypted_at_rest"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_encrypted_in_transit(self):
        """
        ElastiCache replication groups should be encrypted in transit.
        """
        all_compliant = True  # Track overall compliance status

        for group in self.replication_groups.get("ReplicationGroups", []):
            transit_encryption_enabled = group.get("TransitEncryptionEnabled", False)

            self.findings["encrypted_in_transit"]["details"].append({
                "replication_group_id": group["ReplicationGroupId"],
                "region": self.client.meta.region_name,
                "transit_encryption_enabled": transit_encryption_enabled,
                "compliance": transit_encryption_enabled
            })

            if not transit_encryption_enabled:
                all_compliant = False

        # Set the main status for the check
        if self.findings["encrypted_in_transit"]["status"] and not all_compliant:
            self.findings["encrypted_in_transit"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_redis_auth_enabled(self):
        """
        ElastiCache (Redis OSS) replication groups of earlier versions should have Redis OSS AUTH enabled.
        Redis AUTH is particularly important for Redis 5.x versions as they don't have the more
        advanced security features of Redis 6.x and later.
        """
        all_compliant = True  # Track overall compliance status

        for group in self.replication_groups.get("ReplicationGroups", []):
            # Check if the replication group is Redis OSS and version 5.x
            # Redis 6.x and later have more advanced authentication mechanisms
            if group.get("Engine") == "redis" and group.get("EngineVersion", "").startswith("5"):
                auth_token_enabled = group.get("AuthTokenEnabled", False)

                self.findings["redis_auth_enabled"]["details"].append({
                    "replication_group_id": group["ReplicationGroupId"],
                    "region": self.client.meta.region_name,
                    "auth_token_enabled": auth_token_enabled,
                    "engine_version": group.get("EngineVersion", ""),
                    "compliance": auth_token_enabled
                })

                if not auth_token_enabled:
                    all_compliant = False

        # Set the main status for the check
        if self.findings["redis_auth_enabled"]["status"] and not all_compliant:
            self.findings["redis_auth_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_non_default_subnet_group(self):
        """
        ElastiCache clusters should not use the default subnet group.
        """
        all_compliant = True  # Track overall compliance status

        for cluster in self.clusters.get("CacheClusters", []):
            subnet_group_name = cluster.get("CacheSubnetGroupName", "default")
            non_default_subnet_group = subnet_group_name != "default"

            self.findings["non_default_subnet_group"]["details"].append({
                "cluster_id": cluster["CacheClusterId"],
                "region": self.client.meta.region_name,
                "subnet_group_name": subnet_group_name,
                "non_default_subnet_group": non_default_subnet_group,
                "compliance": non_default_subnet_group
            })

            if not non_default_subnet_group:
                all_compliant = False

        # Set the main status for the check
        if self.findings["non_default_subnet_group"]["status"] and not all_compliant:
            self.findings["non_default_subnet_group"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    def get_check_functions(self):
        return [
            self.check_automatic_backups_enabled,
            self.check_automatic_minor_version_upgrades_enabled,
            self.check_automatic_failover_enabled,
            self.check_encrypted_at_rest,
            self.check_encrypted_in_transit,
            self.check_redis_auth_enabled,
            self.check_non_default_subnet_group
        ]

    async def run_checks(self):
        for region in self.regions:
            logger.info(f"Scanning region: {region}")
            session = self.get_session(region)
            if not await self.is_region_accessible():
                logger.warning(f"Region {region} is not accessible. Skipping checks.")
                continue
            async with session.client(AWSServiceNameEnum.ElastiCache.value, region_name=region) as client:
                self.client = client
                self.clusters = await self.client.describe_cache_clusters(ShowCacheNodeInfo=False)
                self.replication_groups = await self.client.describe_replication_groups()

                for check_function in self.get_check_functions():
                    await check_function()
            await asyncio.sleep(5)

        return self.findings
