import asyncio
from celery.utils.log import get_task_logger
from ..config import BaseChecksProcessor
from app.common import (AWSServiceNameEnum, SeverityEnum, ResourceComplianceStatusEnum,
                        ECSChecksDescriptionEnum)

__all__ = ['ChecksProcessor']

logger = get_task_logger(__name__)


class ChecksProcessor(BaseChecksProcessor):
    def __init__(self, credentials, regions=[]):
        super().__init__(credentials, regions)
        self.task_definitions = None
        self.clusters = None
        self.replication_groups = None
        self.findings = {
            "secure_networking_modes": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ECSChecksDescriptionEnum.SECURE_NETWORKING_MODES.value,
                "severity": SeverityEnum.HIGH.value
            },
            "latest_farget_platform_version": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ECSChecksDescriptionEnum.LATEST_FARGATE_PLATFORM_VERSION.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "container_insights_enabled": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ECSChecksDescriptionEnum.CONTAINER_INSIGHTS_ENABLED.value,
                "severity": SeverityEnum.MEDIUM.value
            },
            "no_public_ip_assignment_task_sets": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ECSChecksDescriptionEnum.NO_PUBLIC_IP_ASSIGNMENT_TASK_SETS.value,
                "severity": SeverityEnum.HIGH.value
            },
            "no_public_ip_assignment_services": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ECSChecksDescriptionEnum.NO_PUBLIC_IP_ASSIGNMENT_SERVICES.value,
                "severity": SeverityEnum.HIGH.value
            },
            "no_host_process_namespace": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ECSChecksDescriptionEnum.NO_HOST_PROCESS_NAMESPACE.value,
                "severity": SeverityEnum.HIGH.value
            },
            "non_privileged_containers": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ECSChecksDescriptionEnum.NON_PRIVILEGED_CONTAINERS.value,
                "severity": SeverityEnum.HIGH.value
            },
            "read_only_root_filesystems": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ECSChecksDescriptionEnum.READ_ONLY_ROOT_FILESYSTEMS.value,
                "severity": SeverityEnum.HIGH.value
            },
            "no_env_variables_for_secrets": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ECSChecksDescriptionEnum.NO_ENV_VARIABLES_FOR_SECRETS.value,
                "severity": SeverityEnum.HIGH.value
            },
            "logging_configuration_required": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ECSChecksDescriptionEnum.LOGGING_CONFIGURATION_REQUIRED.value,
                "severity": SeverityEnum.HIGH.value
            }
        }

    async def check_secure_networking_modes(self):
        """
        Amazon ECS task definitions should have secure networking modes and user definitions.
        """
        all_compliant = True  # Track overall compliance status

        for task_definition_arn in self.task_definitions.get("taskDefinitionArns", []):
            task_definition = await self.client.describe_task_definition(taskDefinition=task_definition_arn)
            network_mode = task_definition.get("taskDefinition", {}).get("networkMode", "")
            task_role_arn = task_definition.get("taskDefinition", {}).get("taskRoleArn", "")

            secure_network_mode = network_mode == "awsvpc"
            has_task_role = bool(task_role_arn)

            compliant = secure_network_mode and has_task_role

            self.findings["secure_networking_modes"]["details"].append({
                "task_definition_arn": task_definition_arn,
                "network_mode": network_mode,
                "task_role_arn": task_role_arn,
                "secure_network_mode": secure_network_mode,
                "has_task_role": has_task_role,
                "compliance": compliant,
                "region": self.client.meta.region_name
            })

            if not compliant:
                all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["secure_networking_modes"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_latest_fargate_platform_version(self):
        """
        ECS Fargate services should run on the latest Fargate platform version.
        """
        all_compliant = True  # Track overall compliance status

        for cluster_arn in self.clusters.get("clusterArns", []):
            services = await self.client.list_services(cluster=cluster_arn)
            for service_arn in services.get("serviceArns", []):
                service = await self.client.describe_services(cluster=cluster_arn, services=[service_arn])
                for svc in service.get("services", []):
                    platform_version = svc.get("platformVersion", "LATEST")
                    is_latest = platform_version == "LATEST"

                    self.findings["latest_farget_platform_version"]["details"].append({
                        "cluster_arn": cluster_arn,
                        "service_arn": service_arn,
                        "platform_version": platform_version,
                        "is_latest": is_latest,
                        "compliance": is_latest,
                        "region": self.client.meta.region_name
                    })

                    if not is_latest:
                        all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["latest_farget_platform_version"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_container_insights_enabled(self):
        """
        ECS clusters should use Container Insights.
        """
        all_compliant = True  # Track overall compliance status

        for cluster_arn in self.clusters.get("clusterArns", []):
            cluster = await self.client.describe_clusters(clusters=[cluster_arn])
            for cl in cluster.get("clusters", []):
                settings = cl.get("settings", [])
                container_insights_enabled = any(
                    setting.get("name") == "containerInsights" and setting.get("value") == "enabled"
                    for setting in settings
                )

                self.findings["container_insights_enabled"]["details"].append({
                    "cluster_arn": cluster_arn,
                    "container_insights_enabled": container_insights_enabled,
                    "compliance": container_insights_enabled,
                    "region": self.client.meta.region_name
                })

                if not container_insights_enabled:
                    all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["container_insights_enabled"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_no_public_ip_assignment_task_sets(self):
        """
        ECS task sets should not automatically assign public IP addresses.
        """
        all_compliant = True  # Track overall compliance status

        for cluster_arn in self.clusters.get("clusterArns", []):
            services = await self.client.list_services(cluster=cluster_arn)
            for service_arn in services.get("serviceArns", []):
                task_sets = await self.client.list_task_sets(cluster=cluster_arn, service=service_arn)
                for task_set_arn in task_sets.get("taskSets", []):
                    task_set = await self.client.describe_task_sets(cluster=cluster_arn, service=service_arn,
                                                                    taskSets=[task_set_arn])
                    for ts in task_set.get("taskSets", []):
                        network_configuration = ts.get("networkConfiguration", {})
                        assign_public_ip = network_configuration.get("awsvpcConfiguration", {}).get("assignPublicIp",
                                                                                                    "DISABLED")
                        no_public_ip = assign_public_ip == "DISABLED"

                        self.findings["no_public_ip_assignment_task_sets"]["details"].append({
                            "cluster_arn": cluster_arn,
                            "service_arn": service_arn,
                            "task_set_arn": task_set_arn,
                            "assign_public_ip": assign_public_ip,
                            "no_public_ip": no_public_ip,
                            "compliance": no_public_ip,
                            "region": self.client.meta.region_name
                        })

                        if not no_public_ip:
                            all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["no_public_ip_assignment_task_sets"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_no_public_ip_assignment_services(self):
        """
        ECS services should not have public IP addresses assigned to them automatically.
        """
        all_compliant = True  # Track overall compliance status

        for cluster_arn in self.clusters.get("clusterArns", []):
            services = await self.client.list_services(cluster=cluster_arn)
            for service_arn in services.get("serviceArns", []):
                service = await self.client.describe_services(cluster=cluster_arn, services=[service_arn])
                for svc in service.get("services", []):
                    network_configuration = svc.get("networkConfiguration", {})
                    assign_public_ip = network_configuration.get("awsvpcConfiguration", {}).get("assignPublicIp",
                                                                                                "DISABLED")
                    no_public_ip = assign_public_ip == "DISABLED"

                    self.findings["no_public_ip_assignment_services"]["details"].append({
                        "cluster_arn": cluster_arn,
                        "service_arn": service_arn,
                        "assign_public_ip": assign_public_ip,
                        "no_public_ip": no_public_ip,
                        "compliance": no_public_ip,
                        "region": self.client.meta.region_name
                    })

                    if not no_public_ip:
                        all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["no_public_ip_assignment_services"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_no_host_process_namespace(self):
        """
        ECS task definitions should not share the host's process namespace.
        """
        all_compliant = True  # Track overall compliance status

        for task_definition_arn in self.task_definitions.get("taskDefinitionArns", []):
            task_definition = await self.client.describe_task_definition(taskDefinition=task_definition_arn)
            pid_mode = task_definition.get("taskDefinition", {}).get("pidMode", "")
            no_host_process_namespace = pid_mode != "host"

            self.findings["no_host_process_namespace"]["details"].append({
                "task_definition_arn": task_definition_arn,
                "pid_mode": pid_mode,
                "no_host_process_namespace": no_host_process_namespace,
                "compliance": no_host_process_namespace,
                "region": self.client.meta.region_name
            })

            if not no_host_process_namespace:
                all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["no_host_process_namespace"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_non_privileged_containers(self):
        """
        ECS containers should run as non-privileged.
        """
        all_compliant = True  # Track overall compliance status

        for task_definition_arn in self.task_definitions.get("taskDefinitionArns", []):
            task_definition = await self.client.describe_task_definition(taskDefinition=task_definition_arn)
            container_definitions = task_definition.get("taskDefinition", {}).get("containerDefinitions", [])

            for container in container_definitions:
                is_privileged = container.get("privileged", False)
                non_privileged = not is_privileged

                self.findings["non_privileged_containers"]["details"].append({
                    "task_definition_arn": task_definition_arn,
                    "container_name": container.get("name"),
                    "is_privileged": is_privileged,
                    "non_privileged": non_privileged,
                    "compliance": non_privileged,
                    "region": self.client.meta.region_name
                })

                if not non_privileged:
                    all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["non_privileged_containers"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_read_only_root_filesystems(self):
        """
        ECS containers should be limited to read-only access to root filesystems.
        """
        all_compliant = True  # Track overall compliance status

        for task_definition_arn in self.task_definitions.get("taskDefinitionArns", []):
            task_definition = await self.client.describe_task_definition(taskDefinition=task_definition_arn)
            container_definitions = task_definition.get("taskDefinition", {}).get("containerDefinitions", [])

            for container in container_definitions:
                readonly_root_filesystem = container.get("readonlyRootFilesystem", False)

                self.findings["read_only_root_filesystems"]["details"].append({
                    "task_definition_arn": task_definition_arn,
                    "container_name": container.get("name"),
                    "readonly_root_filesystem": readonly_root_filesystem,
                    "compliance": readonly_root_filesystem,
                    "region": self.client.meta.region_name
                })

                if not readonly_root_filesystem:
                    all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["read_only_root_filesystems"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_no_env_variables_for_secrets(self):
        """
        Secrets should not be passed as container environment variables.
        """
        all_compliant = True  # Track overall compliance status

        for task_definition_arn in self.task_definitions.get("taskDefinitionArns", []):
            task_definition = await self.client.describe_task_definition(taskDefinition=task_definition_arn)
            container_definitions = task_definition.get("taskDefinition", {}).get("containerDefinitions", [])

            for container in container_definitions:
                environment_variables = container.get("environment", [])
                has_secrets_in_env = any(
                    "secret" in env.get("name", "").lower() or "key" in env.get("name", "").lower()
                    for env in environment_variables
                )

                self.findings["no_env_variables_for_secrets"]["details"].append({
                    "task_definition_arn": task_definition_arn,
                    "container_name": container.get("name"),
                    "environment_variables": environment_variables,
                    "has_secrets_in_env": has_secrets_in_env,
                    "compliance": not has_secrets_in_env,
                    "region": self.client.meta.region_name
                })

                if has_secrets_in_env:
                    all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["no_env_variables_for_secrets"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    async def check_logging_configuration_required(self):
        """
        ECS task definitions should have a logging configuration.
        """
        all_compliant = True  # Track overall compliance status

        for task_definition_arn in self.task_definitions.get("taskDefinitionArns", []):
            task_definition = await self.client.describe_task_definition(taskDefinition=task_definition_arn)
            container_definitions = task_definition.get("taskDefinition", {}).get("containerDefinitions", [])

            for container in container_definitions:
                has_logging_config = "logConfiguration" in container

                self.findings["logging_configuration_required"]["details"].append({
                    "task_definition_arn": task_definition_arn,
                    "container_name": container.get("name"),
                    "has_logging_config": has_logging_config,
                    "compliance": has_logging_config,
                    "region": self.client.meta.region_name
                })

                if not has_logging_config:
                    all_compliant = False

        # Set the main status for the check
        if not all_compliant:
            self.findings["logging_configuration_required"]["status"] = ResourceComplianceStatusEnum.FAIL.value

    def get_check_functions(self):
        return [
            self.check_secure_networking_modes,
            self.check_latest_fargate_platform_version,
            self.check_container_insights_enabled,
            self.check_no_public_ip_assignment_task_sets,
            self.check_no_public_ip_assignment_services,
            self.check_no_host_process_namespace,
            self.check_non_privileged_containers,
            self.check_read_only_root_filesystems,
            self.check_no_env_variables_for_secrets,
            self.check_logging_configuration_required
        ]

    async def run_checks(self):
        for region in self.regions:
            logger.info(f"Scanning region: {region}")
            session = self.get_session(region)
            if not await self.is_region_accessible():
                logger.warning(f"Region {region} is not accessible. Skipping checks.")
                continue
            async with session.client(AWSServiceNameEnum.ECS.value, region_name=region) as client:
                self.client = client
                self.task_definitions = await self.client.list_task_definitions()
                self.clusters = await self.client.list_clusters()

                for check_function in self.get_check_functions():
                    await check_function()
            await asyncio.sleep(5)  # Avoid rate limiting

        return self.findings
