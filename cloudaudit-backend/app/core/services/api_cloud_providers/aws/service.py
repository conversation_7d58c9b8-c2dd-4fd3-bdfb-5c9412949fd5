from ..base import CloudProviderService
from .aws_service_factory import AWSServiceFactory
from app.common import (CloudProviderKeyEnum, AccountExistsException, CloudProviderNameEnum,
                        AWSChildAccountAccessRoleException)
from app.core.models.mysql import check_account_exists, add_user_account


__all__ = ['AWSService']


class AWSService(CloudProviderService):
    def __init__(self, credentials):
        super().__init__(credentials)
        self.service_factory = AWSServiceFactory(credentials)

    async def verify_account_exists(self, conn_pool, message, user_id):
        is_account_exists = await check_account_exists(conn_pool, CloudProviderKeyEnum.AWS_ACCOUNT_ID.value, {
            "cloud_provider_id": message.cloud_provider_id,
            "generic_key_val": self.credentials['aws_account_id']
        })
        if is_account_exists:
            raise AccountExistsException
        return is_account_exists

    async def test_connection(self):
        from app.core.services.api_cloud_providers import CloudProviderConnection
        return await CloudProviderConnection(CloudProviderNameEnum.AWS.value, self.credentials).test_connection()

    async def process_child_accounts(self, conn_pool, message, user_id, add_account_db):
        child_account_ids = await self.service_factory.get_child_account_ids()
        account_counter = 1
        for account_id in child_account_ids:
            try:
                child_account_data = await self.service_factory.get_child_account_temp_credentials(account_id)
            except AWSChildAccountAccessRoleException:
                raise AWSChildAccountAccessRoleException
            message.credentials.update({"role_arn": child_account_data["AssumedRoleUser"]["Arn"]})
            # add account to accounts table
            account_id = await add_account_db(acc_name_prefix=f"child_account_{account_counter}")
            # add user-account to user-accounts table
            await add_user_account(conn_pool, message.user_id, account_id)
            account_counter += 1
