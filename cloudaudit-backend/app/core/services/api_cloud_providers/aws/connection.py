import aioboto3
from app.common.exception import InvalidAWSCredentialsException


async def check_aws_credentials(access_key, secret_key):
    try:
        # Create an STS client using the provided credentials
        session = aioboto3.Session(
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key
        )

        async with session.client('sts') as sts_client:
            # Attempt to call the 'get_caller_identity' API to check if credentials are valid
            response = await sts_client.get_caller_identity()
            await sts_client.close()
            return {"aws_account_id": response['Account']}
    except Exception as e:
        raise InvalidAWSCredentialsException
