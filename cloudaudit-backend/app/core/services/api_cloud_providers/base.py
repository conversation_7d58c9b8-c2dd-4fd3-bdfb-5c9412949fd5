

__all__ = ["CloudProviderService"]


class CloudProviderService:
    def __init__(self, credentials):
        self.credentials = credentials

    async def verify_account_exists(self, conn_pool, message, user_id):
        raise NotImplementedError

    async def test_connection(self):
        raise NotImplementedError

    async def process_child_accounts(self, conn_pool, message, user_id, add_account_db):
        raise NotImplementedError
