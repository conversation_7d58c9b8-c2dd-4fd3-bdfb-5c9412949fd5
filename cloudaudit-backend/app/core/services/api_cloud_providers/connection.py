from app.common.enums import CloudProviderNameEnum
from .aws import check_aws_credentials


__all__ = ['CloudProviderConnection']


class CloudProviderConnection:
    def __init__(self, cloud_provider_name, credentials):
        self.cloud_provider_name = cloud_provider_name
        self.credentials = credentials

    async def test_connection(self):
        if self.cloud_provider_name == CloudProviderNameEnum.AWS.value:
            return await check_aws_credentials(self.credentials['access_key'], self.credentials['secret_key'])
        # elif self.cloud_provider_name == CloudProviderNameEnum.GCP.value:
