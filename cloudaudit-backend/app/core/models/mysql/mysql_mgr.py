import aiomysql

from app import app

__all__ = ["mysql_connection_pool_factory"]


async def mysql_connection_pool_factory(**kwargs):
    """ This method is used to create MySQL pool creation """

    minsize = kwargs.get('minsize', app.config.MYSQL_CONNECTION_POOL_MINIMUM_SIZE)
    maxsize = kwargs.get('maxsize', app.config.MYSQL_CONNECTION_POOL_MAXIMUM_SIZE)
    host = kwargs.get('host', app.config.MYSQL_DATABASE_HOST)
    user = kwargs.get('user', app.config.MYSQL_DATABASE_USER)
    password = kwargs.get('password', app.config.MYSQL_DATABASE_PASSWORD)
    db = kwargs.get('db', app.config.MYSQL_DATABASE_NAME)
    pool_recycle = kwargs.get('pool_recycle', app.config.MYSQL_CONNECTION_MAX_POOL_RECYCLE_TIME)

    return await aiomysql.create_pool(
        minsize=minsize,
        maxsize=maxsize,
        host=host,
        user=user,
        password=password,
        db=db,
        pool_recycle=pool_recycle,
        cursorclass=aiomysql.DictCursor,
        autocommit=True,
    )
