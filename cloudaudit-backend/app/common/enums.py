import os
from enum import Enum

__all__ = ['HttpStatusCodeEnum', 'HttpMethodEnum', 'QueueEnum', 'UserPermissionEnum',
           'CloudProviderNameEnum', 'CloudProviderKeyEnum', 'AWSRegionNameEnum', 'AWSServiceNameEnum',
           'EC2ChecksDescriptionEnum', 'CloudProviderSensitiveKeyEnum', 'ScanStatusEnum', 'SeverityEnum',
           'ResourceComplianceStatusEnum', 'CloudProviderOrganizationIAMRoleARNEnum', 'AWSAssumeRoleCredentialKeysEnum',
           'PredefinedUserRoleEnum', 'RDSChecksDescriptionEnum', 'IAMChecksDescriptionEnum', 'S3ChecksDescriptionEnum',
           'LambdaChecksDescriptionEnum', 'ELBChecksDescriptionEnum', 'ElastiCacheChecksDescriptionEnum',
           'ECSChecksDescriptionEnum', 'EKSChecksDescriptionEnum', 'EFSChecksDescriptionEnum', 'ScanServiceStatusEnum']


class HttpStatusCodeEnum(Enum):
    SUCCESS = 200
    CREATED = 201
    NO_CONTENT = 204
    BAD_REQUEST = 400
    CONFLICT = 409
    RATE_LIMIT = 429
    INTERNAL_SERVER_ERROR = 500
    BAD_GATEWAY = 502
    SERVICE_UNAVAILABLE = 503
    GATEWAY_TIMEOUT = 504


class HttpMethodEnum(Enum):
    POST = 'POST'
    PUT = 'PUT'
    GET = 'GET'
    DELETE = 'DELETE'


class QueueEnum(Enum):
    SCAN_STATUS_UPDATES = {
        'route_key': 'scan-status-updates-handler',
        'queue_name': 'scan-status-updates-handler-queue',
        'exchange_type': 'direct',
        'exchange_name': 'cloud-audit-direct',
        'worker': int(os.environ.get("SCAN_STATUS_UPDATE_HANDLER_QUEUE_WORKER_COUNT", 5)),
        "prefetch_count": int(os.environ.get("SCAN_STATUS_UPDATE_HANDLER_QUEUE_PREFETCH_COUNT", 10))
    }
    SCAN_STATUS_UPDATES_FAILED = {
        'route_key': 'scan-status-updates-handler-failed',
        'queue_name': 'scan-status-updates-handler-queue-failed',
        'exchange_type': 'direct',
        'exchange_name': 'cloud-audit-direct',
        'worker': 0
    }


class UserPermissionEnum(Enum):
    CREATE_ACCOUNT = 'create_account'
    DELETE_ACCOUNT = 'delete_account'
    CREATE_USER = 'create_user'
    DELETE_USER = 'delete_user'
    UPDATE_USER_PERMISSIONS = 'update_user_permissions'
    ASSIGN_ROLE_TO_USER = 'assign_role_to_user'
    REVOKE_ROLE_FROM_USER = 'revoke_role_from_user'
    SCAN_SERVICE = 'scan_service'
    REMEDIATE_FINDINGS = 'remediate_findings'
    CREATE_CUSTOM_ROLE = 'create_custom_role'
    UPDATE_CUSTOM_ROLE = 'update_custom_role'
    DELETE_CUSTOM_ROLE = 'delete_custom_role'
    READ_ONLY = 'read_only'


class PredefinedUserRoleEnum(Enum):
    ADMIN = 1
    AUDITOR = 5
    MODERATOR = 3
    POWER_USER = 2
    REMEDIATOR = 7
    SCANNER = 6
    USER_MANAGER = 4


class CloudProviderNameEnum(Enum):
    AWS = 'aws'
    GCP = 'gcp'
    AZURE = 'azure'


class CloudProviderKeyEnum(Enum):
    AWS = '$.access_key'
    AWS_ACCOUNT_ID = '$.aws_account_id'


class CloudProviderSensitiveKeyEnum(Enum):
    SECRET_KEY = 'secret_key'


class CloudProviderOrganizationIAMRoleARNEnum(Enum):
    AWS = 'arn:aws:iam::#account_id#:role/OrganizationAccountAccessRole'


class AWSRegionNameEnum(Enum):
    US_EAST_1 = 'us-east-1'
    US_EAST_2 = 'us-east-2'
    US_WEST_1 = 'us-west-1'
    US_WEST_2 = 'us-west-2'
    CA_CENTRAL_1 = 'ca-central-1'
    EU_WEST_1 = 'eu-west-1'
    EU_WEST_2 = 'eu-west-2'
    EU_WEST_3 = 'eu-west-3'
    EU_CENTRAL_1 = 'eu-central-1'
    EU_NORTH_1 = 'eu-north-1'
    AP_SOUTHEAST_1 = 'ap-southeast-1'
    AP_SOUTHEAST_2 = 'ap-southeast-2'
    AP_NORTHEAST_1 = 'ap-northeast-1'
    AP_NORTHEAST_2 = 'ap-northeast-2'
    AP_NORTHEAST_3 = 'ap-northeast-3'
    AP_SOUTH_1 = 'ap-south-1'
    SA_EAST_1 = 'sa-east-1'
    ME_SOUTH_1 = 'me-south-1'
    AFRICA_1 = 'af-south-1'
    # US_GOV_WEST_1 = 'us-gov-west-1'
    # US_GOV_EAST_1 = 'us-gov-east-1'
    # US_GOV_CENTRAL_1 = 'us-gov-central-1'
    # CN_NORTH_1 = 'cn-north-1'
    # CN_NORTHWEST_1 = 'cn-northwest-1'

    @property
    def description(self):
        descriptions = {
            "us-east-1": "US East (N. Virginia)",
            "us-east-2": "US East (Ohio)",
            "us-west-1": "US West (N. California)",
            "us-west-2": "US West (Oregon)",
            "ca-central-1": "Canada (Central)",
            "eu-west-1": "EU West (Ireland)",
            "eu-west-2": "EU West (London)",
            "eu-west-3": "EU West (Paris)",
            "eu-central-1": "EU Central (Frankfurt)",
            "eu-north-1": "EU North (Stockholm)",
            "ap-southeast-1": "Asia Pacific (Singapore)",
            "ap-southeast-2": "Asia Pacific (Sydney)",
            "ap-northeast-1": "Asia Pacific (Tokyo)",
            "ap-northeast-2": "Asia Pacific (Seoul)",
            "ap-northeast-3": "Asia Pacific (Osaka)",
            "ap-south-1": "Asia Pacific (Mumbai)",
            "sa-east-1": "South America (São Paulo)",
            "me-south-1": "Middle East (Bahrain)",
            "af-south-1": "Africa (Cape Town)",
            # "us-gov-west-1": "AWS GovCloud (US-West)",
            # "us-gov-east-1": "AWS GovCloud (US-East)",
            # "us-gov-central-1": "AWS GovCloud (US-Central)",
            # "cn-north-1": "China (Beijing)",
            # "cn-northwest-1": "China (Ningxia)",
        }
        return descriptions.get(self.value, self.value)


class AWSServiceNameEnum(Enum):
    S3 = 's3'
    EC2 = 'ec2'
    RDS = 'rds'
    IAM = 'iam'
    Lambda = 'lambda'
    SQS = 'sqs'
    SNS = 'sns'
    DynamoDB = 'dynamodb'
    CloudWatch = 'cloudwatch'
    ECS = 'ecs'
    EKS = 'eks'
    ElastiCache = 'elasticache'
    Route53 = 'route53'
    KMS = 'kms'
    SSM = 'ssm'
    WAF = 'waf'
    STS = 'sts'
    ORGANIZATIONS = 'organizations'
    ELB = 'elb'
    EFS = 'efs'
    BACKUP = 'backup'


class AWSAssumeRoleCredentialKeysEnum(Enum):
    ACCESS_KEY = 'AccessKeyId'
    SECRET_KEY = 'SecretAccessKey'
    SESSION_TOKEN = 'SessionToken'


class EC2ChecksDescriptionEnum(Enum):
    EBS_ENCRYPTION = "Checks if EBS volumes are encrypted"
    IMDSV2 = "Checks if EC2 instances are using IMDSv2 for enhanced metadata protection"
    PUBLIC_ACCESS = "Checks if EC2 instance has a public IP address"
    TERMINATION_PROTECTION = "Checks if termination protection is enabled for EC2 instances"
    VPC_ENDPOINTS = "Checks if required VPC endpoints are configured for AWS services"
    VPC_BLOCK_IGW_TRAFFIC = "Checks if VPC Block Internet Gateway Traffic settings are configured"
    EBS_PUBLIC_SNAPSHOTS = "Checks if EBS public snapshots are configured"
    UNRESTRICTED_PORTS = "Checks if security groups have unrestricted access to high-risk or unauthorized ports"
    VPC_DEFAULT_SECURITY_GROUP = "Checks if VPC default security group rules are configured"
    TRANSIT_GATEWAY_AUTO_ACCEPT = "Checks if Transit Gateway auto accept is configured"
    SUBNET_LAUNCH_TEMPLATE_PUBLIC_IP = "Checks if subnets and launch templates prevent automatic public IP assignment"
    VPN_CONFIGURATION = "Checks VPN logging and tunnel status configuration"
    NACL_UNRESTRICTED_SSH_RDP = "Checks if NACL unrestricted SSH/RDP is configured"
    PARAVIRTUAL_INSTANCES = "Checks if Paravirtual instances are configured"
    STOPPED_INSTANCES_CLEANUP = "Checks if stopped instances cleanup is configured"
    VPC_FLOW_LOGS = "Checks if VPC Flow Logs are configured"
    EBS_DEFAULT_ENCRYPTION = "Checks if EBS default encryption is configured"
    UNUSED_NACLS = "Checks if unused NACLs are configured"
    EC2_MULTIPLE_ENIS = "Checks if EC2 multiple ENIs are configured"
    EC2_LAUNCH_TEMPLATES_IMDSV2 = "Checks if EC2 launch templates IMDSv2 are configured"
    EC2_CLIENT_VPN_LOGGING = "Checks if EC2 Client VPN logging is configured"


class RDSChecksDescriptionEnum(Enum):
    RDS_CUSTOM_ADMIN = "Checks if RDS instances are using default admin usernames"
    RDS_MYSQL_ENCRYPTION_IN_TRANSIT = ("Checks if MySQL-based RDS instances enforce SSL/TLS encryption for database "
                                       "connections")
    RDS_ENHANCED_MONITORING = "Checks if RDS instances have enhanced monitoring enabled"
    RDS_CUSTOM_PORT = "Checks if RDS instances are using non-default ports"
    RDS_CLUSTER_COPY_TAGS_TO_SNAPSHOTS = "Checks if RDS clusters have copy tags to snapshots enabled"
    RDS_INSTANCE_COPY_TAGS_TO_SNAPSHOTS = "Checks if RDS instances have copy tags to snapshots enabled"
    RDS_SNAPSHOT_PRIVACY = "Checks if RDS snapshots are private"
    RDS_INSTANCE_PUBLIC_ACCESS = "Checks if RDS instances have public access enabled"
    RDS_AUTO_MINOR_VERSION_UPGRADE = "Checks if RDS instances have auto minor version upgrade enabled"
    RDS_INSTANCE_VPC_DEPLOYMENT = "Checks if RDS instances are deployed within a VPC"
    RDS_IAM_AUTHENTICATION = "Checks if RDS instances have IAM authentication enabled"
    RDS_AUTOMATIC_BACKUPS = "Checks if RDS instances have automatic backups enabled"
    RDS_CLUSTER_IAM_AUTHENTICATION = "Checks if RDS clusters have IAM authentication enabled"
    AURORA_CLUSTER_BACKTRACKING = "Checks if Aurora clusters have backtracking enabled"
    RDS_CLUSTER_MULTI_AZ = "Checks if RDS clusters are deployed in multiple availability zones"
    RDS_CLUSTER_ENCRYPTION = "Checks if RDS clusters have encryption enabled"
    RDS_INSTANCE_ENCRYPTION = "Checks if RDS instances have encryption enabled"
    AURORA_MYSQL_AUDIT_LOGGING = "Checks if Aurora MySQL clusters have audit logging enabled"
    RDS_CLUSTER_AUTO_MINOR_VERSION_UPGRADE = "Checks if RDS clusters have auto minor version upgrade enabled"
    RDS_POSTGRESQL_LOGGING = "Checks if PostgreSQL-based RDS instances have logging enabled"
    RDS_POSTGRESQL_ENCRYPTION_IN_TRANSIT = ("Checks if PostgreSQL-based RDS instances enforce SSL/TLS encryption for "
                                            "database connections")
    RDS_SNAPSHOT_ENCRYPTION = "Checks if RDS snapshots are encrypted"
    RDS_MULTI_AZ = "Checks if RDS instances are deployed in multiple availability zones"
    RDS_EVENT_NOTIFICATIONS = ("RDS event subscriptions are properly set up to notify administrators about critical DB "
                               "instance events (e.g., failovers, maintenance, crashes)")
    RDS_INSTANCE_EVENT_NOTIFICATIONS = ("RDS event subscriptions are properly set up to notify administrators about "
                                        "critical DB instance events (e.g., failovers, maintenance, crashes)")
    RDS_PARAMETER_GROUP_EVENT_NOTIFICATIONS = ("RDS event subscriptions are properly set up to notify administrators "
                                               "about critical parameter group events (e.g., modifications, changes "
                                               "that require reboot, etc.)")
    RDS_SECURITY_GROUP_EVENT_NOTIFICATIONS = ("RDS event subscriptions are set up to notify administrators about "
                                              "critical security group events, such as: - Changes to security group "
                                              "rules - Security group deletion")
    RDS_CLUSTER_DELETION_PROTECTION = ("Amazon RDS clusters have deletion protection enabled to prevent accidental "
                                       "or malicious deletions")
    RDS_INSTANCE_DELETION_PROTECTION = ("Amazon RDS instances have deletion protection enabled to prevent accidental "
                                        "or unauthorized deletions")
    RDS_INSTANCE_LOGS_TO_CLOUDWATCH = ("Amazon RDS instances are configured to send database logs to Amazon CloudWatch "
                                       "Logs for monitoring, auditing, and troubleshooting")
    RDS_CLUSTER_CUSTOM_ADMIN = ("Amazon RDS clusters do not use the default administrative usernames (such as admin, "
                                "root, postgres, sqladmin)")


class IAMChecksDescriptionEnum(Enum):
    NO_FULL_ADMIN_PRIVILEGES = "IAM policies should not allow full '*' administrative privileges"
    NO_IAM_POLICIES_FOR_USERS = "IAM users should not have IAM policies attached"
    NO_WILDCARD_ACTIONS_IN_CUSTOM_POLICIES = "IAM customer managed policies that you create should not allow wildcard actions for services"
    ACCESS_KEY_ROTATION = "IAM users' access keys should be rotated every 90 days or less"
    NO_ROOT_USER_ACCESS_KEY = "IAM root user access key should not exist"
    MFA_ENABLED_FOR_CONSOLE_USERS = "MFA should be enabled for all IAM users that have a console password"
    HARDWARE_MFA_FOR_ROOT_USER = "Hardware MFA should be enabled for the root user"
    STRONG_PASSWORD_POLICIES = "Password policies for IAM users should have strong configurations"
    REMOVE_UNUSED_USER_CREDENTIALS = "Unused IAM user credentials should be removed"


class S3ChecksDescriptionEnum(Enum):
    BLOCK_PUBLIC_ACCESS_SETTINGS_ENABLED = "General purpose buckets should have block public access settings enabled"
    S3_ACL_SECURITY = "S3 general purpose buckets should have secure ACL configurations (no public access, no ACLs for user access)"
    LIFECYCLE_CONFIGURATIONS_REQUIRED = "General purpose buckets should have Lifecycle configurations"
    ACCESS_POINTS_BLOCK_PUBLIC_ACCESS = "Access points should have block public access settings enabled"
    MULTI_REGION_ACCESS_POINTS_BLOCK_PUBLIC_ACCESS = "Multi-Region Access Points should have block public access settings enabled"
    REQUIRE_SSL_FOR_REQUESTS = "General purpose buckets should require requests to use SSL"
    RESTRICT_ACCESS_TO_OTHER_AWS_ACCOUNTS = "General purpose bucket policies should restrict access to other AWS accounts"
    SERVER_ACCESS_LOGGING_ENABLED = "General purpose buckets should have server access logging enabled"


class LambdaChecksDescriptionEnum(Enum):
    PROHIBIT_PUBLIC_ACCESS = "Lambda function policies should prohibit public access"
    SUPPORTED_RUNTIMES = "Lambda functions should use supported runtimes"
    MULTI_AZ_VPC_OPERATION = "VPC Lambda functions should operate in multiple Availability Zones"


class ELBChecksDescriptionEnum(Enum):
    HTTP_TO_HTTPS_REDIRECT = "Application Load Balancer should be configured to redirect all HTTP requests to HTTPS"
    CLASSIC_LOAD_BALANCER_AZ_SPAN = "Classic Load Balancer should span multiple Availability Zones"
    ALB_DESYNC_MITIGATION_MODE = "Application Load Balancer should be configured with defensive or strictest desync mitigation mode"
    LOAD_BALANCERS_AZ_SPAN = "Application, Network and Gateway Load Balancers should span multiple Availability Zones"
    CLASSIC_LOAD_BALANCER_DESYNC_MITIGATION_MODE = "Classic Load Balancer should be configured with defensive or strictest desync mitigation mode"
    CLASSIC_LOAD_BALANCER_SECURE_LISTENER_CONFIGURATION = "Classic Load Balancers should have HTTPS/SSL listeners configured with certificates from ACM"
    CLASSIC_LOAD_BALANCER_SSL_CERTIFICATE = "Classic Load Balancers with SSL/HTTPS listeners should use a certificate provided by AWS Certificate Manager"
    CLASSIC_LOAD_BALANCER_HTTPS_TERMINATION = "Classic Load Balancer listeners should be configured with HTTPS or TLS termination"
    ALB_DROP_INVALID_HTTP_HEADERS = "Application load balancer should be configured to drop invalid http headers"
    LOAD_BALANCERS_LOGGING_ENABLED = "Application and Classic Load Balancers logging should be enabled"
    LOAD_BALANCERS_DELETION_PROTECTION = "Application, Gateway, and Network Load Balancers should have deletion protection enabled"
    CLASSIC_LOAD_BALANCER_CONNECTION_DRAINING = "Classic Load Balancers should have connection draining enabled"
    CLASSIC_LOAD_BALANCER_SSL_SECURITY_POLICY = "Classic Load Balancers with SSL listeners should use a predefined security policy that has strong configuration"
    CLASSIC_LOAD_BALANCER_CROSS_ZONE_LOAD_BALANCING = "Classic Load Balancers should have cross-zone load balancing enabled"


class ElastiCacheChecksDescriptionEnum(Enum):
    AUTOMATIC_BACKUPS_ENABLED = "ElastiCache (Redis OSS) clusters should have automatic backups enabled"
    AUTOMATIC_MINOR_VERSION_UPGRADES_ENABLED = "ElastiCache clusters should have automatic minor version upgrades enabled"
    AUTOMATIC_FAILOVER_ENABLED = "ElastiCache replication groups should have automatic failover enabled"
    ENCRYPTED_AT_REST = "ElastiCache replication groups should be encrypted at rest"
    ENCRYPTED_IN_TRANSIT = "ElastiCache replication groups should be encrypted in transit"
    REDIS_AUTH_ENABLED = "ElastiCache (Redis OSS) replication groups of earlier versions should have Redis OSS AUTH enabled"
    NON_DEFAULT_SUBNET_GROUP = "ElastiCache clusters should not use the default subnet group"


class ECSChecksDescriptionEnum(Enum):
    SECURE_NETWORKING_MODES = "Amazon ECS task definitions should have secure networking modes and user definitions."
    LATEST_FARGATE_PLATFORM_VERSION = "ECS Fargate services should run on the latest Fargate platform version."
    CONTAINER_INSIGHTS_ENABLED = "ECS clusters should use Container Insights."
    NO_PUBLIC_IP_ASSIGNMENT_TASK_SETS = "ECS task sets should not automatically assign public IP addresses."
    NO_PUBLIC_IP_ASSIGNMENT_SERVICES = "ECS services should not have public IP addresses assigned to them automatically."
    NO_HOST_PROCESS_NAMESPACE = "ECS task definitions should not share the host's process namespace."
    NON_PRIVILEGED_CONTAINERS = "ECS containers should run as non-privileged."
    READ_ONLY_ROOT_FILESYSTEMS = "ECS containers should be limited to read-only access to root filesystems."
    NO_ENV_VARIABLES_FOR_SECRETS = "Secrets should not be passed as container environment variables."
    LOGGING_CONFIGURATION_REQUIRED = "ECS task definitions should have a logging configuration."


class EKSChecksDescriptionEnum(Enum):
    PRIVATE_CLUSTER_ENDPOINT = "EKS cluster endpoints should not be publicly accessible"
    SUPPORTED_KUBERNETES_VERSION = "EKS clusters should run on a supported Kubernetes version"
    ENCRYPTED_KUBERNETES_SECRETS = "EKS clusters should use encrypted Kubernetes secrets"
    AUDIT_LOGGING_ENABLED = "EKS clusters should have audit logging enabled"


class EFSChecksDescriptionEnum(Enum):
    ENCRYPTED_WITH_KMS = "Elastic File System should be configured to encrypt file data at-rest using AWS KMS"
    INCLUDED_IN_BACKUP_PLANS = "Amazon EFS volumes should be in backup plans"
    SECURE_ACCESS_POINTS = "EFS access points should enforce both a root directory and user identity"
    MOUNT_TARGETS_NOT_IN_PUBLIC_SUBNET = "EFS mount targets should not be associated with a public subnet"
    AUTOMATIC_BACKUPS_ENABLED = "EFS file systems should have automatic backups enabled"

class ScanStatusEnum(Enum):
    RUNNING = 'running'
    COMPLETED = 'completed'


class ScanServiceStatusEnum(Enum):
    PENDING = 'pending'
    RUNNING = 'running'
    COMPLETED = 'completed'


class SeverityEnum(Enum):
    CRITICAL = 'critical'
    HIGH = 'high'
    MEDIUM = 'medium'
    LOW = 'low'


class ResourceComplianceStatusEnum(Enum):
    PASS = 'pass'
    FAIL = 'fail'
