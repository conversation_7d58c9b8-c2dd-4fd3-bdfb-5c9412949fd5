from fastapi import Request
from app import router
from app.common import GET_CLOUD_PROVIDERS_API, CloudProvidersResponseSchema, requires_permission, UserPermissionEnum
from ..core import CloudProviderService


__all__ = ['cloud_providers']


@router.get(GET_CLOUD_PROVIDERS_API, response_model=CloudProvidersResponseSchema)
async def cloud_providers(request: Request,
                          user: dict = requires_permission(UserPermissionEnum.READ_ONLY.value)):
    service = CloudProviderService()
    response = await service.process()

    return CloudProvidersResponseSchema(
        ok=True,
        status_code=200,
        data=response
    )
