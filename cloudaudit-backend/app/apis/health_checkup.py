from fastapi.responses import Response
from app import router
from app.common import APP_READINESS_API, APP_LIVENESS_API, RABBITMQ_CONSUMER_HEALTH_TEST_API, HttpStatusCodeEnum
from app.core.services.queue_service.rabbitmq_client import restart_dead_consumers


__all__ = ["service_status", "queue_service_health"]


@router.get(APP_READINESS_API)
@router.get(APP_LIVENESS_API)
async def service_status():
    return Response(status_code=HttpStatusCodeEnum.NO_CONTENT.value)


@router.get(RABBITMQ_CONSUMER_HEALTH_TEST_API)
async def queue_service_health():
    """This method is resolved the rabbitmq consumer auto discard issue"""
    await restart_dead_consumers()
    return Response(status_code=HttpStatusCodeEnum.NO_CONTENT.value)
