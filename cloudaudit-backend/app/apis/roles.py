from fastapi import Request
from app import router
from app.common import (DefaultResponseSchema, CUSTOM_ROLES_API, requires_permission,
                        UserPermissionEnum, RolesSchema, GetRolesSchema, ROLES_API, ASSIGN_ROLE_API,
                        REVOKE_ROLE_API, AssignRevokeRoleSchema)
from ..core import (GetRolesService, CreateCustomRoleService, UpdateCustomRoleService, DeleteCustomRoleService,
                    AssignRoleService, RevokeRoleService)

__all__ = ['get_roles', 'create_custom_role', 'update_custom_role', 'delete_custom_role',
           'assign_role', 'revoke_role']


@router.get(ROLES_API, response_model=GetRolesSchema)
async def get_roles(request: Request,
                    user: dict = requires_permission(UserPermissionEnum.READ_ONLY.value)):
    req_user_id = int(request.query_params.get('user_id', 0))
    service = GetRolesService(user, req_user_id)
    response = await service.process()

    return GetRolesSchema(
        ok=True,
        status_code=200,
        data=response
    )


@router.post(CUSTOM_ROLES_API, response_model=DefaultResponseSchema)
async def create_custom_role(request: Request, payload: RolesSchema,
                             user: dict = requires_permission(UserPermissionEnum.CREATE_CUSTOM_ROLE.value)):
    service = CreateCustomRoleService(payload, user)
    await service.process()

    return DefaultResponseSchema(
        ok=True,
        status_code=200,
        message="Custom Role created successfully"
    )


@router.put(CUSTOM_ROLES_API, response_model=DefaultResponseSchema)
async def update_custom_role(request: Request, payload: RolesSchema,
                             user: dict = requires_permission(UserPermissionEnum.UPDATE_CUSTOM_ROLE.value)):
    service = UpdateCustomRoleService(payload, user)
    await service.process()

    return DefaultResponseSchema(
        ok=True,
        status_code=200,
        message="Custom Role updated successfully"
    )


@router.delete(CUSTOM_ROLES_API, response_model=DefaultResponseSchema)
async def delete_custom_role(request: Request,
                             user: dict = requires_permission(UserPermissionEnum.DELETE_CUSTOM_ROLE.value)):
    custom_role_id = request.query_params.get('custom_role_id')
    service = DeleteCustomRoleService(custom_role_id, user)
    await service.process()

    return DefaultResponseSchema(
        ok=True,
        status_code=200,
        message="Custom Role deleted successfully"
    )


@router.post(ASSIGN_ROLE_API, response_model=DefaultResponseSchema)
async def assign_role(request: Request, payload: AssignRevokeRoleSchema,
                      user: dict = requires_permission(UserPermissionEnum.ASSIGN_ROLE_TO_USER.value)):
    service = AssignRoleService(payload, user)
    await service.process()

    return DefaultResponseSchema(
        ok=True,
        status_code=200,
        message="Role assigned successfully"
    )


@router.post(REVOKE_ROLE_API, response_model=DefaultResponseSchema)
async def revoke_role(request: Request, payload: AssignRevokeRoleSchema,
                      user: dict = requires_permission(UserPermissionEnum.REVOKE_ROLE_FROM_USER.value)):
    service = RevokeRoleService(payload, user)
    await service.process()

    return DefaultResponseSchema(
        ok=True,
        status_code=200,
        message="Role revoked successfully"
    )
