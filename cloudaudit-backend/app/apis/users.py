from fastapi import Request
from app import router
from app.common import (DefaultResponseSchema, USERS_API, CreateUserSchema, GET_PERMISSIONS_API,
                        ListPermissionsSchema, requires_permission, UserPermissionEnum, GET_USER_INFO_API,
                        UserInfoResponseSchema, LIST_TEAM_MEMBERS_API, TeamMembersResponseSchema,
                        UPDATE_TEAM_MEMBER_API, UpdateTeamMemberSchema, CHANGE_PASSWORD_API, ChangePasswordSchema,
                        UPDATE_USER_INFO_API, UpdateUserInfoSchema, UPDATE_PASSWORD_API, UpdatePasswordSchema)
from ..core import CreateUserService, GetPermissionsService, DeleteUserService, GetUserInfoService, ListTeamMembersService, UpdateTeamMemberService, ChangePasswordService, UpdateUserInfoService, UpdatePasswordService

__all__ = ['create_user', 'delete_user', 'get_permissions', 'get_user_info', 'list_team_members', 'update_team_member', 'change_password', 'update_user_info', 'update_password']


@router.post(USERS_API, response_model=DefaultResponseSchema)
async def create_user(request: Request, payload: CreateUserSchema,
                      user: dict = requires_permission(UserPermissionEnum.CREATE_USER.value)):
    service = CreateUserService(payload, user)
    await service.process()

    return DefaultResponseSchema(
        ok=True,
        status_code=200,
        message="User created successfully"
    )


@router.delete(USERS_API, response_model=DefaultResponseSchema)
async def delete_user(request: Request,
                      user: dict = requires_permission(UserPermissionEnum.DELETE_USER.value)):
    user_to_delete = int(request.query_params.get('user_to_delete'))
    service = DeleteUserService(user_to_delete, user)
    await service.process()

    return DefaultResponseSchema(
        ok=True,
        status_code=200,
        message="User deleted successfully"
    )


@router.get(GET_PERMISSIONS_API, response_model=ListPermissionsSchema)
async def get_permissions(request: Request,
                          user: dict = requires_permission(UserPermissionEnum.READ_ONLY.value)):
    service = GetPermissionsService()
    response = await service.process()

    return ListPermissionsSchema(
        ok=True,
        status_code=200,
        data=response
    )


@router.get(GET_USER_INFO_API, response_model=UserInfoResponseSchema)
async def get_user_info(request: Request,
                        user: dict = requires_permission(UserPermissionEnum.READ_ONLY.value)):
    service = GetUserInfoService(user)
    response = await service.process()

    return UserInfoResponseSchema(
        ok=True,
        status_code=200,
        data=response
    )


@router.get(LIST_TEAM_MEMBERS_API, response_model=TeamMembersResponseSchema)
async def list_team_members(request: Request,
                           user: dict = requires_permission(UserPermissionEnum.READ_ONLY.value)):
    service = ListTeamMembersService(user)
    response = await service.process()

    return TeamMembersResponseSchema(
        ok=True,
        status_code=200,
        data=response
    )


@router.put(UPDATE_TEAM_MEMBER_API, response_model=DefaultResponseSchema)
async def update_team_member(request: Request, payload: UpdateTeamMemberSchema,
                           user: dict = requires_permission(UserPermissionEnum.UPDATE_USER_PERMISSIONS.value)):
    service = UpdateTeamMemberService(payload, user)
    await service.process()

    return DefaultResponseSchema(
        ok=True,
        status_code=200,
        message="Team member updated successfully"
    )


@router.post(CHANGE_PASSWORD_API, response_model=DefaultResponseSchema)
async def change_password(request: Request, payload: ChangePasswordSchema,
                         user: dict = requires_permission(UserPermissionEnum.UPDATE_USER_PERMISSIONS.value)):
    service = ChangePasswordService(payload, user)
    await service.process()

    return DefaultResponseSchema(
        ok=True,
        status_code=200,
        message="Password changed successfully"
    )


@router.put(UPDATE_USER_INFO_API, response_model=DefaultResponseSchema)
async def update_user_info(request: Request, payload: UpdateUserInfoSchema,
                          user: dict = requires_permission(UserPermissionEnum.READ_ONLY.value)):
    service = UpdateUserInfoService(payload, user)
    await service.process()

    return DefaultResponseSchema(
        ok=True,
        status_code=200,
        message="User information updated successfully"
    )


@router.post(UPDATE_PASSWORD_API, response_model=DefaultResponseSchema)
async def update_password(request: Request, payload: UpdatePasswordSchema,
                         user: dict = requires_permission(UserPermissionEnum.READ_ONLY.value)):
    service = UpdatePasswordService(payload, user)
    await service.process()

    return DefaultResponseSchema(
        ok=True,
        status_code=200,
        message="Password updated successfully"
    )
