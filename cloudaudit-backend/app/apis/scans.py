from fastapi import Request
from app import router
from app.common import (CREATE_SCAN_API, DefaultResponseSchema, CreateScanSchema, get_current_user, requires_permission,
                        UserPermissionEnum, GET_SCANS_API, GetScansResponseSchema, GET_SCAN_DETAIL_API, ScanDetailResponseSchema)
from app.core import CreateScanService, GetScansService, GetScanDetailService


__all__ = ['create_scan', 'get_scans', 'get_scan_detail']


@router.post(CREATE_SCAN_API, response_model=DefaultResponseSchema)
async def create_scan(request: Request, payload: CreateScanSchema,
                      user: dict = requires_permission(UserPermissionEnum.SCAN_SERVICE.value)):
    service = CreateScanService(payload, user)
    await service.process()

    return DefaultResponseSchema(
        ok=True,
        status_code=200,
        message="Scan started successfully",
    )


@router.get(GET_SCANS_API, response_model=GetScansResponseSchema)
async def get_scans(request: Request,
                    page: int = 1,
                    page_size: int = 10,
                    user: dict = requires_permission(UserPermissionEnum.READ_ONLY.value)):
    service = GetScansService(user, page, page_size)
    response = await service.process()

    return GetScansResponseSchema(
        ok=True,
        status_code=200,
        data=response["data"],
        pagination=response["pagination"]
    )


@router.get(GET_SCAN_DETAIL_API, response_model=ScanDetailResponseSchema)
async def get_scan_detail(request: Request, scan_id: int,
                         user: dict = requires_permission(UserPermissionEnum.READ_ONLY.value)):
    service = GetScanDetailService(user, scan_id)
    response = await service.process()

    return ScanDetailResponseSchema(
        ok=True,
        status_code=200,
        data=response
    )
