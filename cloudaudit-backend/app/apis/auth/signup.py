from fastapi import Request
from app import router
from app.common import SIGNUP_API, SignUpLoginSchema, DefaultResponseSchema
from app.core.services.auth import SignUpService


__all__ = ['signup']


@router.post(SIGNUP_API, response_model=DefaultResponseSchema)
async def signup(request: Request, payload: SignUpLoginSchema):
    service = SignUpService(payload)
    await service.process()
    return DefaultResponseSchema(
        ok=True,
        status_code=201,
        message="User signed up successfully"
    )
