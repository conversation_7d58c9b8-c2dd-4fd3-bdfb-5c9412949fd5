from fastapi import Request
from app import router
from app.common import LOGOUT_API, RefreshTokenSchema, DefaultResponseSchema
from app.core.services.auth import LogoutService


__all__ = ['logout']


@router.post(LOGOUT_API, response_model=DefaultResponseSchema)
async def logout(request: Request, payload: RefreshTokenSchema):
    service = LogoutService(payload)
    await service.process()
    return DefaultResponseSchema(
        ok=True,
        status_code=200,
        message="User logged out successfully"
    )
