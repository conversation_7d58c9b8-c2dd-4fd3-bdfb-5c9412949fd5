from fastapi import Request
from app import router
from app.common import GET_REGIONS_API, GetRegionsResponseSchema, requires_permission, UserPermissionEnum
from ..core import CloudProviderRegionsService


__all__ = ['get_regions']


@router.get(GET_REGIONS_API, response_model=GetRegionsResponseSchema)
async def get_regions(request: Request,
                      cloud_provider_id: int,
                      user: dict = requires_permission(UserPermissionEnum.READ_ONLY.value)):
    service = CloudProviderRegionsService(cloud_provider_id)
    response = await service.process()

    return GetRegionsResponseSchema(
        ok=True,
        status_code=200,
        data=response
    )