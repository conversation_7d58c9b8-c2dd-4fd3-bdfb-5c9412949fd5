# import eventlet
from app.manage import create_app

# Create App
app, router = create_app()


@app.on_event("startup")
async def init_app():
    """Initialise app configuration on app startup"""
    # eventlet.monkey_patch()
    # WebSocket Manager
    from app.core.services import WebSocketManager
    app.state.websocket_manager = WebSocketManager()

    # To avoid circular import error in MySQL connection pool
    from app.core.models.mysql import mysql_connection_pool_factory
    app.state.connection_pool = await mysql_connection_pool_factory()

    # RabbitMQ configuration
    from app.core.services.queue_service.rabbitmq_client import rmq_pool_factory, queue_init
    app.rmq_channel_pool = await rmq_pool_factory(app)
    await queue_init(app)

    # Celery Configuration
    from app.core.services.celery_conf import celery_obj
    app.state.celery = celery_obj


@app.on_event("shutdown")
async def terminate_app():
    """Terminate app configuration on app shutdown"""

    # Close DB connections
    pool = app.state.connection_pool
    if pool:
        pool.close()
        await pool.wait_closed()

    # Close RabbitMQ connections
    await app.rmq_connection_pool.close()


# import APIs endpoints
from .apis import *

# Include router
app.include_router(router)
