# React + Vite Development Environment

This repository contains a React + Vite application that can be run locally or with Docker, configured to run on localhost only.

## Prerequisites

- [Node.js](https://nodejs.org/) (v20.x or later)
- [npm](https://www.npmjs.com/) (latest version recommended)
- [Git](https://git-scm.com/downloads)
- [Docker](https://docs.docker.com/get-docker/) (optional, for containerized setup)

## Getting Started

### 1. Clone the repository

```bash
git clone https://github.com/Maruti-Techlabs/cloudaudit-frontend.git
cd cloudaudit-frontend
```

## Running Locally (Without Docker)

### 2. Install dependencies

```bash
npm install
```

### 3. Start the development server

```bash
npm run dev
```

### 4. Access the application

Open your browser and navigate to:

```
http://localhost:5173
```

## Running with Docker

### 2. Build the Docker image

```bash
docker build -t image_name .
```

### 3. Run the Docker container

```bash
docker run -p 5173:5173 -v $(pwd):/app image_name
```

This command:

- Maps port 5173 from the container to your host machine
- Mounts your current directory as a volume in the container
- Uses the image we just built

### 4. Access the application

Open your browser and navigate to:

```
http://localhost:5173
```

## Features

### Local Environment

- Hot-reloading development server
- Fast refresh for React components
- Native performance on your local machine

### Docker Environment

- Consistent Node.js 20.x environment across all setups
- Latest npm version
- Isolated dependencies
- Hot-reloading development server
- Volume mounting for instant code changes
- Restricted to localhost connections only for security

## Docker Details

- Base Image: Ubuntu 22.04
- Exposed Port: 5173 (Vite's default development port)
- Working Directory: `/app`
- Command: `npm run dev -- --host`

## Production Deployment

For production deployment, see the comprehensive [DEPLOYMENT.md](DEPLOYMENT.md) guide.

### Quick Production Setup

```bash
# Clone and setup
git clone https://github.com/your-org/cloudaudit-frontend.git
cd cloudaudit-frontend

# Configure environment
cp .env.example .env.production
# Edit .env.production with your production settings

# Deploy with Docker
./deploy.sh production
```

### Environment Configuration

The application supports multiple environments:

- **Development**: `.env.local` or default values
- **Staging**: `.env.staging`
- **Production**: `.env.production`

Key environment variables:

- `VITE_BACKEND_LOCAL_BASE_URL`: API endpoint
- `VITE_APP_ENV`: Environment (development/staging/production)
- `VITE_APP_BASE_URL`: Application URL
- `VITE_SENTRY_DSN`: Error reporting (optional)
- `VITE_GA_ID`: Google Analytics (optional)

### Build Commands

```bash
# Development build
npm run build

# Staging build
npm run build:staging

# Production build
npm run build:production
```

## Customization

### Changing the port

If you need to use a different port, modify:

1. The `EXPOSE` line in the Dockerfile
2. The port mapping in the `docker run` command
3. Update any references to port 5173 in your Vite configuration
4. Set `VITE_DEV_PORT` in your environment file

### Adding environment variables

Create or edit your environment file:

```bash
# .env.local for development
VITE_BACKEND_LOCAL_BASE_URL=http://localhost:8000/api
VITE_APP_NAME=CloudAudit
VITE_ENABLE_DEVTOOLS=true
```
