# Environment Configuration Template
# Copy this file to .env.local for local development
# Copy this file to .env.production for production deployment
# Copy this file to .env.staging for staging deployment

# =============================================================================
# API Configuration
# =============================================================================

# Backend API Base URL
# Development: http://localhost:8000/api
# Staging: https://api-staging.cloudaudit.com/api
# Production: https://api.cloudaudit.com/api
VITE_BACKEND_LOCAL_BASE_URL=http://localhost:8000/api

# API Timeout (in milliseconds)
VITE_API_TIMEOUT=30000

# Maximum retry attempts for failed requests
VITE_MAX_RETRIES=2

# =============================================================================
# Application Configuration
# =============================================================================

# Application Environment (development, staging, production)
VITE_APP_ENV=development

# Application Name
VITE_APP_NAME=CloudAudit

# Application Version (automatically set during build)
VITE_APP_VERSION=0.1.0

# Application Base URL
VITE_APP_BASE_URL=http://localhost:5173

# =============================================================================
# Feature Flags
# =============================================================================

# Enable React Query DevTools (true/false)
VITE_ENABLE_DEVTOOLS=true

# Enable debug logging (true/false)
VITE_ENABLE_DEBUG_LOGGING=true

# Enable performance monitoring (true/false)
VITE_ENABLE_PERFORMANCE_MONITORING=false

# Enable error reporting (true/false)
VITE_ENABLE_ERROR_REPORTING=false

# =============================================================================
# Security Configuration
# =============================================================================

# Token refresh threshold (in minutes before expiry)
VITE_TOKEN_REFRESH_THRESHOLD=5

# Session timeout (in minutes)
VITE_SESSION_TIMEOUT=60

# =============================================================================
# Third-party Services
# =============================================================================

# Sentry DSN for error reporting (production only)
VITE_SENTRY_DSN=

# Google Analytics ID (production only)
VITE_GA_ID=

# =============================================================================
# Build Configuration
# =============================================================================

# Enable source maps in production (true/false)
VITE_ENABLE_SOURCE_MAPS=false

# Enable bundle analysis (true/false)
VITE_ENABLE_BUNDLE_ANALYSIS=false

# Build output directory
VITE_BUILD_OUTPUT_DIR=dist

# =============================================================================
# CDN Configuration (for production)
# =============================================================================

# CDN Base URL for static assets
VITE_CDN_BASE_URL=

# =============================================================================
# Monitoring and Analytics
# =============================================================================

# Enable web vitals reporting (true/false)
VITE_ENABLE_WEB_VITALS=false

# Performance monitoring endpoint
VITE_PERFORMANCE_ENDPOINT=

# =============================================================================
# Development Configuration
# =============================================================================

# Development server port
VITE_DEV_PORT=5173

# Development server host
VITE_DEV_HOST=localhost

# Enable hot module replacement (true/false)
VITE_ENABLE_HMR=true
