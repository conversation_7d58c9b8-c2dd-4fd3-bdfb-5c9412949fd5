# GitHub Actions workflow for deploying CloudAudit Frontend to Staging EC2
# Triggers on push to 'staging' branch or manual dispatch
name: Deploy Frontend to Staging

on:
  push:
    branches:
      - staging
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: self-hosted
    environment: staging
    steps:
      # Step 1: Checkout the repository code
      - name: Checkout code
        uses: actions/checkout@v4

      # Step 2: Set up Node.js environment
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      # Step 3: Install frontend dependencies
      - name: Install dependencies
        run: npm ci
        working-directory: ./

      # Step 4: Build the frontend application
      - name: Build frontend
        run: npm run build
        working-directory: ./
        env:
          # Pass environment variables from GitHub Secrets
          VITE_API_URL: ${{ secrets.STAGING_VITE_API_URL }}

      # Step 5: Deploy to EC2 server using SSH
      - name: Deploy to EC2 via SSH
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.STAGING_EC2_HOST }}
          username: ${{ secrets.STAGING_EC2_USER }}
          key: ${{ secrets.STAGING_EC2_SSH_KEY }}
          port: ${{ secrets.STAGING_EC2_PORT }}
          script: |
            # Navigate to the frontend directory on the EC2 instance
            cd /path/to/your/cloudaudit-frontend
            # Pull latest code from staging branch
            git pull origin staging
            # Install dependencies and build
            npm ci
            npm run build
            # Restart the frontend service using PM2 (or start if not running)
            pm2 restart frontend || pm2 start npm --name "frontend" -- run start
