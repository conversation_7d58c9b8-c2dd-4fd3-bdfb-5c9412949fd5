#!/bin/bash

# CloudAudit Frontend Deployment Script
# Usage: ./deploy.sh [environment] [options]
# Environments: development, staging, production
# Options: --build-only, --no-cache, --help

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="development"
BUILD_ONLY=false
NO_CACHE=false
DOCKER_COMPOSE_FILE="docker-compose.yml"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show help
show_help() {
    cat << EOF
CloudAudit Frontend Deployment Script

Usage: $0 [environment] [options]

Environments:
    development     Deploy for local development (default)
    staging         Deploy to staging environment
    production      Deploy to production environment

Options:
    --build-only    Only build the Docker image, don't deploy
    --no-cache      Build without using Docker cache
    --help          Show this help message

Examples:
    $0                          # Deploy to development
    $0 staging                  # Deploy to staging
    $0 production --no-cache    # Deploy to production without cache
    $0 staging --build-only     # Only build staging image

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        development|staging|production)
            ENVIRONMENT="$1"
            shift
            ;;
        --build-only)
            BUILD_ONLY=true
            shift
            ;;
        --no-cache)
            NO_CACHE=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Set environment-specific configurations
case $ENVIRONMENT in
    staging)
        DOCKER_COMPOSE_FILE="docker-compose.staging.yml"
        ENV_FILE=".env.staging"
        ;;
    production)
        DOCKER_COMPOSE_FILE="docker-compose.yml"
        ENV_FILE=".env.production"
        ;;
    development)
        DOCKER_COMPOSE_FILE="docker-compose.dev.yml"
        ENV_FILE=".env.local"
        ;;
esac

print_status "Starting deployment for environment: $ENVIRONMENT"

# Check if required files exist
if [[ ! -f "$DOCKER_COMPOSE_FILE" ]]; then
    print_error "Docker compose file not found: $DOCKER_COMPOSE_FILE"
    exit 1
fi

if [[ ! -f "$ENV_FILE" ]] && [[ "$ENVIRONMENT" != "development" ]]; then
    print_warning "Environment file not found: $ENV_FILE"
    print_warning "Using default environment variables"
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    print_error "Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

# Load environment variables if file exists
if [[ -f "$ENV_FILE" ]]; then
    print_status "Loading environment variables from $ENV_FILE"
    export $(grep -v '^#' "$ENV_FILE" | xargs)
fi

# Build options
BUILD_ARGS=""
if [[ "$NO_CACHE" == "true" ]]; then
    BUILD_ARGS="--no-cache"
    print_status "Building without cache"
fi

# Build the application
print_status "Building Docker image for $ENVIRONMENT environment..."
if [[ "$ENVIRONMENT" == "development" ]]; then
    # For development, we might want to use a different Dockerfile or target
    docker-compose -f "$DOCKER_COMPOSE_FILE" build $BUILD_ARGS
else
    docker-compose -f "$DOCKER_COMPOSE_FILE" build $BUILD_ARGS
fi

if [[ $? -eq 0 ]]; then
    print_success "Docker image built successfully"
else
    print_error "Failed to build Docker image"
    exit 1
fi

# If build-only flag is set, exit here
if [[ "$BUILD_ONLY" == "true" ]]; then
    print_success "Build completed. Exiting as requested (--build-only flag)"
    exit 0
fi

# Deploy the application
print_status "Deploying application..."
docker-compose -f "$DOCKER_COMPOSE_FILE" up -d

if [[ $? -eq 0 ]]; then
    print_success "Application deployed successfully"
else
    print_error "Failed to deploy application"
    exit 1
fi

# Wait for the application to be ready
print_status "Waiting for application to be ready..."
sleep 10

# Health check
if [[ "$ENVIRONMENT" == "development" ]]; then
    HEALTH_URL="http://localhost:5173/health"
elif [[ "$ENVIRONMENT" == "staging" ]]; then
    HEALTH_URL="http://localhost:8080/health"
else
    HEALTH_URL="http://localhost/health"
fi

# Try to reach the health endpoint
for i in {1..30}; do
    if curl -f "$HEALTH_URL" > /dev/null 2>&1; then
        print_success "Application is healthy and ready!"
        break
    else
        if [[ $i -eq 30 ]]; then
            print_warning "Health check failed, but deployment completed"
            print_warning "Please check the application manually"
        else
            print_status "Waiting for application to be ready... ($i/30)"
            sleep 2
        fi
    fi
done

# Show running containers
print_status "Running containers:"
docker-compose -f "$DOCKER_COMPOSE_FILE" ps

# Show logs (last 20 lines)
print_status "Recent logs:"
docker-compose -f "$DOCKER_COMPOSE_FILE" logs --tail=20

print_success "Deployment completed for $ENVIRONMENT environment!"

# Environment-specific post-deployment messages
case $ENVIRONMENT in
    development)
        print_status "Application is available at: http://localhost:5173"
        ;;
    staging)
        print_status "Application is available at: http://localhost:8080"
        print_status "Or at your configured staging domain"
        ;;
    production)
        print_status "Application is available at: http://localhost"
        print_status "Or at your configured production domain"
        ;;
esac

print_status "To view logs: docker-compose -f $DOCKER_COMPOSE_FILE logs -f"
print_status "To stop: docker-compose -f $DOCKER_COMPOSE_FILE down"
