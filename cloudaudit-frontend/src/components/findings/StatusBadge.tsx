import React from "react";
import { <PERSON><PERSON><PERSON>cle, AlertCircle, RefreshCw } from "lucide-react";
import { StatusBadgeProps } from "./types";
import { cn } from "@/utils/cn";

/**
 * StatusBadge component displays a badge with the status of a finding
 *
 * @param status - The status to display (pass, fail, remediated, etc.)
 * @param size - The size of the badge (sm, md, lg)
 */
const StatusBadge: React.FC<StatusBadgeProps> = ({ status, size = "md" }) => {
  // Normalize status to lowercase for consistent comparison
  const normalizedStatus = status.toLowerCase();
  const isPass = normalizedStatus === "pass";
  const isRemediated = normalizedStatus === "remediated";

  // Determine the appropriate classes based on status and size
  const containerClasses = cn(
    "inline-flex items-center justify-center rounded-full",
    {
      "bg-success-500/20 text-success-500": isPass,
      "bg-primary-500/20 text-primary-500": isRemediated,
      "bg-error-500/20 text-error-500": !isPass && !isRemediated,
      "px-1.5 py-0.5 text-xs": size === "sm",
      "px-2 py-1 text-xs": size === "md",
      "px-3 py-1.5 text-sm": size === "lg",
    }
  );

  // Determine the icon size based on badge size
  const iconSize = size === "sm" ? 12 : size === "md" ? 14 : 16;

  return (
    <div
      className={containerClasses}
      role="status"
      aria-label={`Status: ${status}`}
    >
      {isPass ? (
        <CheckCircle size={iconSize} className="mr-1 flex-shrink-0" />
      ) : isRemediated ? (
        <RefreshCw size={iconSize} className="mr-1 flex-shrink-0" />
      ) : (
        <AlertCircle size={iconSize} className="mr-1 flex-shrink-0" />
      )}
      <span className="font-medium uppercase">{status}</span>
    </div>
  );
};

export default StatusBadge;
