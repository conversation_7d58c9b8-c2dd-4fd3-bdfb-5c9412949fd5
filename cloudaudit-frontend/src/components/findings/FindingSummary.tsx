import React from "react";
import { formatDate } from "@/lib/dateUtils";
import { Card, CardContent } from "@components/ui/Card";
import { CheckCircle, AlertCircle, RefreshCw } from "lucide-react";
import { FindingSummaryProps } from "./types";
import StatusBadge from "./StatusBadge";
import SeverityBadge from "./SeverityBadge";

/**
 * FindingSummary component displays a summary of a finding
 * including service, account, cloud provider, date, and status information
 */
const FindingSummary: React.FC<FindingSummaryProps> = ({ finding }) => {
  // Format the date for better readability
  const formattedDate = formatDate(finding.created_at, {
    shortMonth: false,
  });

  return (
    <Card>
      <CardContent className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Service Information */}
          <div>
            <h3 className="text-dark-400 text-sm mb-1">Service</h3>
            <p className="font-medium text-dark-200">{finding.service_name}</p>
          </div>

          {/* Account Information */}
          <div>
            <h3 className="text-dark-400 text-sm mb-1">Account</h3>
            <p className="font-medium text-dark-200">{finding.account_name}</p>
            <p className="text-xs text-dark-400">{finding.aws_account_id}</p>
          </div>

          {/* Cloud Provider Information */}
          <div>
            <h3 className="text-dark-400 text-sm mb-1">Cloud Provider</h3>
            <p className="font-medium text-dark-200">
              {finding.cloud_provider_name}
            </p>
          </div>

          {/* Date Information */}
          <div>
            <h3 className="text-dark-400 text-sm mb-1">Date</h3>
            <p className="font-medium text-dark-200">{formattedDate}</p>
          </div>

          {/* Finding Description and Status */}
          <div className="col-span-full">
            <div className="border border-dark-700 rounded-lg p-4 bg-dark-850">
              <div className="flex items-start gap-3">
                <div
                  className={`p-2 rounded-full flex-shrink-0 ${
                    finding.status === "pass"
                      ? "bg-success-500/20"
                      : finding.status === "remediated"
                      ? "bg-primary-500/20"
                      : "bg-error-500/20"
                  }`}
                  aria-hidden="true"
                >
                  {finding.status === "pass" ? (
                    <CheckCircle size={18} className="text-success-500" />
                  ) : finding.status === "remediated" ? (
                    <RefreshCw size={18} className="text-primary-500" />
                  ) : (
                    <AlertCircle size={18} className="text-error-500" />
                  )}
                </div>
                <div>
                  <h3 className="font-medium text-dark-200 mb-1">
                    {finding.description}
                  </h3>
                  <div className="flex flex-wrap items-center gap-3 mt-2">
                    <SeverityBadge severity={finding.severity} />
                    <StatusBadge status={finding.status} />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default React.memo(FindingSummary);
