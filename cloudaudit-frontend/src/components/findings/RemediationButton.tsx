import React from "react";
import Button from "@components/ui/Button";
import { <PERSON>Alert, Loader2, Lock } from "lucide-react";
import { RemediationButtonProps } from "./types";
import { usePermissionContext } from "@/contexts/PermissionContext";
import { PERMISSIONS } from "@/types/permissions";

/**
 * RemediationButton component displays a button to remediate a finding
 * with proper loading states and tooltips
 */
const RemediationButton: React.FC<RemediationButtonProps> = ({
  findingId,
  detailIndex,
  detail,
  onRemediate,
  isRemediating,
}) => {
  // Get permission context
  const { hasPermission } = usePermissionContext();

  // Check if user has permission to remediate findings
  const canRemediate = hasPermission(PERMISSIONS.REMEDIATE_FINDINGS);

  const handleClick = React.useCallback(() => {
    onRemediate(findingId, detailIndex, detail);
  }, [findingId, detailIndex, detail, onRemediate]);

  return (
    <div className="ml-4 relative group">
      <Button
        variant="outline"
        size="sm"
        className="border-error-500/50 text-error-500 hover:bg-error-500/10 hover:border-error-500"
        leftIcon={
          isRemediating ? undefined : canRemediate ? (
            <ShieldAlert size={14} />
          ) : (
            <Lock size={14} />
          )
        }
        onClick={handleClick}
        disabled={isRemediating || !canRemediate}
        aria-label={
          canRemediate
            ? "Remediate this finding"
            : "Permission required to remediate"
        }
        title={
          canRemediate
            ? undefined
            : "You don't have permission to remediate findings"
        }
      >
        {isRemediating ? (
          <>
            <Loader2
              size={14}
              className="mr-2 animate-spin"
              aria-hidden="true"
            />
            <span>Remediating...</span>
          </>
        ) : (
          "Remediate"
        )}
      </Button>

      {/* Tooltip */}
      <div
        className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 hidden group-hover:block z-10 w-48"
        role="tooltip"
        id={`remediate-tooltip-${detailIndex}`}
      >
        <div className="bg-dark-800 text-dark-200 text-xs p-2 rounded shadow-lg border border-dark-700">
          {canRemediate
            ? "Apply automated remediation to fix this compliance issue"
            : "You need the 'remediate_findings' permission to fix this issue"}
        </div>
        {/* Tooltip arrow */}
        <div
          className="absolute top-full left-1/2 transform -translate-x-1/2 w-2 h-2 rotate-45 bg-dark-800 border-b border-r border-dark-700"
          aria-hidden="true"
        ></div>
      </div>
    </div>
  );
};

export default React.memo(RemediationButton);
