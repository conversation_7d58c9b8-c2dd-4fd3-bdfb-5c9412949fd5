import React from "react";
import { FindingDetailsRowProps } from "./types";
import BooleanIndicator from "./BooleanIndicator";
import RemediationButton from "./RemediationButton";
import RemediationStatusButton from "./RemediationStatusButton";

/**
 * FindingDetailsRow component renders a single row in the finding details table
 * with proper handling of different data types and remediation actions
 */
const FindingDetailsRow: React.FC<FindingDetailsRowProps> = ({
  detail,
  index,
  findingId,
  onRemediate,
  remediatingDetailIndex,
  isPending,
  onViewRemediationDetails,
}) => {
  // Determine if this row is currently being remediated
  const isRemediating = remediatingDetailIndex === index && isPending;

  // Handle view remediation details button click
  const handleViewDetails = React.useCallback(() => {
    onViewRemediationDetails(detail, index);
  }, [detail, index, onViewRemediationDetails]);

  return (
    <tr
      className="border-b border-dark-800/50 hover:bg-dark-900/30 transition-colors"
      data-testid={`finding-detail-row-${index}`}
    >
      {/* Render all fields except compliance and remediate */}
      {Object.entries(detail).map(([key, value]) => {
        // Skip compliance and remediate fields as they're handled separately
        if (key === "compliance" || key === "remediate") return null;

        return (
          <td key={key} className="py-3 px-4 text-dark-300">
            {/* Render different data types appropriately */}
            {typeof value === "boolean" ? (
              <BooleanIndicator value={value} />
            ) : typeof value === "object" && value !== null ? (
              // Skip rendering objects directly
              <span className="text-dark-400 italic">
                {key === "remediate" ? "[Remediation data]" : "[Object data]"}
              </span>
            ) : (
              String(value)
            )}
          </td>
        );
      })}

      {/* Render compliance column if it exists */}
      {Object.prototype.hasOwnProperty.call(detail, "compliance") && (
        <td key="compliance" className="py-3 px-4 text-dark-300 min-w-[200px]">
          <div className="flex items-center justify-between">
            {/* Compliance status indicator */}
            <BooleanIndicator value={detail.compliance === true} />

            {/* Remediation button or status */}
            {detail.compliance === false && !("remediate" in detail) && (
              <RemediationButton
                findingId={findingId}
                detailIndex={index}
                detail={detail}
                onRemediate={onRemediate}
                isRemediating={isRemediating}
              />
            )}

            {/* Show remediation status if it exists */}
            {"remediate" in detail &&
              detail.remediate &&
              typeof detail.remediate === "object" && (
                <RemediationStatusButton
                  remediate={detail.remediate}
                  onClick={handleViewDetails}
                />
              )}
          </div>
        </td>
      )}
    </tr>
  );
};

export default React.memo(FindingDetailsRow);
