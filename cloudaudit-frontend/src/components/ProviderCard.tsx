import React, { ReactNode } from "react";
import { formatDate } from "@/lib/dateUtils";
import {
  Shield,
  Trash2,
  CloudIcon,
  Cloud,
  ShieldAlert,
  Database,
} from "lucide-react";
import { motion } from "framer-motion";
import { Card, CardContent } from "./ui/Card";
import Button from "./ui/Button";
import Badge from "./ui/Badge";
import { CloudProviderName } from "@/stores/cloudProviderStore";

interface ProviderCardProps {
  provider: CloudProviderName;
  accountId: string;
  lastScanDate?: string;
  totalScans: number;
  issuesFound: number;
  onScan: () => void;
  onDelete: () => void;
  onClick: () => void;
}

const getProviderIcon = (provider: CloudProviderName): ReactNode => {
  switch (provider) {
    case "AWS":
      return <CloudIcon className="text-accent-500" />;
    case "GCP":
      return <Database className="text-primary-500" />;
    case "Azure":
      return <Cloud className="text-secondary-500" />;
    default:
      return <Cloud />;
  }
};

const getProviderColor = (provider: CloudProviderName) => {
  switch (provider) {
    case "AWS":
      return "bg-accent-500/20 border-accent-500/30";
    case "GCP":
      return "bg-primary-500/20 border-primary-500/30";
    case "Azure":
      return "bg-secondary-500/20 border-secondary-500/30";
    default:
      return "bg-dark-700/30 border-dark-600/30";
  }
};

const ProviderCard: React.FC<ProviderCardProps> = ({
  provider,
  accountId,
  lastScanDate,
  totalScans,
  issuesFound,
  onScan,
  onDelete,
  onClick,
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card
        className="h-full hover:shadow-lg transition-all duration-300"
        onClick={onClick}
      >
        <CardContent className="p-0">
          <div className="p-5">
            <div className="flex justify-between items-start mb-4">
              <div
                className={`p-3 rounded-lg ${getProviderColor(
                  provider
                )} border`}
              >
                {getProviderIcon(provider)}
              </div>
              <Badge variant={issuesFound > 0 ? "error" : "success"}>
                {issuesFound > 0 ? `${issuesFound} Issues` : "No Issues"}
              </Badge>
            </div>

            <h3 className="text-lg font-semibold mb-1 text-dark-100">
              {provider}
            </h3>
            <p className="text-sm font-mono text-dark-400 mb-4">{accountId}</p>

            <div className="flex gap-2 items-center text-dark-400 text-sm mb-4">
              <ShieldAlert size={16} className="text-dark-500" />
              <span>{totalScans} Total Scans</span>
            </div>

            {lastScanDate && (
              <p className="text-xs text-dark-500">
                Last scan: {formatDate(lastScanDate)}
              </p>
            )}
          </div>

          <div className="border-t border-dark-700 p-4 grid grid-cols-2 gap-2">
            <Button
              variant="primary"
              size="sm"
              fullWidth
              leftIcon={<Shield size={16} />}
              onClick={(e) => {
                e.stopPropagation();
                onScan();
              }}
            >
              Scan
            </Button>
            <Button
              variant="outline"
              size="sm"
              fullWidth
              leftIcon={<Trash2 size={16} />}
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
            >
              Delete
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default ProviderCard;
