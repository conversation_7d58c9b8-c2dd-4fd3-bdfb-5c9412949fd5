import { useCreateScan, useRegions, QUERY_KEYS } from "@/hooks/useScans";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { QUERY_KEYS as ACCOUNT_QUERY_KEYS } from "@/hooks/useAccounts";
import {
  AlertCircle,
  Cloud,
  CloudIcon,
  Database,
  Lock,
  RefreshCw,
} from "lucide-react";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { getServices } from "../services/dashboard";
import {
  CloudProviderName,
  useProviderStore,
} from "@/stores/cloudProviderStore";
import { useAccounts } from "@/hooks/useAccounts";
import { useScanStore } from "@/stores/scanStore";
import { useAuth } from "@/hooks/useAuth";
import Button from "./ui/Button";
import Modal from "./ui/Modal";
import MultiSelect from "./ui/MultiSelect";
import Select from "./ui/Select";
import { Skeleton } from "./ui/Skeleton";
import { ServiceError } from "@/services/baseService";
import toast from "react-hot-toast";
import { Account } from "@/types/api";
import { usePermissionContext } from "@/contexts/PermissionContext";
import { PERMISSIONS } from "@/types/permissions";

/**
 * Props for the ScanModal component
 * @property isOpen - Whether the modal is open
 * @property onClose - Function to call when the modal is closed
 * @property provider - The selected cloud provider (optional if accounts are provided)
 * @property accountId - The ID of the account to scan (optional if accounts are provided)
 * @property providerId - The ID of the cloud provider (optional if accounts are provided)
 * @property onProviderChange - Optional function to call when the provider is changed
 * @property accounts - Optional list of accounts to select from
 */
interface ScanModalProps {
  isOpen: boolean;
  onClose: () => void;
  provider?: CloudProviderName;
  accountId?: string | number;
  providerId?: number;
  onProviderChange?: (provider: CloudProviderName) => void;
  accounts?: Account[];
}

/**
 * ScanModal component for launching security scans
 */
const ScanModal: React.FC<ScanModalProps> = ({
  isOpen,
  onClose,
  provider: initialProvider,
  accountId: initialAccountId,
  providerId: initialProviderId,
  onProviderChange,
  accounts = [],
}) => {
  const queryClient = useQueryClient();
  const { data: allAccounts = [] } = useAccounts();
  const { user } = useAuth();
  const { hasPermission } = usePermissionContext();
  const lastUsedAccountId = useScanStore((state) => state.lastUsedAccountId);
  const setLastUsedAccountId = useScanStore(
    (state) => state.setLastUsedAccountId
  );

  // Check if user has permission to scan
  const canScan = hasPermission(PERMISSIONS.SCAN_SERVICE);

  // State for account selection
  const [selectedAccountId, setSelectedAccountId] = useState<string | null>(
    null
  );
  const [accountError, setAccountError] = useState<string | null>(null);
  const [isRefreshingAccounts, setIsRefreshingAccounts] = useState(false);

  // State for scan configuration
  const [selectedProvider, setSelectedProvider] =
    useState<CloudProviderName | null>(null);
  const [selectedProviderId, setSelectedProviderId] = useState<number | null>(
    null
  );
  const [selectedServices, setSelectedServices] = useState<number[]>([]);
  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);

  const setServices = useProviderStore((state) => state.setServices);
  const { mutateAsync: startScan, isPending: isStarting } = useCreateScan();

  // Combine provided accounts with accounts from store
  const availableAccounts = useMemo(() => {
    return accounts.length > 0 ? accounts : allAccounts;
  }, [accounts, allAccounts]);

  // Initialize account and provider when modal opens
  useEffect(() => {
    if (isOpen) {
      // Initialize with provided values or find the most recently used account
      if (initialAccountId) {
        setSelectedAccountId(initialAccountId.toString());

        // Find the account to get its provider
        const account = availableAccounts.find(
          (acc) => acc.id.toString() === initialAccountId.toString()
        );

        if (account) {
          setSelectedProvider(account.cloud_provider_name as CloudProviderName);
          setSelectedProviderId(account.cloud_provider_id);
        } else if (initialProvider && initialProviderId) {
          setSelectedProvider(initialProvider);
          setSelectedProviderId(initialProviderId);
        }
      } else if (lastUsedAccountId) {
        // Try to find the last used account
        const lastAccount = availableAccounts.find(
          (acc) => acc.id.toString() === lastUsedAccountId
        );

        if (lastAccount) {
          setSelectedAccountId(lastUsedAccountId);
          setSelectedProvider(
            lastAccount.cloud_provider_name as CloudProviderName
          );
          setSelectedProviderId(lastAccount.cloud_provider_id);
        } else if (availableAccounts.length > 0) {
          // Fall back to the first account
          const firstAccount = availableAccounts[0];
          setSelectedAccountId(firstAccount.id.toString());
          setSelectedProvider(
            firstAccount.cloud_provider_name as CloudProviderName
          );
          setSelectedProviderId(firstAccount.cloud_provider_id);
        }
      } else if (availableAccounts.length > 0) {
        // No last used account, select the first one
        const firstAccount = availableAccounts[0];
        setSelectedAccountId(firstAccount.id.toString());
        setSelectedProvider(
          firstAccount.cloud_provider_name as CloudProviderName
        );
        setSelectedProviderId(firstAccount.cloud_provider_id);
      }
    }
  }, [
    isOpen,
    initialAccountId,
    initialProvider,
    initialProviderId,
    lastUsedAccountId,
    availableAccounts,
  ]);

  // Fetch services with improved error handling
  const {
    data: servicesData,
    isLoading: isLoadingServices,
    error: servicesError,
  } = useQuery({
    queryKey: [QUERY_KEYS.SERVICES, selectedProviderId],
    queryFn: () => {
      if (!selectedProviderId) throw new Error("Provider ID is required");
      return getServices(selectedProviderId);
    },
    enabled: isOpen && !!selectedProviderId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors
      if (
        error instanceof ServiceError &&
        error.statusCode &&
        error.statusCode >= 400 &&
        error.statusCode < 500
      ) {
        return false;
      }
      return failureCount < 3;
    },
  });

  // Fetch regions with the same improved pattern
  const {
    data: regionsData,
    isLoading: isLoadingRegions,
    error: regionsError,
  } = useRegions(selectedProviderId);

  // Set error from API responses
  useEffect(() => {
    if (servicesError) {
      setError(
        servicesError instanceof ServiceError
          ? servicesError.message
          : "Failed to load services. Please try again."
      );
    } else if (regionsError) {
      setError(
        regionsError instanceof ServiceError
          ? regionsError.message
          : "Failed to load regions. Please try again."
      );
    } else {
      setError(null);
    }
  }, [servicesError, regionsError]);

  // Update store when services data changes
  useEffect(() => {
    if (servicesData?.data) {
      setServices(servicesData.data);
    }
  }, [servicesData, setServices]);

  // Reset selections when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setSelectedServices([]);
      setSelectedRegions([]);
      setError(null);
    }
  }, [isOpen]);

  // Memoize data for UI to prevent unnecessary re-renders
  const services = useMemo(() => servicesData?.data || [], [servicesData]);
  const regions = useMemo(
    () => regionsData?.data?.regions || [],
    [regionsData]
  );

  /**
   * Handle services selection change
   * @param selectedIds - The selected service IDs
   */
  const handleServicesChange = useCallback(
    (selectedIds: (string | number)[]) => {
      setError(null);
      setSelectedServices(selectedIds as number[]);
    },
    []
  );

  /**
   * Handle regions selection change
   * @param selectedIds - The selected region IDs
   */
  const handleRegionsChange = useCallback(
    (selectedIds: (string | number)[]) => {
      setError(null);
      setSelectedRegions(selectedIds as string[]);
    },
    []
  );

  /**
   * Handle account selection change
   */
  const handleAccountChange = useCallback(
    (accountId: string | number) => {
      setAccountError(null);
      setSelectedAccountId(accountId.toString());

      // Find the account to get its provider
      const account = availableAccounts.find(
        (acc) => acc.id.toString() === accountId.toString()
      );

      if (account) {
        setSelectedProvider(account.cloud_provider_name as CloudProviderName);
        setSelectedProviderId(account.cloud_provider_id);

        // Reset selections when account changes
        setSelectedServices([]);
        setSelectedRegions([]);
      }
    },
    [availableAccounts]
  );

  /**
   * Handle refreshing the accounts list
   */
  const handleRefreshAccounts = useCallback(async () => {
    try {
      setIsRefreshingAccounts(true);
      await queryClient.invalidateQueries({ queryKey: ["accounts"] });
      toast.success("Accounts refreshed successfully");
    } catch {
      toast.error("Failed to refresh accounts");
    } finally {
      setIsRefreshingAccounts(false);
    }
  }, [queryClient]);

  /**
   * Handle starting a scan
   * Validates inputs and calls the API
   */
  const handleStartScan = useCallback(async () => {
    try {
      setError(null);
      setAccountError(null);

      // Check if user has permission to scan
      if (!canScan) {
        setError("You don't have permission to launch scans");
        toast.error("Permission denied: Cannot launch scans");
        return;
      }

      // Validate account selection
      if (!selectedAccountId) {
        setAccountError("Please select an account to scan");
        return;
      }

      // Validate provider
      if (!selectedProvider || !selectedProviderId) {
        setError("Invalid cloud provider");
        return;
      }

      // Validate selections
      if (selectedServices.length === 0) {
        setError("Please select at least one service to scan");
        return;
      }

      if (selectedRegions.length === 0) {
        setError("Please select at least one region to scan");
        return;
      }

      // Convert accountId to number
      const numericAccountId = parseInt(selectedAccountId, 10);

      // Make sure accountId is a valid number
      if (isNaN(numericAccountId)) {
        setAccountError("Invalid account ID");
        return;
      }

      // Get cloud provider name (lowercase)
      const cloudProviderName = selectedProvider.toLowerCase();

      // Validate user authentication
      if (!user) {
        setError("User authentication required. Please log in again.");
        return;
      }

      // Call the API to start the scan with authenticated user ID
      await startScan({
        user_id: user.user_id,
        account_id: numericAccountId,
        cloud_provider_id: selectedProviderId,
        cloud_provider_name: cloudProviderName,
        regions: selectedRegions,
        services: selectedServices,
      });

      // Save the last used account
      setLastUsedAccountId(selectedAccountId);

      // Invalidate relevant queries to ensure fresh data
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ACCOUNTS] });

      onClose();
    } catch (error) {
      // Show error message
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to start scan. Please try again.";

      setError(errorMessage);
      toast.error(errorMessage);
    }
  }, [
    canScan,
    selectedAccountId,
    selectedProvider,
    selectedProviderId,
    selectedRegions,
    selectedServices,
    setLastUsedAccountId,
    onClose,
    queryClient,
    startScan,
    user,
  ]);

  /**
   * Handle closing the modal
   * Resets state and calls the onClose prop
   */
  const handleClose = useCallback(() => {
    setSelectedServices([]);
    setSelectedRegions([]);
    setError(null);

    // If an account was selected, invalidate its details to ensure fresh data
    if (selectedAccountId) {
      const numericAccountId = parseInt(selectedAccountId, 10);
      if (!isNaN(numericAccountId)) {
        // Also invalidate using the ACCOUNT_QUERY_KEYS constant for consistency
        queryClient.invalidateQueries({
          queryKey: [ACCOUNT_QUERY_KEYS.ACCOUNT_DETAILS, numericAccountId],
        });
      }
    }

    onClose();
  }, [onClose, selectedAccountId, queryClient]);

  /**
   * Provider options for selection
   */
  const providerOptions: CloudProviderName[] = ["AWS", "GCP", "Azure"];

  /**
   * Get the icon for a provider
   * @param providerName - The provider name
   * @returns The provider icon component
   */
  const getProviderIcon = (providerName: CloudProviderName) => {
    switch (providerName) {
      case "AWS":
        return <CloudIcon size={24} className="text-accent-500" />;
      case "GCP":
        return <Database size={24} className="text-primary-500" />;
      case "Azure":
        return <Cloud size={24} className="text-secondary-500" />;
      default:
        return <Cloud size={24} />;
    }
  };

  /**
   * Render a loading skeleton for select components
   */
  const renderSelectSkeleton = () => (
    <div className="space-y-2">
      <Skeleton className="h-10 w-full" />
      <div className="flex gap-2">
        <Skeleton className="h-6 w-20" />
        <Skeleton className="h-6 w-24" />
        <Skeleton className="h-6 w-16" />
      </div>
    </div>
  );

  /**
   * Render the error message if there is one
   */
  const renderError = useMemo(() => {
    if (!error) return null;

    return (
      <div className="mb-6 p-3 bg-error-500/10 border border-error-500/30 rounded-md text-error-400 text-sm flex items-start">
        <AlertCircle size={16} className="mr-2 mt-0.5 flex-shrink-0" />
        <span>{error}</span>
      </div>
    );
  }, [error]);

  /**
   * Render the account selection section
   */
  const renderAccountSelection = () => {
    // Map accounts to select options
    const accountOptions = availableAccounts.map((account) => ({
      id: account.id.toString(),
      label: account.account_name,
      description: account.account_id,
      icon: getProviderIcon(account.cloud_provider_name as CloudProviderName),
    }));

    return (
      <div className="mb-6">
        <div className="flex justify-between items-center mb-3">
          <h4 className="font-medium text-dark-200">Select Account</h4>
          <Button
            variant="ghost"
            size="sm"
            leftIcon={<RefreshCw size={14} />}
            onClick={handleRefreshAccounts}
            disabled={isRefreshingAccounts}
          >
            Refresh
          </Button>
        </div>

        {availableAccounts.length === 0 ? (
          <div className="p-4 bg-dark-850 border border-dark-700 rounded-md text-center">
            <p className="text-dark-400 mb-2">No accounts available</p>
            <p className="text-sm text-dark-500">
              You need to create an account before running a scan
            </p>
          </div>
        ) : (
          <Select
            options={accountOptions}
            selectedValue={selectedAccountId}
            onChange={handleAccountChange}
            placeholder="Select an account..."
            error={accountError || undefined}
            required
          />
        )}
      </div>
    );
  };

  /**
   * Render the provider selection section
   */
  const renderProviderSelection = () => (
    <div className="mb-6">
      <h4 className="font-medium text-dark-200 mb-3">Select Provider</h4>
      <div className="grid grid-cols-3 gap-4">
        {providerOptions.map((option) => (
          <div
            key={option}
            className={`p-4 border rounded-lg cursor-pointer transition-all flex items-center gap-3 ${
              selectedProvider === option
                ? "border-primary-500 bg-primary-500/5"
                : "border-dark-700 hover:border-dark-500"
            }`}
            onClick={() => onProviderChange?.(option)}
          >
            {getProviderIcon(option)}
            <span className="text-dark-200">{option}</span>
          </div>
        ))}
      </div>
    </div>
  );

  /**
   * Render the services selection section
   */
  const renderServicesSelection = () => (
    <div className="mb-6">
      <h4 className="font-medium text-dark-200 mb-3">
        Select services to scan:
      </h4>
      {isLoadingServices ? (
        renderSelectSkeleton()
      ) : (
        <MultiSelect
          options={services.map((service) => ({
            id: service.id,
            label: service.name,
          }))}
          selectedValues={selectedServices}
          onChange={handleServicesChange}
          placeholder="Select services..."
          isLoading={isLoadingServices}
          className="mt-2"
        />
      )}
    </div>
  );

  /**
   * Render the regions selection section
   */
  const renderRegionsSelection = () => (
    <div className="mb-6">
      <h4 className="font-medium text-dark-200 mb-3">
        Select regions to scan:
      </h4>
      {isLoadingRegions ? (
        renderSelectSkeleton()
      ) : (
        <MultiSelect
          options={regions.map((region) => ({
            id: region.id,
            label: region.name,
          }))}
          selectedValues={selectedRegions}
          onChange={handleRegionsChange}
          placeholder="Select regions..."
          isLoading={isLoadingRegions}
          className="mt-2"
        />
      )}
    </div>
  );

  /**
   * Render the scan summary section
   */
  const renderScanSummary = () => {
    // Find the selected account
    const selectedAccount = availableAccounts.find(
      (acc) => acc.id.toString() === selectedAccountId
    );

    return (
      <div className="text-sm text-dark-400 bg-dark-850 border border-dark-700 rounded-lg p-4 mb-6">
        This scan will check for security best practices and potential
        vulnerabilities in your {selectedProvider || "cloud"} account
        {selectedAccount
          ? ` (${selectedAccount.account_name})`
          : ""} across {selectedRegions.length} region(s) and{" "}
        {selectedServices.length} service(s). Scan results will be available
        once completed.
      </div>
    );
  };

  /**
   * Render the action buttons
   */
  const renderActionButtons = () => (
    <div className="flex justify-end space-x-3">
      <Button variant="outline" onClick={handleClose} disabled={isStarting}>
        Cancel
      </Button>

      {canScan ? (
        <Button
          variant="primary"
          onClick={handleStartScan}
          isLoading={isStarting}
          disabled={isStarting || isLoadingServices || isLoadingRegions}
        >
          Launch Scan
        </Button>
      ) : (
        <Button
          variant="primary"
          disabled={true}
          leftIcon={<Lock size={16} />}
          title="You don't have permission to launch scans"
        >
          Launch Scan
        </Button>
      )}
    </div>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Launch Security Scan"
      size="lg"
    >
      <div className="p-6">
        {renderError}
        {renderAccountSelection()}
        {selectedAccountId && (
          <>
            {onProviderChange && renderProviderSelection()}
            {renderServicesSelection()}
            {renderRegionsSelection()}
            {renderScanSummary()}
          </>
        )}
        {renderActionButtons()}
      </div>
    </Modal>
  );
};

export default ScanModal;
