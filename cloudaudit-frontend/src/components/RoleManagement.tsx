import {
  useCreateCustomRole,
  useDeleteCustomRole,
  useUpdateCustomRole,
} from "@/hooks/useUserManagement";
import type { Permission, Role } from "@/types/api";
import { AlertCircle, Edit, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";

// Components
import Badge from "@components/ui/Badge";
import Button from "@components/ui/Button";
import { Card, CardContent, CardHeader, CardTitle } from "@components/ui/Card";
import Checkbox from "@components/ui/Checkbox";
import Input from "@components/ui/Input";
import Modal from "@components/ui/Modal";
import { useUserStore } from "@/stores/userStore";

// Types
interface RoleFormData {
  id?: number;
  name: string;
  permissions: Permission[];
}

interface RoleFormErrors {
  name?: string;
  permissions?: string;
}

// Sub-components
const RoleForm = ({
  formData,
  setFormData,
  permissions,
  errors,
  onSubmit,
  onCancel,
  isLoading,
  submitLabel,
}: {
  formData: RoleFormData;
  setFormData: React.Dispatch<React.SetStateAction<RoleFormData>>;
  permissions: Permission[];
  errors: RoleFormErrors;
  onSubmit: (e: React.FormEvent) => void;
  onCancel: () => void;
  isLoading: boolean;
  submitLabel: string;
}) => {
  const togglePermission = (permission: Permission) => {
    setFormData((prev) => {
      const isSelected = prev.permissions.some((p) => p.id === permission.id);
      return {
        ...prev,
        permissions: isSelected
          ? prev.permissions.filter((p) => p.id !== permission.id)
          : [...prev.permissions, permission],
      };
    });
  };

  return (
    <form onSubmit={onSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-1">Role Name</label>
        <Input
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          placeholder="Enter role name"
          error={errors.name}
        />
        {errors.name && (
          <p className="text-red-500 text-xs mt-1 flex items-center">
            <AlertCircle size={12} className="mr-1" />
            {errors.name}
          </p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">Permissions</label>
        <div className="max-h-60 overflow-y-auto p-2 border border-dark-700 rounded-md">
          <div className="space-y-2">
            {permissions.map((permission) => (
              <div key={permission.id} className="flex items-center space-x-2">
                <Checkbox
                  checked={formData.permissions.some(
                    (p) => p.id === permission.id
                  )}
                  onChange={() => togglePermission(permission)}
                  label={permission.name}
                />
              </div>
            ))}
          </div>
        </div>
        {errors.permissions && (
          <p className="text-red-500 text-xs mt-1 flex items-center">
            <AlertCircle size={12} className="mr-1" />
            {errors.permissions}
          </p>
        )}
      </div>

      <div className="flex justify-end space-x-2 pt-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" isLoading={isLoading}>
          {submitLabel}
        </Button>
      </div>
    </form>
  );
};

const DeleteConfirmationModal = ({
  isOpen,
  onClose,
  onConfirm,
  roleName,
  isLoading,
}: {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  roleName: string;
  isLoading: boolean;
}) => {
  if (!isOpen) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Confirm Deletion">
      <div className="p-6">
        <div className="flex items-start mb-4">
          <div className="mr-3 mt-0.5">
            <AlertCircle className="h-6 w-6 text-red-500" />
          </div>
          <div>
            <h3 className="text-lg font-medium text-dark-100">
              Delete Role: {roleName}
            </h3>
            <p className="text-dark-400 mt-1">
              Are you sure you want to delete this role? This action cannot be
              undone. Users assigned to this role will lose these permissions.
            </p>
          </div>
        </div>

        <div className="flex justify-end space-x-3 mt-6">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            variant="error"
            onClick={onConfirm}
            isLoading={isLoading}
            leftIcon={<Trash2 size={16} />}
          >
            Delete Role
          </Button>
        </div>
      </div>
    </Modal>
  );
};

const PermissionBadge = ({ name }: { name: string }) => {
  return (
    <Badge variant="neutral" size="sm" className="mr-1 mb-1">
      {name}
    </Badge>
  );
};

// Main component
export const RoleManagement = ({
  isCreateModalOpen,
  setIsCreateModalOpen,
  customRoles,
  permissions,
}: {
  isCreateModalOpen: boolean;
  setIsCreateModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  customRoles: Role[];
  permissions: Permission[];
}) => {
  // State
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [roleToDelete, setRoleToDelete] = useState<Role | null>(null);
  const [formErrors, setFormErrors] = useState<RoleFormErrors>({});
  const [roleForm, setRoleForm] = useState<RoleFormData>({
    name: "",
    permissions: [],
  });

  // Operation states
  const {
    roleOperations: { isCreating, isUpdating, isDeleting },
  } = useUserStore();

  const createRoleMutation = useCreateCustomRole();
  const updateRoleMutation = useUpdateCustomRole();
  const deleteRoleMutation = useDeleteCustomRole();

  // Reset form when modals close
  useEffect(() => {
    if (!isCreateModalOpen && !isEditModalOpen) {
      setRoleForm({ name: "", permissions: [] });
      setFormErrors({});
    }
  }, [isCreateModalOpen, isEditModalOpen]);

  // Form validation
  const validateForm = (): boolean => {
    const errors: RoleFormErrors = {};

    if (!roleForm.name.trim()) {
      errors.name = "Role name is required";
    } else if (roleForm.name.length < 3) {
      errors.name = "Role name must be at least 3 characters";
    } else if (roleForm.name.length > 50) {
      errors.name = "Role name must be less than 50 characters";
    }

    if (roleForm.permissions.length === 0) {
      errors.permissions = "At least one permission must be selected";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handlers
  const handleCreateRole = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      await createRoleMutation.mutateAsync({
        name: roleForm.name,
        permissions: roleForm.permissions,
      });

      toast.success("Custom role created successfully");
      setIsCreateModalOpen(false);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create custom role";

      toast.error(errorMessage);
    }
  };

  const handleUpdateRole = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedRole || !validateForm()) return;

    try {
      await updateRoleMutation.mutateAsync({
        id: selectedRole.id,
        name: roleForm.name,
        permissions: roleForm.permissions,
      });

      toast.success("Custom role updated successfully");
      setIsEditModalOpen(false);
      setSelectedRole(null);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update custom role";

      toast.error(errorMessage);
    }
  };

  const handleDeleteRole = async () => {
    if (!roleToDelete) return;

    try {
      await deleteRoleMutation.mutateAsync(roleToDelete.id);
      toast.success("Custom role deleted successfully");
      setIsDeleteModalOpen(false);
      setRoleToDelete(null);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to delete custom role";

      toast.error(errorMessage);
    }
  };

  const handleEditRole = (role: Role) => {
    setSelectedRole(role);
    setRoleForm({
      id: role.id,
      name: role.name,
      permissions: role.permissions,
    });
    setIsEditModalOpen(true);
  };

  const handleDeleteClick = (role: Role) => {
    setRoleToDelete(role);
    setIsDeleteModalOpen(true);
  };

  // Render
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-6">
          <CardTitle className="text-xl">Custom Roles</CardTitle>
        </CardHeader>

        <CardContent>
          {customRoles.length === 0 ? (
            <div className="text-center py-8 text-dark-400">
              <p>No custom roles created yet.</p>
              {/* <Button
                onClick={() => setIsCreateModalOpen(true)}
                variant="outline"
                className="mt-4"
              >
                Create Your First Role
              </Button> */}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-dark-700">
                    <th className="text-left py-3 px-4 text-dark-300 font-medium">
                      Name
                    </th>
                    <th className="text-left py-3 px-4 text-dark-300 font-medium">
                      Permissions
                    </th>
                    <th className="text-right py-3 px-4 text-dark-300 font-medium">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {customRoles.map((role) => (
                    <tr
                      key={role.id}
                      className="border-b border-dark-700/50 hover:bg-dark-800/30 transition-colors"
                    >
                      <td className="py-3 px-4 font-medium text-dark-200">
                        {role.name}
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex flex-wrap">
                          {role.permissions.length > 0 ? (
                            role.permissions.map((permission) => (
                              <PermissionBadge
                                key={permission.id}
                                name={permission.name}
                              />
                            ))
                          ) : (
                            <span className="text-dark-500 text-sm">
                              No permissions
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="py-3 px-4 text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditRole(role)}
                            disabled={isUpdating[role.id]}
                            className="text-dark-400 hover:text-primary-400"
                            leftIcon={<Edit size={16} />}
                          >
                            Edit
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteClick(role)}
                            disabled={isDeleting[role.id]}
                            className="text-dark-400 hover:text-red-500"
                            leftIcon={<Trash2 size={16} />}
                          >
                            Delete
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Role Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Create Custom Role"
      >
        <div className="p-6">
          <RoleForm
            formData={roleForm}
            setFormData={setRoleForm}
            permissions={permissions}
            errors={formErrors}
            onSubmit={handleCreateRole}
            onCancel={() => setIsCreateModalOpen(false)}
            isLoading={isCreating || createRoleMutation.isPending}
            submitLabel="Create Role"
          />
        </div>
      </Modal>

      {/* Edit Role Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title={`Edit Role: ${selectedRole?.name}`}
      >
        <div className="p-6">
          <RoleForm
            formData={roleForm}
            setFormData={setRoleForm}
            permissions={permissions}
            errors={formErrors}
            onSubmit={handleUpdateRole}
            onCancel={() => setIsEditModalOpen(false)}
            isLoading={
              selectedRole
                ? isUpdating[selectedRole.id] || updateRoleMutation.isPending
                : false
            }
            submitLabel="Update Role"
          />
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDeleteRole}
        roleName={roleToDelete?.name || ""}
        isLoading={
          roleToDelete
            ? isDeleting[roleToDelete.id] || deleteRoleMutation.isPending
            : false
        }
      />
    </div>
  );
};
