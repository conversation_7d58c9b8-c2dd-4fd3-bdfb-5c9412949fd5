import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  CloudIcon,
  Database,
  Cloud,
  ArrowRight,
  KeyRound,
  EyeIcon,
  EyeOffIcon,
  AlertCircle,
  AlertTriangle,
} from "lucide-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import Modal from "./ui/Modal";
import Button from "./ui/Button";
import Input from "./ui/Input";
import { CloudProviderName } from "../stores/cloudProviderStore";
import { testConnection } from "../services/account";
import { useAddAccount } from "@hooks/useAccounts";
import { useCloudProviders } from "@hooks/useCloudProviders";
import type { AddAccountRequest } from "../types/api";

interface NewProviderModalProps {
  isOpen: boolean;
  onClose: () => void;
  provider: CloudProviderName | null;
}

const schema = z.object({
  accountName: z.string().min(1, "Account name is required"),
  accessKeyId: z.string().min(1, "Access Key ID is required"),
  secretAccessKey: z.string().min(1, "Secret Access Key is required"),
});

type FormValues = z.infer<typeof schema>;

const NewProviderModal: React.FC<NewProviderModalProps> = ({
  isOpen,
  onClose,
  provider,
}) => {
  const [step, setStep] = useState(1);
  const [showSecret, setShowSecret] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedProvider, setSelectedProvider] =
    useState<CloudProviderName | null>(provider);
  const queryClient = useQueryClient();

  // Use the new React Query hooks
  const { mutateAsync: addAccountMutate } = useAddAccount();

  // Fetch cloud providers to check which ones are enabled
  const { cloudProviders } = useCloudProviders();

  // Update selectedProvider when provider prop changes
  useEffect(() => {
    setSelectedProvider(provider);
  }, [provider]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<FormValues>({
    resolver: zodResolver(schema),
  });

  const testConnectionMutation = useMutation({
    mutationFn: async (data: FormValues) => {
      if (!selectedProvider) throw new Error("No provider selected");

      const providerIdMap: Record<CloudProviderName, number> = {
        AWS: 1,
        GCP: 2,
        Azure: 3,
      };

      return testConnection({
        cloud_provider_id: providerIdMap[selectedProvider],
        account_name: data.accountName,
        is_organization: false,
        credentials: {
          access_key: data.accessKeyId,
          secret_key: data.secretAccessKey,
        },
      });
    },
    onSuccess: () => {
      setError(null);
      setStep(3);
    },
    onError: (error: Error) => {
      setError(error.message);
    },
  });

  const addAccountMutation = useMutation({
    mutationFn: async (data: FormValues) => {
      if (!selectedProvider) throw new Error("No provider selected");

      const providerIdMap: Record<CloudProviderName, number> = {
        AWS: 1,
        GCP: 2,
        Azure: 3,
      };

      const accountRequest: AddAccountRequest = {
        cloud_provider_id: providerIdMap[selectedProvider],
        account_name: data.accountName,
        is_organization: false,
        credentials: {
          access_key: data.accessKeyId,
          secret_key: data.secretAccessKey,
        },
      };

      // Use the mutation from useAddAccount hook
      return addAccountMutate(accountRequest);
    },
    onSuccess: () => {
      handleClose();
    },
    onError: (error: Error) => {
      setError(error.message);
    },
  });

  const handleTestConnection = async (data: FormValues) => {
    setError(null);
    testConnectionMutation.mutate(data);
  };

  const handleClose = () => {
    reset();
    setStep(1);
    setError(null);
    setSelectedProvider(provider); // Reset to the initial provider prop
    onClose();
  };

  const getProviderIcon = (provider: CloudProviderName | null) => {
    if (!provider) return null;

    switch (provider) {
      case "AWS":
        return <CloudIcon size={24} className="text-accent-500" />;
      case "GCP":
        return <Database size={24} className="text-primary-500" />;
      case "Azure":
        return <Cloud size={24} className="text-secondary-500" />;
      default:
        return <Cloud size={24} />;
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={`Step ${step} of 3`}
      size="md"
    >
      <div className="p-6">
        {error && (
          <div className="mb-6 p-3 bg-error-500/10 border border-error-500/30 rounded-md text-error-400 text-sm flex items-start">
            <AlertCircle size={16} className="mr-2 mt-0.5 flex-shrink-0" />
            <span>{error}</span>
          </div>
        )}

        {step === 1 && (
          <div>
            <h3 className="text-xl font-semibold text-dark-100 mb-3">
              Select Provider Type
            </h3>
            <p className="text-dark-400 mb-6">
              Choose the cloud provider you want to connect to your workspace.
            </p>

            <div className="space-y-4 mb-6">
              {(["AWS", "GCP", "Azure"] as CloudProviderName[]).map(
                (providerOption) => {
                  // Find the provider in the cloudProviders array to check if it's enabled
                  const providerData = cloudProviders.find(
                    (p) => p.name === providerOption
                  );
                  const isEnabled = providerData?.is_enable !== false; // Default to true if not found

                  return (
                    <div
                      key={providerOption}
                      className={`flex items-center p-4 border rounded-lg transition-all ${
                        selectedProvider === providerOption
                          ? "border-primary-500 bg-primary-500/5"
                          : "border-dark-700 hover:border-dark-500"
                      } ${
                        isEnabled
                          ? "cursor-pointer"
                          : "cursor-not-allowed opacity-60 bg-dark-800/30"
                      }`}
                      onClick={
                        isEnabled
                          ? () => setSelectedProvider(providerOption)
                          : undefined
                      }
                    >
                      <div className="mr-4">
                        {getProviderIcon(providerOption)}
                      </div>
                      <div className="flex-grow">
                        <h4 className="font-medium text-dark-200">
                          {providerOption}
                        </h4>
                        <p className="text-sm text-dark-400">
                          {isEnabled
                            ? `Connect to ${providerOption} services`
                            : `${providerOption} integration is currently`}
                        </p>
                      </div>
                      {!isEnabled && (
                        <div className="ml-2 px-2 py-1 bg-dark-800/70 rounded-md text-xs text-dark-300 flex items-center">
                          <AlertTriangle size={12} className="mr-1" />
                          Disabled
                        </div>
                      )}
                    </div>
                  );
                }
              )}
            </div>

            <div className="flex justify-end">
              <Button
                variant="primary"
                onClick={() => setStep(2)}
                rightIcon={<ArrowRight size={16} />}
                disabled={!selectedProvider}
              >
                Next
              </Button>
            </div>
          </div>
        )}

        {step === 2 && selectedProvider && (
          <div>
            <h3 className="text-xl font-semibold text-dark-100 mb-3">
              Add credentials to your cloud account
            </h3>

            <form onSubmit={handleSubmit(handleTestConnection)}>
              <div className="space-y-4 mb-6">
                <Input
                  label="Account Name *"
                  placeholder="Enter a name for this account"
                  error={errors.accountName?.message}
                  fullWidth
                  {...register("accountName")}
                />

                <Input
                  label={`${selectedProvider} Access Key ID *`}
                  placeholder={`Enter ${selectedProvider} Access Key ID`}
                  leftIcon={<KeyRound size={16} />}
                  error={errors.accessKeyId?.message}
                  fullWidth
                  {...register("accessKeyId")}
                />

                <Input
                  label={`${selectedProvider} Secret Access Key *`}
                  placeholder={`Enter ${selectedProvider} Secret Access Key`}
                  type={showSecret ? "text" : "password"}
                  leftIcon={<KeyRound size={16} />}
                  rightIcon={
                    <button
                      type="button"
                      onClick={() => setShowSecret(!showSecret)}
                      className="focus:outline-none"
                    >
                      {showSecret ? (
                        <EyeOffIcon size={16} />
                      ) : (
                        <EyeIcon size={16} />
                      )}
                    </button>
                  }
                  error={errors.secretAccessKey?.message}
                  fullWidth
                  {...register("secretAccessKey")}
                />
              </div>

              <div className="flex justify-end space-x-3">
                <Button variant="outline" onClick={() => setStep(1)}>
                  Back
                </Button>
                <Button
                  type="submit"
                  variant="primary"
                  isLoading={testConnectionMutation.isPending}
                >
                  Test Connection
                </Button>
              </div>
            </form>
          </div>
        )}

        {step === 3 && (
          <div>
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-success-500/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg
                  className="w-8 h-8 text-success-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-dark-100 mb-2">
                Connection Successful!
              </h3>
              <p className="text-dark-400">
                Your {selectedProvider} credentials are valid. You can now add
                this account to your workspace.
              </p>
            </div>

            <div className="flex justify-end space-x-3">
              <Button
                variant="primary"
                onClick={() => {
                  queryClient.invalidateQueries({ queryKey: ["accounts"] });
                  handleClose();
                }}
                isLoading={addAccountMutation.isPending}
              >
                OK
              </Button>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default NewProviderModal;
