import React from "react";
import { motion } from "framer-motion";
import { cn } from "@utils/cn";

interface CardProps {
  className?: string;
  children: React.ReactNode;
  animate?: boolean;
  onClick?: () => void;
}

export const Card: React.FC<CardProps> = ({
  className,
  children,
  animate = false,
  onClick,
  ...props
}) => {
  const CardComponent = animate ? motion.div : "div";

  return (
    <CardComponent
      className={cn(
        "bg-dark-800 rounded-xl border border-dark-700 overflow-hidden shadow-glass",
        onClick &&
          "cursor-pointer hover:border-primary-500/50 transition-all duration-300",
        className
      )}
      onClick={onClick}
      {...(animate && {
        initial: { opacity: 0, y: 10 },
        animate: { opacity: 1, y: 0 },
        transition: { duration: 0.3 },
      })}
      {...props}
    >
      {children}
    </CardComponent>
  );
};

interface CardHeaderProps {
  className?: string;
  children: React.ReactNode;
}

export const CardHeader: React.FC<CardHeaderProps> = ({
  className,
  children,
}) => {
  return (
    <div className={cn("p-6 border-b border-dark-700", className)}>
      {children}
    </div>
  );
};

interface CardTitleProps {
  className?: string;
  children: React.ReactNode;
}

export const CardTitle: React.FC<CardTitleProps> = ({
  className,
  children,
}) => {
  return (
    <h3 className={cn("text-xl font-semibold text-dark-100", className)}>
      {children}
    </h3>
  );
};

interface CardDescriptionProps {
  className?: string;
  children: React.ReactNode;
}

export const CardDescription: React.FC<CardDescriptionProps> = ({
  className,
  children,
}) => {
  return (
    <p className={cn("text-sm text-dark-400 mt-1", className)}>{children}</p>
  );
};

interface CardContentProps {
  className?: string;
  children: React.ReactNode;
}

export const CardContent: React.FC<CardContentProps> = ({
  className,
  children,
}) => {
  return <div className={cn("p-6", className)}>{children}</div>;
};

interface CardFooterProps {
  className?: string;
  children: React.ReactNode;
}

export const CardFooter: React.FC<CardFooterProps> = ({
  className,
  children,
}) => {
  return (
    <div className={cn("p-6 border-t border-dark-700 bg-dark-850", className)}>
      {children}
    </div>
  );
};
