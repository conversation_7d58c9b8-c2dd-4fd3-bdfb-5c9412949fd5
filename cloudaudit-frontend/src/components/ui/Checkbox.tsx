import React, { forwardRef } from "react";
import { cn } from "@utils/cn";
import { Check } from "lucide-react";

interface CheckboxProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "type"> {
  label?: string;
  error?: string;
}

const Checkbox = forwardRef<HTMLInputElement, CheckboxProps>(
  ({ label, error, className, onChange, checked, ...props }, ref) => {
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (onChange) {
        onChange(e);
      }
    };

    // Generate a unique ID for the checkbox if not provided
    const id =
      props.id || `checkbox-${Math.random().toString(36).substring(2, 9)}`;

    return (
      <div className="flex items-start">
        {/* Wrap the entire component in a label to make both checkbox and text clickable */}
        <label htmlFor={id} className="flex items-start cursor-pointer">
          <div className="relative flex items-center h-5">
            <input
              ref={ref}
              id={id}
              type="checkbox"
              checked={checked}
              onChange={handleChange}
              className="absolute w-5 h-5 opacity-0 cursor-pointer"
              {...props}
            />
            <div
              className={cn(
                "w-5 h-5 border rounded flex items-center justify-center transition-colors cursor-pointer",
                checked
                  ? "bg-primary-500 border-primary-500"
                  : "border-dark-600 bg-dark-800 hover:border-primary-500/50",
                props.disabled && "opacity-50 cursor-not-allowed",
                error && "border-error-500",
                className
              )}
            >
              {checked && <Check size={14} className="text-white" />}
            </div>
          </div>
          {label && (
            <span
              className={cn(
                "ml-2 text-sm font-medium text-dark-300 select-none",
                props.disabled && "opacity-50 cursor-not-allowed"
              )}
            >
              {label}
            </span>
          )}
        </label>
      </div>
    );
  }
);

Checkbox.displayName = "Checkbox";

export default Checkbox;
