import React from "react";
import { Shield } from "lucide-react";
import { motion } from "framer-motion";

interface LoadingScreenProps {
  /**
   * Optional message to display below the loading indicator
   */
  message?: string;

  /**
   * Optional progress value (0-100) to show a progress indicator
   */
  progress?: number;

  /**
   * Whether to show the loading screen in a full-page layout
   * @default true
   */
  fullPage?: boolean;

  /**
   * Optional className to apply to the container
   */
  className?: string;
}

/**
 * A branded loading screen component that can be used throughout the application
 */
const LoadingScreen: React.FC<LoadingScreenProps> = ({
  message = "Loading...",
  progress,
  fullPage = true,
  className = "",
}) => {
  return (
    <div
      className={`flex flex-col items-center justify-center bg-dark-950 ${
        fullPage ? "min-h-screen" : "h-full py-16"
      } ${className}`}
    >
      <div className="relative">
        {/* Background gradient effect */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute w-64 h-64 bg-primary-500 rounded-full -left-32 -top-32 opacity-20 blur-3xl" />
          <div className="absolute w-64 h-64 bg-secondary-500 rounded-full -right-32 -bottom-32 opacity-20 blur-3xl" />
        </div>

        <div className="relative z-10 flex flex-col items-center">
          {/* Logo container with gradient background */}
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="bg-gradient-to-br from-primary-500 to-secondary-500 p-4 rounded-2xl shadow-xl mb-6"
          >
            <Shield size={48} className="text-white" />
          </motion.div>

          {/* App name */}
          <motion.h1
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-2xl font-bold text-white mb-2"
          >
            CloudAudit
          </motion.h1>

          {/* Loading message */}
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="text-dark-300 mb-6"
          >
            {message}
          </motion.p>

          {/* Progress bar (if progress is provided) */}
          {typeof progress === "number" ? (
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: "100%" }}
              className="w-64 bg-dark-800 rounded-full h-2 overflow-hidden"
            >
              <motion.div
                initial={{ width: "0%" }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.5 }}
                className="h-full bg-gradient-to-r from-primary-500 to-secondary-500"
              />
            </motion.div>
          ) : (
            /* Animated loading indicator */
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="relative w-12 h-12"
            >
              <div className="absolute inset-0 rounded-full border-4 border-dark-700" />
              <div className="absolute inset-0 rounded-full border-4 border-t-primary-500 border-r-transparent border-b-transparent border-l-transparent" />
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;
