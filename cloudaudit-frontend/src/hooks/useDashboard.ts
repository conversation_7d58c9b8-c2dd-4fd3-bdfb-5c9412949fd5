import { useQueryClient } from '@tanstack/react-query';
import { getAccountDetails } from '@services/dashboard';
import { useProviderStore } from '@/stores/cloudProviderStore';
import { useEffect, useState } from 'react';
import { useAccounts } from './useAccounts';
import { useCloudProviders } from './useCloudProviders';

// Define query keys as constants for better maintainability
export const QUERY_KEYS = {
    ACCOUNTS: 'accounts',
    ACCOUNT_DETAILS: 'accountDetails',
    CLOUD_PROVIDERS: 'cloudProviders',
    SERVICES: 'services',
    REGIONS: 'regions',
};

/**
 * Custom hook for dashboard data and operations
 * @param options Configuration options
 * @returns Dashboard data and operations
 */
export const useDashboard = (options = { enablePrefetch: false }) => {
    const queryClient = useQueryClient();
    const getProviderId = useProviderStore((state) => state.getProviderId);
    const getProviderFromId = useProviderStore((state) => state.getProviderFromId);
    const setProviderMaps = useProviderStore((state) => state.setProviderMaps);
    const [isRefreshing, setIsRefreshing] = useState(false);

    // Use the new React Query hook for accounts
    const {
        data: allAccounts = [],
        error: accountsError,
        isLoading: isLoadingAccounts,
        isError: isAccountsError,
        refetch: refetchAccounts
    } = useAccounts();

    // Use the improved useCloudProviders hook
    const { cloudProviders } = useCloudProviders();

    // Update provider maps in the store when cloud providers are fetched
    useEffect(() => {
        if (cloudProviders && cloudProviders.length > 0) {
            setProviderMaps(cloudProviders);
        }
    }, [cloudProviders, setProviderMaps]);

    // Prefetch account details for better UX
    useEffect(() => {
        if (options.enablePrefetch && allAccounts && allAccounts.length > 0) {
            // Prefetch the first account's details
            const firstAccountId = allAccounts[0].id;
            queryClient.prefetchQuery({
                queryKey: [QUERY_KEYS.ACCOUNT_DETAILS, firstAccountId],
                queryFn: () => getAccountDetails(firstAccountId),
                staleTime: 60 * 1000, // 1 minute
            });
        }
    }, [allAccounts, queryClient, options.enablePrefetch]);

    // Function to refresh dashboard data
    const refreshDashboard = async () => {
        try {
            setIsRefreshing(true);
            await refetchAccounts();
        } finally {
            setIsRefreshing(false);
        }
    };

    // Function to get account details by ID
    const getAccountDetailsById = (accountId: number) => {
        return queryClient.fetchQuery({
            queryKey: [QUERY_KEYS.ACCOUNT_DETAILS, accountId],
            queryFn: () => getAccountDetails(accountId),
            staleTime: 60 * 1000, // 1 minute
        });
    };

    return {
        allAccounts,
        accountsError,
        isLoadingAccounts,
        isAccountsError,
        isRefreshing,
        cloudProviders,
        refreshDashboard,
        getAccountDetailsById,
        getProviderId,
        getProviderFromId,
    };
};
