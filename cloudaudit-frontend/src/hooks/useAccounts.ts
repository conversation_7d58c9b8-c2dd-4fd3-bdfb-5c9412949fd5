import { ServiceError } from '@/services/baseService';
import { addAccount as addAccountService, deleteAccount as deleteAccountService, getAccountDetail, getAccounts } from '@/services/account';
import { fetchAccounts } from '@/services/dashboard';
import type { Account, AccountDetailResponse, AccountsResponse, AddAccountRequest, AddAccountResponse } from '@/types/api';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';

// Define query keys as constants for better maintainability
export const QUERY_KEYS = {
    ACCOUNTS: 'accounts',
    ACCOUNT_DETAILS: 'accountDetails',
    ACCOUNT_BY_PROVIDER: 'accountsByProvider',
};

/**
 * Hook to get all accounts across all providers
 * @returns Query result with accounts data
 */
export const useAccounts = () => {
    return useQuery<
        AccountsResponse[],
        ServiceError,
        Account[]
    >({
        queryKey: [QUERY_KEYS.ACCOUNTS],
        queryFn: () => fetchAccounts(),
        select: (data: AccountsResponse[]) =>
            data.flatMap((response) => response.data.accounts),
        refetchOnMount: true,
        refetchOnWindowFocus: true,
        staleTime: 1000, // 1 minute
        retry: (failureCount, error) => {
            // Don't retry on 4xx errors, but retry up to 3 times on other errors
            if (error.statusCode && error.statusCode >= 400 && error.statusCode < 500) {
                return false;
            }
            return failureCount < 3;
        }
    });
};

/**
 * Hook to get accounts for a specific cloud provider
 * @param cloudProviderId The cloud provider ID
 * @returns Query result with accounts data
 */
export const useAccountsByProvider = (cloudProviderId: number | null) => {
    return useQuery<
        AccountsResponse,
        ServiceError,
        Account[]
    >({
        queryKey: [QUERY_KEYS.ACCOUNT_BY_PROVIDER, cloudProviderId],
        queryFn: () => {
            if (!cloudProviderId) throw new Error('Cloud provider ID is required');
            return getAccounts(cloudProviderId);
        },
        select: (data) => data.data.accounts,
        enabled: !!cloudProviderId,
        staleTime: 1000, // 1 minute
        retry: (failureCount, error) => {
            // Don't retry on 4xx errors, but retry up to 3 times on other errors
            if (error.statusCode && error.statusCode >= 400 && error.statusCode < 500) {
                return false;
            }
            return failureCount < 3;
        }
    });
};

/**
 * Hook to get detailed information for a specific account
 * @param accountId The account ID
 * @returns Query result with account details
 */
export const useAccountDetail = (accountId: number | null) => {
    return useQuery<
        AccountDetailResponse,
        ServiceError
    >({
        queryKey: [QUERY_KEYS.ACCOUNT_DETAILS, accountId],
        queryFn: () => {
            if (!accountId) throw new Error('Account ID is required');
            return getAccountDetail(accountId);
        },
        select: (response) => response,
        enabled: !!accountId,
        staleTime: 1000, // 1 minute
        retry: (failureCount, error) => {
            // Don't retry on 4xx errors, but retry up to 3 times on other errors
            if (error.statusCode && error.statusCode >= 400 && error.statusCode < 500) {
                return false;
            }
            return failureCount < 3;
        }
    });
};

/**
 * Hook to add a new account
 * @returns Mutation for adding an account
 */
export const useAddAccount = () => {
    const queryClient = useQueryClient();

    return useMutation<
        AddAccountResponse,
        Error,
        AddAccountRequest
    >({
        mutationFn: (data) => addAccountService(data),
        onSuccess: (response) => {
            // Invalidate accounts queries to refetch the latest data
            queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ACCOUNTS] });
            queryClient.invalidateQueries({
                queryKey: [QUERY_KEYS.ACCOUNT_BY_PROVIDER, response.data?.account.cloud_provider_id]
            });

            // Show success toast
            toast.success('Account added successfully');
        },
        onError: (error) => {
            toast.error(error instanceof Error
                ? error.message
                : 'Failed to add account. Please try again.');
        }
    });
};

/**
 * Hook to delete an account
 * @returns Mutation for deleting an account
 */
export const useDeleteAccount = () => {
    const queryClient = useQueryClient();

    return useMutation<
        void,
        Error,
        number
    >({
        mutationFn: (accountId) => deleteAccountService(accountId),
        onSuccess: () => {
            // Invalidate accounts queries to refetch the latest data
            queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ACCOUNTS] });
            queryClient.invalidateQueries({
                queryKey: [QUERY_KEYS.ACCOUNT_BY_PROVIDER]
            });

            // Show success toast
            toast.success('Account deleted successfully');
        },
        onError: (error) => {
            toast.error(error instanceof Error
                ? error.message
                : 'Failed to delete account. Please try again.');
        }
    });
};
