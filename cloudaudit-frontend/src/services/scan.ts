import type {
    CreateScanRequest,
    CreateScanResponse,
    GetScansResponse,
    GetRegionsResponse,
    ScanDetailsResponse,
    GetFindingsResponse,
    GetFindingDetailResponse,
    RemediateFindingResponse,
    FindingDetailItem
} from '../types/api';
import { createBaseApi } from './baseService';

const scanAPI = createBaseApi();

/**
 * Create a new scan for an account
 * @param data The scan request data
 * @returns Promise with the scan response
 */
export const createScan = async (data: CreateScanRequest): Promise<CreateScanResponse> => {
    const response = await scanAPI.post<CreateScanResponse>('/api/scans', data);
    return response.data;
};

/**
 * Get all scans with pagination
 * @param page The page number (default: 1)
 * @param pageSize The page size (default: 20)
 * @returns Promise with the scans response
 */
export const getScans = async (page: number = 1, pageSize: number = 20): Promise<GetScansResponse> => {
    const response = await scanAPI.get<GetScansResponse>('/api/scans', {
        params: { page, page_size: pageSize }
    });
    return response.data;
};

/**
 * Get scans for a specific account with pagination
 * @param accountId The account ID
 * @param page The page number (default: 1)
 * @param pageSize The page size (default: 20)
 * @returns Promise with the scans response
 */
export const getAccountScans = async (
    accountId: number,
    page: number = 1,
    pageSize: number = 20
): Promise<GetScansResponse> => {
    const response = await scanAPI.get<GetScansResponse>('/api/scans', {
        params: { account_id: accountId, page, page_size: pageSize }
    });
    return response.data;
};

/**
 * Get available regions for a cloud provider
 * @param cloudProviderId The cloud provider ID
 * @returns Promise with the regions response
 */
export const getRegions = async (cloudProviderId: number): Promise<GetRegionsResponse> => {
    const response = await scanAPI.get<GetRegionsResponse>('/api/regions', {
        params: { cloud_provider_id: cloudProviderId }
    });
    return response.data;
};

/**
 * Get detailed information for a specific scan
 * @param scanId The scan ID
 * @returns Promise with the scan details response
 */
export const getScanDetails = async (scanId: number): Promise<ScanDetailsResponse> => {
    const response = await scanAPI.get<ScanDetailsResponse>(`/api/scans/${scanId}`);
    return response.data;
};

/**
 * Get findings for a specific scan and service with pagination
 * @param scanId The scan ID
 * @param serviceId The service ID
 * @param page The page number (default: 1)
 * @param pageSize The page size (default: 20)
 * @param filters Optional filters for the findings (e.g., status)
 * @returns Promise with the findings response
 */
export const getFindings = async (
    scanId: number,
    serviceId: number,
    page: number = 1,
    pageSize: number = 20,
    filters?: { status?: string[] }
): Promise<GetFindingsResponse> => {
    const params: Record<string, string | number> = {
        scan_id: scanId,
        service_id: serviceId,
        page,
        page_size: pageSize
    };

    // Add status filter if provided
    if (filters?.status && filters.status.length > 0) {
        params.status = filters.status.join(',');
    }

    const response = await scanAPI.get<GetFindingsResponse>('/api/findings', { params });
    return response.data;
};

/**
 * Get detailed information for a specific finding
 * @param findingId The finding ID
 * @returns Promise with the finding detail response
 */
export const getFindingDetail = async (findingId: number): Promise<GetFindingDetailResponse> => {
    const response = await scanAPI.get<GetFindingDetailResponse>(`/api/findings/${findingId}`);
    return response.data;
};

/**
 * Remediate a specific finding
 * @param findingId The finding ID
 * @param detailsData The finding details data to include in the remediation request
 * @returns Promise with the remediation response
 */
export const remediateFinding = async (
    findingId: number,
    detailsData: FindingDetailItem
): Promise<RemediateFindingResponse> => {
    const response = await scanAPI.post<RemediateFindingResponse>(
        `/api/findings/${findingId}/remediate`,
        { details: detailsData }
    );
    return response.data;
};
