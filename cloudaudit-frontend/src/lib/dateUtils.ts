/**
 * Date formatting utilities for consistent date display across the application
 */

/**
 * Format options for date formatting
 */
export type DateFormatOptions = {
  /** Include time in the formatted output */
  includeTime?: boolean;
  /** Format as time only (no date) */
  timeOnly?: boolean;
  /** Use short month format (e.g., "Jan" instead of "January") */
  shortMonth?: boolean;
  /** Include seconds in time format */
  includeSeconds?: boolean;
  /** Use 24-hour format instead of 12-hour format */
  use24Hour?: boolean;
  /** Custom locale for formatting (defaults to user's locale) */
  locale?: string;
};

/**
 * Default format options
 */
const DEFAULT_FORMAT_OPTIONS: DateFormatOptions = {
  includeTime: false,
  timeOnly: false,
  shortMonth: true,
  includeSeconds: false,
  use24Hour: false,
};

/**
 * Formats a date string or Date object into a consistent, human-readable format
 * 
 * @param dateInput - Date string, Date object, or null/undefined
 * @param options - Formatting options
 * @returns Formatted date string or fallback text for invalid/null dates
 * 
 * @example
 * // Basic date formatting
 * formatDate("2023-05-15T14:30:00Z") // "May 15, 2023"
 * 
 * @example
 * // Date with time
 * formatDate("2023-05-15T14:30:00Z", { includeTime: true }) // "May 15, 2023, 2:30 PM"
 * 
 * @example
 * // Time only
 * formatDate("2023-05-15T14:30:00Z", { timeOnly: true }) // "2:30 PM"
 * 
 * @example
 * // Full month format
 * formatDate("2023-05-15T14:30:00Z", { shortMonth: false }) // "May 15, 2023"
 */
export function formatDate(
  dateInput: string | Date | null | undefined,
  options: DateFormatOptions = {}
): string {
  // Handle null, undefined or empty string
  if (dateInput === null || dateInput === undefined || dateInput === "") {
    return "N/A";
  }

  // Merge default options with provided options
  const mergedOptions = { ...DEFAULT_FORMAT_OPTIONS, ...options };
  const {
    includeTime,
    timeOnly,
    shortMonth,
    includeSeconds,
    use24Hour,
    locale,
  } = mergedOptions;

  try {
    // Parse the date - handle both string and Date object inputs
    let date: Date;
    
    if (typeof dateInput === "string") {
      // Ensure ISO format with timezone if not present
      const dateString = dateInput.endsWith("Z") ? dateInput : `${dateInput}Z`;
      date = new Date(dateString);
    } else {
      date = dateInput;
    }

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return "Invalid date";
    }

    // Format time only if requested
    if (timeOnly) {
      return date.toLocaleTimeString(locale, {
        hour: "2-digit",
        minute: "2-digit",
        second: includeSeconds ? "2-digit" : undefined,
        hour12: !use24Hour,
      });
    }

    // Format date part
    const datePart = timeOnly ? "" : date.toLocaleDateString(locale, {
      year: "numeric",
      month: shortMonth ? "short" : "long",
      day: "numeric",
    });

    // Format time part if requested
    const timePart = includeTime ? date.toLocaleTimeString(locale, {
      hour: "2-digit",
      minute: "2-digit",
      second: includeSeconds ? "2-digit" : undefined,
      hour12: !use24Hour,
    }) : "";

    // Combine date and time parts
    if (includeTime && !timeOnly) {
      return `${datePart}, ${timePart}`;
    }
    
    return datePart;
  } catch (error) {
    console.error("Error formatting date:", error);
    return "Error formatting date";
  }
}
