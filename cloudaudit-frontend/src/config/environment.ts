/**
 * Environment Configuration
 * Centralized configuration management for different environments
 */

export type Environment = 'development' | 'staging' | 'production';

export interface EnvironmentConfig {
  // Application
  APP_ENV: Environment;
  APP_NAME: string;
  APP_VERSION: string;
  APP_BASE_URL: string;

  // API
  BACKEND_BASE_URL: string;
  API_TIMEOUT: number;
  MAX_RETRIES: number;

  // Feature Flags
  ENABLE_DEVTOOLS: boolean;
  ENABLE_DEBUG_LOGGING: boolean;
  ENABLE_PERFORMANCE_MONITORING: boolean;
  ENABLE_ERROR_REPORTING: boolean;

  // Security
  TOKEN_REFRESH_THRESHOLD: number;
  SESSION_TIMEOUT: number;

  // Third-party Services
  SENTRY_DSN?: string;
  GA_ID?: string;
  CDN_BASE_URL?: string;

  // Development
  DEV_HOST?: string;
  DEV_PORT?: number;
  ENABLE_HMR?: boolean;
  ENABLE_SOURCE_MAPS?: boolean;
  BUILD_OUTPUT_DIR?: string;
}

/**
 * Get environment variable with type safety and default values
 */
function getEnvVar(key: string, defaultValue?: string, suppressWarning: boolean = false): string {
  const value = import.meta.env[key];
  if (value === undefined && defaultValue === undefined && !suppressWarning) {
    console.warn(`Environment variable ${key} is not defined`);
    return '';
  }
  return value || defaultValue || '';
}

/**
 * Get boolean environment variable
 */
function getBooleanEnvVar(key: string, defaultValue: boolean = false, suppressWarning: boolean = false): boolean {
  const value = getEnvVar(key, undefined, suppressWarning);
  if (!value) return defaultValue;
  return value.toLowerCase() === 'true';
}

/**
 * Get number environment variable
 */
function getNumberEnvVar(key: string, defaultValue: number, suppressWarning: boolean = false): number {
  const value = getEnvVar(key, undefined, suppressWarning);
  if (!value) return defaultValue;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * Get current environment
 */
export function getCurrentEnvironment(): Environment {
  const env = getEnvVar('VITE_APP_ENV', 'development') as Environment;
  if (!['development', 'staging', 'production'].includes(env)) {
    console.warn(`Invalid environment: ${env}, defaulting to development`);
    return 'development';
  }
  return env;
}

/**
 * Check if running in development mode
 */
export function isDevelopment(): boolean {
  return getCurrentEnvironment() === 'development' || import.meta.env.DEV;
}

/**
 * Check if running in production mode
 */
export function isProduction(): boolean {
  return getCurrentEnvironment() === 'production' || import.meta.env.PROD;
}

/**
 * Check if running in staging mode
 */
export function isStaging(): boolean {
  return getCurrentEnvironment() === 'staging';
}

/**
 * Get complete environment configuration
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  const currentEnv = getCurrentEnvironment();

  return {
    // Application
    APP_ENV: currentEnv,
    APP_NAME: getEnvVar('VITE_APP_NAME', 'CloudAudit'),
    APP_VERSION: getEnvVar('VITE_APP_VERSION', '0.1.0'),
    APP_BASE_URL: getEnvVar('VITE_APP_BASE_URL', 'http://localhost:5173'),

    // API
    BACKEND_BASE_URL: getEnvVar('VITE_BACKEND_LOCAL_BASE_URL', 'http://localhost:8000/api'),
    API_TIMEOUT: getNumberEnvVar('VITE_API_TIMEOUT', 30000),
    MAX_RETRIES: getNumberEnvVar('VITE_MAX_RETRIES', 2),

    // Feature Flags
    ENABLE_DEVTOOLS: getBooleanEnvVar('VITE_ENABLE_DEVTOOLS', isDevelopment()),
    ENABLE_DEBUG_LOGGING: getBooleanEnvVar('VITE_ENABLE_DEBUG_LOGGING', isDevelopment()),
    ENABLE_PERFORMANCE_MONITORING: getBooleanEnvVar('VITE_ENABLE_PERFORMANCE_MONITORING', isProduction()),
    ENABLE_ERROR_REPORTING: getBooleanEnvVar('VITE_ENABLE_ERROR_REPORTING', isProduction()),

    // Security
    TOKEN_REFRESH_THRESHOLD: getNumberEnvVar('VITE_TOKEN_REFRESH_THRESHOLD', 5),
    SESSION_TIMEOUT: getNumberEnvVar('VITE_SESSION_TIMEOUT', 60),

    // Third-party Services
    SENTRY_DSN: getEnvVar('VITE_SENTRY_DSN', undefined, true),
    GA_ID: getEnvVar('VITE_GA_ID', undefined, true),
    CDN_BASE_URL: getEnvVar('VITE_CDN_BASE_URL', undefined, true),

    // Development
    DEV_HOST: getEnvVar('VITE_DEV_HOST', undefined, true),
    DEV_PORT: getNumberEnvVar('VITE_DEV_PORT', 5173),
    ENABLE_HMR: getBooleanEnvVar('VITE_ENABLE_HMR', true),
    ENABLE_SOURCE_MAPS: getBooleanEnvVar('VITE_ENABLE_SOURCE_MAPS', isDevelopment()),
    BUILD_OUTPUT_DIR: getEnvVar('VITE_BUILD_OUTPUT_DIR', 'dist'),
  };
}

/**
 * Validate required environment variables
 */
export function validateEnvironment(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  const config = getEnvironmentConfig();

  // Required variables
  const requiredVars = [
    { key: 'BACKEND_BASE_URL', value: config.BACKEND_BASE_URL },
    { key: 'APP_NAME', value: config.APP_NAME },
    { key: 'APP_BASE_URL', value: config.APP_BASE_URL },
  ];

  for (const { key, value } of requiredVars) {
    if (!value || value.trim() === '') {
      errors.push(`Required environment variable ${key} is missing or empty`);
    }
  }

  // URL validation
  try {
    new URL(config.BACKEND_BASE_URL);
  } catch {
    errors.push('BACKEND_BASE_URL is not a valid URL');
  }

  try {
    new URL(config.APP_BASE_URL);
  } catch {
    errors.push('APP_BASE_URL is not a valid URL');
  }

  // Production-specific validations
  if (isProduction()) {
    if (config.ENABLE_DEBUG_LOGGING) {
      errors.push('Debug logging should be disabled in production');
    }
    if (config.ENABLE_DEVTOOLS) {
      errors.push('DevTools should be disabled in production');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Export the configuration instance
export const config = getEnvironmentConfig();

// Log configuration in development
if (isDevelopment()) {
  console.log('Environment Configuration:', config);
}
