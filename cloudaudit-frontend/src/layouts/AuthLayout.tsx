import React from "react";
import { motion } from "framer-motion";
import { Outlet } from "react-router-dom";
import { Shield } from "lucide-react";

const AuthLayout: React.FC = () => {
  return (
    <div className="flex min-h-screen">
      {/* Left side - Brand/Info */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-dark-900 to-dark-950 flex-col justify-center items-center p-8 text-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute w-96 h-96 bg-primary-500 rounded-full -left-20 -top-20 opacity-20 blur-3xl" />
          <div className="absolute w-96 h-96 bg-secondary-500 rounded-full -right-20 -bottom-20 opacity-20 blur-3xl" />
          <div className="absolute w-[30rem] h-[30rem] bg-accent-500 rounded-full top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 opacity-10 blur-3xl" />
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="max-w-lg text-center z-10 relative"
        >
          <div className="flex justify-center mb-6">
            <div className="bg-gradient-to-br from-primary-500 to-secondary-500 p-4 rounded-2xl shadow-xl">
              <Shield size={48} className="text-white" />
            </div>
          </div>

          <h1 className="text-3xl font-bold mb-6">CloudAudit</h1>
          <p className="text-lg text-dark-200 mb-8">
            Your comprehensive security solution for cloud infrastructure. Scan,
            detect, and remediate vulnerabilities across AWS, GCP, and Azure.
          </p>

          <div className="grid grid-cols-2 gap-6 mb-8">
            <div className="bg-dark-800/40 p-4 rounded-lg border border-dark-700/50">
              <h3 className="font-semibold mb-2">Comprehensive Scans</h3>
              <p className="text-sm text-dark-300">
                Deep security analysis across all major cloud services
              </p>
            </div>
            <div className="bg-dark-800/40 p-4 rounded-lg border border-dark-700/50">
              <h3 className="font-semibold mb-2">Vulnerability Detection</h3>
              <p className="text-sm text-dark-300">
                Identify critical security issues before they're exploited
              </p>
            </div>
            <div className="bg-dark-800/40 p-4 rounded-lg border border-dark-700/50">
              <h3 className="font-semibold mb-2">Compliance Checks</h3>
              <p className="text-sm text-dark-300">
                Ensure your cloud infrastructure meets industry standards
              </p>
            </div>
            <div className="bg-dark-800/40 p-4 rounded-lg border border-dark-700/50">
              <h3 className="font-semibold mb-2">Detailed Reporting</h3>
              <p className="text-sm text-dark-300">
                Get actionable insights with remediation guidance
              </p>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Right side - Auth form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center bg-dark-950 p-6">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md"
        >
          <Outlet />
        </motion.div>
      </div>
    </div>
  );
};

export default AuthLayout;
