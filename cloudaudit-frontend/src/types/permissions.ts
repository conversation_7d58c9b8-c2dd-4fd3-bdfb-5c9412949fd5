/**
 * Permission types and constants for the RBAC system
 */

/**
 * Permission categories
 */
export enum PermissionCategory {
  ACCOUNT_MANAGEMENT = 'Account Management',
  USER_MANAGEMENT = 'User Management',
  ROLE_MANAGEMENT = 'Role Management',
  OPERATIONAL = 'Operational',
  ACCESS_LEVEL = 'Access Level',
}

/**
 * Permission names
 * These should match the permission names returned by the API
 */
export const PERMISSIONS = {
  // Account Management
  CREATE_ACCOUNT: 'create_account',
  DELETE_ACCOUNT: 'delete_account',
  
  // User Management
  CREATE_USER: 'create_user',
  DELETE_USER: 'delete_user',
  UPDATE_USER_PERMISSIONS: 'update_user_permissions',
  
  // Role Management
  ASSIGN_ROLE_TO_USER: 'assign_role_to_user',
  REVOKE_ROLE_FROM_USER: 'revoke_role_from_user',
  CREATE_CUSTOM_ROLE: 'create_custom_role',
  UPDATE_CUSTOM_ROLE: 'update_custom_role',
  DELETE_CUSTOM_ROLE: 'delete_custom_role',
  
  // Operational
  SCAN_SERVICE: 'scan_service',
  REMEDIATE_FINDINGS: 'remediate_findings',
  
  // Access Level
  READ_ONLY: 'read_only',
} as const;

/**
 * Permission mapping to categories
 */
export const PERMISSION_CATEGORIES: Record<string, PermissionCategory> = {
  [PERMISSIONS.CREATE_ACCOUNT]: PermissionCategory.ACCOUNT_MANAGEMENT,
  [PERMISSIONS.DELETE_ACCOUNT]: PermissionCategory.ACCOUNT_MANAGEMENT,
  
  [PERMISSIONS.CREATE_USER]: PermissionCategory.USER_MANAGEMENT,
  [PERMISSIONS.DELETE_USER]: PermissionCategory.USER_MANAGEMENT,
  [PERMISSIONS.UPDATE_USER_PERMISSIONS]: PermissionCategory.USER_MANAGEMENT,
  
  [PERMISSIONS.ASSIGN_ROLE_TO_USER]: PermissionCategory.ROLE_MANAGEMENT,
  [PERMISSIONS.REVOKE_ROLE_FROM_USER]: PermissionCategory.ROLE_MANAGEMENT,
  [PERMISSIONS.CREATE_CUSTOM_ROLE]: PermissionCategory.ROLE_MANAGEMENT,
  [PERMISSIONS.UPDATE_CUSTOM_ROLE]: PermissionCategory.ROLE_MANAGEMENT,
  [PERMISSIONS.DELETE_CUSTOM_ROLE]: PermissionCategory.ROLE_MANAGEMENT,
  
  [PERMISSIONS.SCAN_SERVICE]: PermissionCategory.OPERATIONAL,
  [PERMISSIONS.REMEDIATE_FINDINGS]: PermissionCategory.OPERATIONAL,
  
  [PERMISSIONS.READ_ONLY]: PermissionCategory.ACCESS_LEVEL,
};

/**
 * Permission descriptions for UI display
 */
export const PERMISSION_DESCRIPTIONS: Record<string, string> = {
  [PERMISSIONS.CREATE_ACCOUNT]: 'Create new cloud provider accounts',
  [PERMISSIONS.DELETE_ACCOUNT]: 'Delete existing cloud provider accounts',
  
  [PERMISSIONS.CREATE_USER]: 'Create new users in the workspace',
  [PERMISSIONS.DELETE_USER]: 'Delete existing users from the workspace',
  [PERMISSIONS.UPDATE_USER_PERMISSIONS]: 'Update permissions for existing users',
  
  [PERMISSIONS.ASSIGN_ROLE_TO_USER]: 'Assign roles to users',
  [PERMISSIONS.REVOKE_ROLE_FROM_USER]: 'Remove roles from users',
  [PERMISSIONS.CREATE_CUSTOM_ROLE]: 'Create custom roles with specific permissions',
  [PERMISSIONS.UPDATE_CUSTOM_ROLE]: 'Modify existing custom roles',
  [PERMISSIONS.DELETE_CUSTOM_ROLE]: 'Delete custom roles',
  
  [PERMISSIONS.SCAN_SERVICE]: 'Initiate and manage security scans',
  [PERMISSIONS.REMEDIATE_FINDINGS]: 'Remediate security findings',
  
  [PERMISSIONS.READ_ONLY]: 'Read-only access to the application',
};

/**
 * Permission type
 */
export interface Permission {
  id: number;
  name: string;
}

/**
 * Role type
 */
export interface Role {
  id: number;
  name: string;
  permissions: Permission[];
}

/**
 * User permissions type
 */
export interface UserPermissions {
  roles: Role[];
  customRoles: Role[];
  allPermissions: Permission[];
}

/**
 * Type for permission check result
 */
export type PermissionCheckResult = boolean;

/**
 * Type for permission names
 */
export type PermissionName = typeof PERMISSIONS[keyof typeof PERMISSIONS];
