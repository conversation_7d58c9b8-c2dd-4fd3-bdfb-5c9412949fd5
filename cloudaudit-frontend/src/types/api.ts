import { InternalAxiosRequestConfig } from 'axios';
import { CloudProvider as ProviderType } from '@/stores/cloudProviderStore';

export interface AuthResponse {
    ok: boolean;
    status_code: number;
    token_type: string;
    access_token: string;
    refresh_token: string;
    message: string;
    email?: string;
    workspace_name?: string;
}

export interface SignupRequest {
    email: string;
    password: string;
    workspace_name: string;
    first_name: string;
    last_name: string;
}

export interface SignupResponse {
    ok: boolean;
    status_code: number;
    message: string;
}

export interface LoginRequest {
    email: string;
    password: string;
}

export interface LoginResponse {
    ok: boolean;
    status_code: number;
    token_type: string;
    access_token: string;
    refresh_token: string;
    message: string;
}

export interface RefreshTokenRequest {
    refresh_token: string;
}

export interface RefreshTokenResponse {
    ok: boolean;
    status_code: number;
    token_type: string;
    access_token: string;
    refresh_token: string;
    message: string;
}

export interface CloudProvider {
    id: number;
    name: string;
    is_enable: boolean;
}

export interface CloudProvidersResponse {
    ok: boolean;
    status_code: number;
    data: {
        cloud_providers: CloudProvider[];
    };
}

export interface Service {
    id: number;
    name: string;
}

export interface ServicesResponse {
    ok: boolean;
    status_code: number;
    data: Service[];
}

export interface ApiError {
    ok: false;
    error: string;
    message: string;
    status: number;
}

export interface CustomAxiosRequestConfig extends InternalAxiosRequestConfig {
    _retry?: number;
    _isRetryAfterRefresh?: boolean;
}

export interface Permission {
    id: number;
    name: string;
}

export interface Role {
    id: number;
    name: string;
    permissions: Permission[];
}

export interface UserInfo {
    user_id: number;
    email: string;
    first_name?: string;
    last_name?: string;
    workspace_id: number;
    workspace_name: string;
    roles: Role[];
    custom_roles: Role[];
}

export interface UserInfoResponse {
    ok: boolean;
    status_code: number;
    data: UserInfo;
}

export interface CloudProviderCredentials {
    access_key: string;
    secret_key: string;
    aws_account_id?: string;
}

export interface Account {
    id: number;
    account_name: string;
    account_id: string;
    cloud_provider_id: number;
    cloud_provider_name: string;
    created_at: string;
    last_scan_date?: string;
    failed_findings?: number;
    total_scans?: number;
    cloud_provider_data?: CloudProviderCredentials;
}

export interface AccountsResponse {
    ok: boolean;
    status_code: number;
    data: {
        accounts: Account[];
    };
}

export interface RecentScan {
    id: number;
    scan_start: string;
    scan_end: string | null;
    status: "completed" | "running" | "pending" | "failed";
    findings_count: number;
    failed_findings: number;
}

export interface AccountDetail {
    id: number;
    account_name: string;
    cloud_provider_id: number;
    cloud_provider_name: string;
    credential_data: {
        access_key: string;
        secret_key: string;
        aws_account_id?: string;
    };
    created_at: string;
    recent_scans?: RecentScan;
}

export interface AccountDetailResponse {
    ok: boolean;
    status_code: number;
    data: AccountDetail;
}

export interface AddAccountRequest {
    cloud_provider_id: number;
    account_name: string;
    is_organization: boolean;
    credentials: {
        access_key: string;
        secret_key: string;
    };
}

export interface AddAccountResponse {
    ok: boolean;
    status_code: number;
    message: string;
    data?: {
        account: Account;
    };
}

export interface CloudServiceResponse {
    ok: boolean;
    status_code: number;
    data: CloudService[];
}

export interface CloudService {
    id: number;
    name: string;
}

export interface Scan {
    id: string;
    date: string;
    compliantCount: number;
    nonCompliantCount: number;
    status: "completed" | "running" | "pending" | "failed";
}

export interface ScanAccount extends Account {
    provider: ProviderType;
    scans: Scan[];
}

export interface EnrichedScan extends Scan {
    accountId: string;
    provider: ProviderType;
    accountNumber: string;
}

export interface ApiScan {
    id: number;
    scan_start: string;
    scan_end: string | null;
    status: "completed" | "running" | "pending" | "failed";
    account_name: string;
    account_id: string;
    cloud_provider: ProviderType;
    findings_count: number,
    failed_findings: number;
    passed_findings: number;
    total_services: number;
    completed_services: number;
}

export interface Region {
    id: string;
    name: string;
    description: string;
}

export interface GetRegionsResponse {
    ok: boolean;
    status_code: number;
    data: {
        regions: Region[];
    };
}

export interface CreateScanRequest {
    user_id: number;
    account_id: number;
    cloud_provider_id: number;
    cloud_provider_name: string;
    regions: string[];
    services: number[];
}

export interface CreateScanResponse {
    ok: boolean;
    status_code: number;
    message: string;
}

export interface GetScansResponse {
    ok: boolean;
    status_code: number;
    data: ApiScan[];
    pagination: {
        total: number;
        page: number;
        page_size: number;
        total_pages: number;
    };
}

export interface CreateUserRequest {
    email: string;
    password: string;
    role_id: number;
    is_custom_role: boolean;
    first_name: string;
    last_name: string;
}

export interface CreateUserResponse {
    ok: boolean;
    status_code: number;
    message: string;
}

export interface CreateCustomRoleRequest {
    name: string;
    permissions: Permission[];
}

export interface CreateCustomRoleResponse {
    ok: boolean;
    status_code: number;
    message: string;
}

export interface AssignRoleRequest {
    user_id: number;
    role_id: number;
    is_custom_role: boolean;
}

export interface AssignRoleResponse {
    ok: boolean;
    status_code: number;
    message: string;
}

export interface RevokeRoleRequest {
    user_id: number;
    role_id: number;
    is_custom_role: boolean;
}

export interface RevokeRoleResponse {
    ok: boolean;
    status_code: number;
    message: string;
}

export interface UpdateCustomRoleRequest {
    id: number;
    name: string;
    permissions: Permission[];
}

export interface UpdateCustomRoleResponse {
    ok: boolean;
    status_code: number;
    message: string;
}

export interface DeleteCustomRoleResponse {
    ok: boolean;
    status_code: number;
    message: string;
}

export interface RolesResponse {
    ok: boolean;
    status_code: number;
    data: {
        custom_roles: Role[];
        roles: Role[];
    };
}

export interface UserPermissionsResponse {
    ok: boolean;
    status_code: number;
    data: Permission[];
}

export interface DeleteUserResponse {
    ok: boolean;
    status_code: number;
    message: string;
}

export interface GetUsersResponse {
    ok: boolean;
    status_code: number;
    data: {
        users: Array<{
            id: number;
            email: string;
            role: Role;
            status: "Active" | "Pending" | "Suspended";
            last_login: string | null;
            created_at: string;
        }>;
    };
}

export interface Finding {
    description: string;
    service: string;
    region: string;
    severity: string;
    status: string;
    lastSeen: string;
}

export interface ApiFinding {
    id: number;
    scan_id: number;
    service_id: number;
    service_name: string;
    policy_check: string;
    severity: string;
    description: string;
    status: string;
    created_at: string;
}

export interface RemediationInfo {
    status: string;
    message: string;
    attempted_at: string;
    attempted_by: number | {
        id: number;
        email: string;
        is_admin: boolean;
    };
}

export interface FindingDetailItem extends Record<string, unknown> {
    compliance?: boolean;
    remediate?: RemediationInfo;
}

export interface FindingDetail extends ApiFinding {
    account_id: number;
    account_name: string;
    aws_account_id: string;
    cloud_provider_id: number;
    cloud_provider_name: string;
    details: FindingDetailItem[];
    field_labels: Record<string, string>;
}

export interface GetFindingDetailResponse {
    ok: boolean;
    status_code: number;
    data: FindingDetail;
}

export interface RemediateFindingResponse {
    ok: boolean;
    status_code: number;
    message: string;
}

export interface GetFindingsResponse {
    ok: boolean;
    status_code: number;
    pagination: {
        total: number;
        page: number;
        page_size: number;
        total_pages: number;
    };
    data: ApiFinding[];
}

export interface ScanServiceDetails {
    id: number;
    service_id: number;
    service_name: string;
    status: "completed" | "running" | "pending" | "failed";
    findings_count: number;
    failed_findings: number;
    passed_findings: number;
    last_scanned_at: string | null;
}

export interface ScanDetailsResponse {
    ok: boolean;
    status_code: number;
    data: {
        id: number;
        account_id: number;
        account_name: string;
        cloud_provider: ProviderType;
        scan_start: string;
        scan_end: string | null;
        status: "completed" | "running" | "pending" | "failed";
        total_services: number;
        completed_services: number;
        findings_count: number;
        failed_findings: number;
        passed_findings: number;
        services: ScanServiceDetails[];
    };
}

export interface TeamMember {
    id: number;
    email: string;
    first_name?: string;
    last_name?: string;
    is_owner: boolean;
    role_name: string;
    is_custom_role: boolean;
    created_at: string;
}

export interface TeamMembersResponse {
    ok: boolean;
    status_code: number;
    data: TeamMember[];
}

export interface UpdateTeamMemberRequest {
    user_id: number;
    role_id: number;
    is_custom_role: boolean;
    first_name: string;
    last_name: string;
}

export interface UpdateTeamMemberResponse {
    ok: boolean;
    status_code: number;
    message: string;
}

export interface ChangePasswordRequest {
    user_id: number;
    password: string;
    confirm_password: string;
}

export interface ChangePasswordResponse {
    ok: boolean;
    status_code: number;
    message: string;
}

export interface UpdateUserInfoRequest {
    email: string;
    first_name: string;
    last_name: string;
}

export interface UpdateUserInfoResponse {
    ok: boolean;
    status_code: number;
    message: string;
}

export interface UpdatePasswordRequest {
    current_password: string;
    new_password: string;
    confirm_password: string;
}

export interface UpdatePasswordResponse {
    ok: boolean;
    status_code: number;
    message: string;
}
