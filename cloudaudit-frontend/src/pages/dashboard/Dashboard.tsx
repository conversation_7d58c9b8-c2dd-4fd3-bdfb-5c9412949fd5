import Button from "@/components/ui/Button";
import { ServiceErrorType } from "@/services/baseService";
import {
  CloudProviderName,
  useProviderStore,
} from "@/stores/cloudProviderStore";
import AddProviderCard from "@components/AddProviderCard";
import ContentLoader from "@components/ContentLoader";
import NewProviderModal from "@components/NewProviderModal";
import ProviderCard from "@components/ProviderCard";
import ScanModal from "@components/ScanModal";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@components/ui/Card";
import Modal from "@components/ui/Modal";
import { useAccounts, useDeleteAccount } from "@hooks/useAccounts";
import { useCloudProviders } from "@hooks/useCloudProviders";
import { AlertCircle, RefreshCw } from "lucide-react";
import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";

const Dashboard: React.FC = () => {
  const navigate = useNavigate();

  const [scanModalOpen, setScanModalOpen] = useState(false);
  const [newProviderModalOpen, setNewProviderModalOpen] = useState(false);
  const [selectedProvider, setSelectedProvider] =
    useState<CloudProviderName | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [accountToDelete, setAccountToDelete] = useState<{
    id: number;
    name: string;
    provider: CloudProviderName;
  } | null>(null);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Use the new React Query hooks
  const {
    data: allAccounts = [],
    error: accountsError,
    isLoading: isLoadingAccounts,
    refetch: refetchAccounts,
  } = useAccounts();

  const { mutateAsync: deleteAccountMutation } = useDeleteAccount();
  const {
    cloudProviders,
    isLoading: isLoadingProviders,
    refetch: refetchProviders,
  } = useCloudProviders();
  const setProviderMaps = useProviderStore((state) => state.setProviderMaps);

  // Update provider maps in the store when cloud providers are fetched
  useEffect(() => {
    if (cloudProviders && cloudProviders.length > 0) {
      setProviderMaps(cloudProviders);
    }
  }, [cloudProviders, setProviderMaps]);

  // Function to refresh dashboard data
  const refreshDashboard = async () => {
    setIsRefreshing(true);
    try {
      // Refresh both accounts and cloud providers
      await Promise.all([refetchAccounts(), refetchProviders()]);
    } catch (error) {
      console.error("Error refreshing dashboard data:", error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleOpenScanModal = () => {
    // Just open the modal - account selection will be handled in the modal
    setScanModalOpen(true);
  };

  const handleOpenAddProviderModal = (provider: CloudProviderName) => {
    setSelectedProvider(provider);
    setNewProviderModalOpen(true);
  };

  const handleCloseScanModal = () => {
    setScanModalOpen(false);
    setSelectedProvider(null);
  };

  const handleCloseNewProviderModal = () => {
    setNewProviderModalOpen(false);
    setSelectedProvider(null);
  };

  const handleDeleteAccount = (account: {
    id: number;
    name: string;
    provider: CloudProviderName;
  }) => {
    setAccountToDelete(account);
    setShowDeleteConfirm(true);
    setDeleteError(null);
  };

  const handleDeleteConfirm = async () => {
    if (!accountToDelete) return;

    try {
      // Use the mutation from useDeleteAccount hook
      await deleteAccountMutation(accountToDelete.id);
      setShowDeleteConfirm(false);
      setAccountToDelete(null);
    } catch (error) {
      setDeleteError(
        error instanceof Error ? error.message : "Failed to delete account"
      );
    }
  };

  // Determine error message based on error type
  const getErrorMessage = () => {
    if (!accountsError) return null;

    switch (accountsError.type) {
      case ServiceErrorType.NETWORK_ERROR:
        return "Unable to connect to the server. Please check your internet connection.";
      case ServiceErrorType.UNAUTHORIZED:
        return "You are not authorized to view this data. Please sign in again.";
      case ServiceErrorType.SERVER_ERROR:
        return "We're experiencing technical difficulties. Please try again later.";
      default:
        return (
          accountsError.message || "Failed to fetch cloud provider accounts"
        );
    }
  };

  return (
    <div className="space-y-8">
      {/* Dashboard header with refresh button */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-dark-100">Dashboard</h1>
        <Button
          variant="outline"
          size="sm"
          onClick={refreshDashboard}
          disabled={isRefreshing || isLoadingAccounts}
          className="flex items-center gap-2"
        >
          <RefreshCw size={16} className={isRefreshing ? "animate-spin" : ""} />
          <span>Refresh</span>
        </Button>
      </div>

      {/* Display any errors */}
      {(accountsError || deleteError) && (
        <div className="p-3 bg-error-500/10 border border-error-500/30 rounded-md text-error-400 text-sm flex items-start">
          <AlertCircle size={16} className="mr-2 mt-0.5 flex-shrink-0" />
          <span>{deleteError || getErrorMessage()}</span>
        </div>
      )}

      {/* Connected Providers */}
      <Card>
        <CardHeader className="pb-6">
          <CardTitle>Connected Cloud Providers</CardTitle>
          <CardDescription>
            Manage your connected cloud accounts and run security scans
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingAccounts ? (
            <ContentLoader type="card" message="Loading accounts..." />
          ) : allAccounts.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 pt-6">
              {allAccounts.map((account) => (
                <ProviderCard
                  key={account.id}
                  provider={account.cloud_provider_name as CloudProviderName}
                  accountId={account.account_name}
                  lastScanDate={account.last_scan_date}
                  totalScans={account.total_scans || 0}
                  issuesFound={account.failed_findings || 0}
                  onScan={handleOpenScanModal}
                  onDelete={() =>
                    handleDeleteAccount({
                      id: account.id,
                      name: account.account_name,
                      provider:
                        account.cloud_provider_name as CloudProviderName,
                    })
                  }
                  onClick={() => navigate(`/dashboard/accounts/${account.id}`)}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-dark-400">
              <p>No cloud providers connected yet.</p>
              <p className="text-sm mt-2">
                Connect your first provider below to get started.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add New Provider */}
      <Card>
        <CardHeader className="pb-6">
          <CardTitle>
            {allAccounts.length === 0
              ? "Connect Your Cloud Providers"
              : "Add More Providers"}
          </CardTitle>
          <CardDescription>
            Connect your cloud accounts to start scanning for vulnerabilities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 pt-6">
            {isLoadingProviders ? (
              <ContentLoader type="card" message="Loading cloud providers..." />
            ) : (
              cloudProviders.map((provider) => (
                <AddProviderCard
                  key={provider.name}
                  provider={provider.name}
                  isEnabled={provider.is_enable}
                  onClick={() => handleOpenAddProviderModal(provider.name)}
                />
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Scan Modal */}
      <ScanModal
        isOpen={scanModalOpen}
        onClose={handleCloseScanModal}
        accounts={allAccounts}
      />

      {/* New Provider Modal */}
      <NewProviderModal
        isOpen={newProviderModalOpen}
        onClose={handleCloseNewProviderModal}
        provider={selectedProvider}
      />

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        title="Delete Account"
        size="sm"
      >
        <div className="p-6">
          {deleteError && (
            <div className="p-3 mb-4 bg-error-500/10 border border-error-500/30 rounded-md text-error-400 text-sm flex items-start">
              <AlertCircle size={16} className="mr-2 mt-0.5 flex-shrink-0" />
              <span>{deleteError}</span>
            </div>
          )}

          <div className="mb-6">
            <h3 className="text-xl font-semibold text-dark-100 mb-3">
              Are you sure?
            </h3>
            <p className="text-dark-400">
              This will permanently delete the {accountToDelete?.provider}{" "}
              account "{accountToDelete?.name}" and remove all scan history.
              This action cannot be undone.
            </p>
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowDeleteConfirm(false)}
            >
              Cancel
            </Button>
            <Button variant="error" onClick={handleDeleteConfirm}>
              Delete Account
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default Dashboard;
