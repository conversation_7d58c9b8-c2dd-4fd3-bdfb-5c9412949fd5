import { useInfiniteScans } from "@/hooks/useScans"; // Import the useInfiniteScans hook from the hooks folder
import { formatDate } from "@/lib/dateUtils";
import { GetScansResponse } from "@/types/api";
import Badge from "@components/ui/Badge";
import Button from "@components/ui/Button";
import ContentLoader from "@components/ContentLoader";
import ScanModal from "@components/ScanModal";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@components/ui/Card";
import { useDashboard } from "@hooks/useDashboard";
import {
  AlertCircle,
  Calendar,
  Clock,
  Cloud,
  CloudIcon,
  Database,
  RefreshCw,
  Shield,
} from "lucide-react";
import React, { useState, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";

const Scans: React.FC = () => {
  const navigate = useNavigate();
  const pageSize = 20;
  const [scanModalOpen, setScanModalOpen] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch accounts data to check if any accounts exist
  const { allAccounts } = useDashboard({
    enablePrefetch: true,
  });

  // Fetch scans from the API using infinite query with automatic polling for in-progress scans
  const {
    data: infiniteScansData,
    isLoading,
    isError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
    lastUpdated,
  } = useInfiniteScans(pageSize, true, 15000);

  // Prepare the scan data from the infinite query pages
  const allScans =
    infiniteScansData?.pages.flatMap((page: GetScansResponse) => page.data) ||
    [];
  const paginationInfo =
    infiniteScansData?.pages[infiniteScansData.pages.length - 1]?.pagination;

  // Format the last updated timestamp
  const formattedLastUpdated = lastUpdated
    ? formatDate(new Date(lastUpdated), { timeOnly: true })
    : "Never";

  // Handle opening the scan modal
  const handleOpenScanModal = () => {
    if (allAccounts.length === 0) {
      // No accounts available, show error message
      toast.error("You need to create an account before running a scan");
      return;
    }

    // Open the scan modal
    setScanModalOpen(true);
  };

  // Handle closing the scan modal
  const handleCloseScanModal = () => {
    setScanModalOpen(false);
  };

  // Handle loading more data
  const handleLoadMore = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  // Handle manual refresh
  const handleRefresh = useCallback(async () => {
    if (isRefreshing) return;

    try {
      setIsRefreshing(true);
      await refetch();
      toast.success("Scan data refreshed successfully");
    } catch {
      toast.error("Failed to refresh scan data");
    } finally {
      setIsRefreshing(false);
    }
  }, [refetch, isRefreshing]);

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-dark-100 mb-2">
            Security Scans
          </h1>
          <p className="text-dark-400">
            View and manage all your cloud security scans
          </p>
          <div className="text-xs text-dark-500 mt-1 flex items-center">
            <Clock size={12} className="mr-1" />
            Last updated: {formattedLastUpdated}
          </div>
        </div>

        <div className="flex gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing || isLoading}
            leftIcon={
              <RefreshCw
                size={16}
                className={isRefreshing ? "animate-spin" : ""}
              />
            }
          >
            {isRefreshing ? "Refreshing..." : "Refresh"}
          </Button>
          <Button
            variant="primary"
            leftIcon={<Shield size={16} />}
            onClick={handleOpenScanModal}
          >
            New Scan
          </Button>
        </div>
      </div>

      {isLoading ? (
        <ContentLoader type="table" message="Loading scans..." />
      ) : isError ? (
        <Card>
          <CardContent className="py-16 flex flex-col items-center justify-center text-center">
            <div className="p-4 rounded-full bg-error-500/10 mb-4">
              <Shield size={32} className="text-error-400" />
            </div>
            <h3 className="text-lg font-medium text-dark-300 mb-2">
              Error Loading Scans
            </h3>
            <p className="text-dark-500 text-sm max-w-md mb-6">
              There was an error loading your scan data. Please try again later.
            </p>
            <Button variant="primary" onClick={handleRefresh}>
              Retry
            </Button>
          </CardContent>
        </Card>
      ) : allScans.length > 0 ? (
        <Card>
          <CardHeader>
            <CardTitle>All Scans</CardTitle>
            <CardDescription>
              Complete history of security scans across all your cloud accounts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-dark-800">
                    <th className="text-left py-3 px-4 text-dark-400 font-medium text-sm">
                      Provider
                    </th>
                    <th className="text-left py-3 px-4 text-dark-400 font-medium text-sm">
                      Account
                    </th>
                    <th className="text-left py-3 px-4 text-dark-400 font-medium text-sm">
                      Scan Date
                    </th>
                    <th className="text-left py-3 px-4 text-dark-400 font-medium text-sm">
                      Services
                    </th>
                    <th className="text-left py-3 px-4 text-dark-400 font-medium text-sm">
                      Checks
                    </th>
                    <th className="text-left py-3 px-4 text-dark-400 font-medium text-sm">
                      Passed
                    </th>
                    <th className="text-center py-3 px-4 text-dark-400 font-medium text-sm">
                      Status
                    </th>
                    <th className="text-left py-3 px-10 text-dark-400 font-medium text-sm">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {allScans.map((scan) => (
                    <tr
                      key={scan.id}
                      className="border-b border-dark-800/50 hover:bg-dark-900/30 transition-colors"
                    >
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-2">
                          {scan.cloud_provider === "AWS" && (
                            <CloudIcon size={16} className="text-accent-500" />
                          )}
                          {scan.cloud_provider === "GCP" && (
                            <Database size={16} className="text-primary-500" />
                          )}
                          {scan.cloud_provider === "Azure" && (
                            <Cloud size={16} className="text-secondary-500" />
                          )}
                          <span className="text-dark-200">
                            {scan.cloud_provider}
                          </span>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div>
                          <div className="text-dark-200 font-medium">
                            {scan.account_name}
                          </div>
                          <div className="text-dark-400 font-mono text-xs">
                            {scan.account_id}
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div>
                          <div className="flex items-center gap-2">
                            <Calendar size={14} className="text-dark-500" />
                            <span className="text-dark-300">
                              {formatDate(scan?.scan_start)}
                            </span>
                          </div>
                          {scan.scan_end && (
                            <div className="flex items-center gap-2 mt-1">
                              <Clock size={14} className="text-dark-500" />
                              <span className="text-dark-400 text-xs">
                                {formatDate(scan?.scan_end, { timeOnly: true })}
                              </span>
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-1">
                          <span className="text-dark-300">
                            {scan.completed_services}
                          </span>
                          <span className="text-dark-500">/</span>
                          <span className="text-dark-400">
                            {scan.total_services}
                          </span>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-1">
                          <span className="text-dark-300">
                            {scan.findings_count}
                          </span>
                          {scan.failed_findings > 0 && (
                            <Badge variant="error" size="sm">
                              {scan.failed_findings} issues
                            </Badge>
                          )}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-1">
                          <span className="text-dark-300">
                            {scan.passed_findings}
                          </span>
                          {scan.passed_findings > 0 && (
                            <Badge variant="success" size="sm">
                              passed
                            </Badge>
                          )}
                        </div>
                      </td>
                      <td className="py-3 px-4 text-center">
                        <Badge
                          variant={
                            scan.status === "completed"
                              ? "success"
                              : scan.status === "running"
                              ? "warning"
                              : "error"
                          }
                          size="sm"
                        >
                          {scan.status === "completed"
                            ? "Completed"
                            : scan.status === "running"
                            ? "Running"
                            : scan.status === "pending"
                            ? "Pending"
                            : "Failed"}
                        </Badge>
                      </td>
                      <td className="py-3 px-4 text-right">
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              navigate(`/dashboard/scans/${scan.id}`)
                            }
                          >
                            View Details
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Load More Button */}
            {hasNextPage && (
              <div className="flex justify-center mt-6">
                <Button
                  variant="outline"
                  onClick={handleLoadMore}
                  disabled={isFetchingNextPage}
                  className="min-w-[200px]"
                >
                  {isFetchingNextPage ? (
                    <div className="flex items-center">
                      <div className="w-4 h-4 border-2 border-dark-400 border-t-primary-500 rounded-full animate-spin mr-2"></div>
                      Loading...
                    </div>
                  ) : (
                    "Load More"
                  )}
                </Button>
              </div>
            )}

            {/* Pagination Info */}
            {paginationInfo && (
              <div className="text-sm text-dark-400 text-center mt-4">
                Showing {allScans.length} of {paginationInfo.total} scans
              </div>
            )}
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="py-16 flex flex-col items-center justify-center text-center">
            <div className="p-4 rounded-full bg-dark-800 mb-4">
              <Shield size={32} className="text-dark-400" />
            </div>
            <h3 className="text-lg font-medium text-dark-300 mb-2">
              No Scans Found
            </h3>
            <p className="text-dark-500 text-sm max-w-md mb-6">
              You haven't run any security scans on your cloud accounts yet.
              Start by running your first scan to discover potential
              vulnerabilities.
            </p>
            {allAccounts.length > 0 ? (
              <Button variant="primary" onClick={handleOpenScanModal}>
                Run First Scan
              </Button>
            ) : (
              <div className="flex flex-col items-center gap-4">
                <div className="flex items-center gap-2 text-error-500 bg-error-500/10 p-3 rounded-md">
                  <AlertCircle size={18} />
                  <span className="text-sm">
                    You need to create an account first
                  </span>
                </div>
                <Button
                  variant="primary"
                  onClick={() => navigate("/dashboard")}
                >
                  Create Account
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Scan Modal */}
      <ScanModal
        isOpen={scanModalOpen}
        onClose={handleCloseScanModal}
        accounts={allAccounts}
      />
    </div>
  );
};

export default Scans;
