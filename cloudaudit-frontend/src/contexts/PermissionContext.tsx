import React, { createContext, useContext, ReactNode } from "react";
import usePermissions from "@/hooks/usePermissions";
import { Permission, PermissionName, Role } from "@/types/permissions";
import { UserInfo } from "@/types/api";
import LoadingScreen from "@/components/LoadingScreen";

/**
 * Permission context interface
 */
interface PermissionContextType {
  roles: Role[];
  customRoles: Role[];
  allPermissions: Permission[];
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<unknown>;
  hasPermission: (permissionName: PermissionName) => boolean;
  hasAllPermissions: (permissionNames: PermissionName[]) => boolean;
  hasAnyPermission: (permissionNames: PermissionName[]) => boolean;
  hasRole: (roleId: number) => boolean;
  getAllRoles: () => Role[];
  userData: UserInfo | undefined;
}

// Create the context with a default value
const PermissionContext = createContext<PermissionContextType | undefined>(
  undefined
);

/**
 * Props for the PermissionProvider component
 */
interface PermissionProviderProps {
  children: ReactNode;
  fallback?: ReactNode;
  loadingComponent?: ReactNode;
}

/**
 * Provider component for the permission context
 * This component fetches and provides permission data to its children
 */
export const PermissionProvider: React.FC<PermissionProviderProps> = ({
  children,
  fallback,
  loadingComponent,
}) => {
  const permissions = usePermissions();

  // Show loading component while fetching permissions
  if (permissions.isLoading) {
    return loadingComponent ? (
      <>{loadingComponent}</>
    ) : (
      <LoadingScreen message="Loading permissions..." />
    );
  }

  // Show fallback if there's an error or no permissions
  if (permissions.error || !permissions.allPermissions) {
    return fallback ? <>{fallback}</> : null;
  }

  return (
    <PermissionContext.Provider value={permissions}>
      {children}
    </PermissionContext.Provider>
  );
};

/**
 * Hook to use the permission context
 * @returns The permission context
 * @throws Error if used outside of a PermissionProvider
 */
export const usePermissionContext = (): PermissionContextType => {
  const context = useContext(PermissionContext);

  if (context === undefined) {
    throw new Error(
      "usePermissionContext must be used within a PermissionProvider"
    );
  }

  return context;
};

export default PermissionContext;
