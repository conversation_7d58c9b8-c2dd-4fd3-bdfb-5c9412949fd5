#!/bin/bash

# CloudAudit Frontend Monitoring Script
# This script monitors the health and performance of the CloudAudit Frontend

set -e

# Configuration
APP_NAME="CloudAudit Frontend"
HEALTH_URL="http://localhost/health"
LOG_FILE="/var/log/cloudaudit-frontend-monitor.log"
ALERT_EMAIL="<EMAIL>"
SLACK_WEBHOOK_URL=""  # Set this if you want Slack notifications

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
    log_message "INFO" "$1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    log_message "SUCCESS" "$1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    log_message "WARNING" "$1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    log_message "ERROR" "$1"
}

# Function to send alerts
send_alert() {
    local subject=$1
    local message=$2
    
    # Email alert (if mail is configured)
    if command -v mail &> /dev/null; then
        echo "$message" | mail -s "$subject" "$ALERT_EMAIL"
    fi
    
    # Slack alert (if webhook URL is configured)
    if [[ -n "$SLACK_WEBHOOK_URL" ]]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"$subject: $message\"}" \
            "$SLACK_WEBHOOK_URL" &> /dev/null
    fi
}

# Function to check application health
check_health() {
    print_status "Checking application health..."
    
    local response_code
    local response_time
    
    # Check HTTP response
    response_code=$(curl -s -o /dev/null -w "%{http_code}" "$HEALTH_URL" --max-time 10)
    response_time=$(curl -s -o /dev/null -w "%{time_total}" "$HEALTH_URL" --max-time 10)
    
    if [[ "$response_code" == "200" ]]; then
        print_success "Health check passed (HTTP $response_code, ${response_time}s)"
        return 0
    else
        print_error "Health check failed (HTTP $response_code)"
        send_alert "$APP_NAME Health Check Failed" "HTTP response code: $response_code"
        return 1
    fi
}

# Function to check Docker containers
check_containers() {
    print_status "Checking Docker containers..."
    
    local container_status
    container_status=$(docker-compose ps -q | xargs docker inspect --format='{{.State.Status}}' 2>/dev/null)
    
    if [[ "$container_status" == "running" ]]; then
        print_success "All containers are running"
        return 0
    else
        print_error "Some containers are not running: $container_status"
        send_alert "$APP_NAME Container Issue" "Container status: $container_status"
        return 1
    fi
}

# Function to check disk space
check_disk_space() {
    print_status "Checking disk space..."
    
    local disk_usage
    disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [[ "$disk_usage" -lt 80 ]]; then
        print_success "Disk usage is normal ($disk_usage%)"
        return 0
    elif [[ "$disk_usage" -lt 90 ]]; then
        print_warning "Disk usage is high ($disk_usage%)"
        return 0
    else
        print_error "Disk usage is critical ($disk_usage%)"
        send_alert "$APP_NAME Disk Space Critical" "Disk usage: $disk_usage%"
        return 1
    fi
}

# Function to check memory usage
check_memory() {
    print_status "Checking memory usage..."
    
    local memory_usage
    memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    if [[ "$memory_usage" -lt 80 ]]; then
        print_success "Memory usage is normal ($memory_usage%)"
        return 0
    elif [[ "$memory_usage" -lt 90 ]]; then
        print_warning "Memory usage is high ($memory_usage%)"
        return 0
    else
        print_error "Memory usage is critical ($memory_usage%)"
        send_alert "$APP_NAME Memory Usage Critical" "Memory usage: $memory_usage%"
        return 1
    fi
}

# Function to check SSL certificate expiry
check_ssl_certificate() {
    local domain=$1
    if [[ -z "$domain" ]]; then
        return 0
    fi
    
    print_status "Checking SSL certificate for $domain..."
    
    local expiry_date
    local days_until_expiry
    
    expiry_date=$(echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
    days_until_expiry=$(( ($(date -d "$expiry_date" +%s) - $(date +%s)) / 86400 ))
    
    if [[ "$days_until_expiry" -gt 30 ]]; then
        print_success "SSL certificate is valid ($days_until_expiry days remaining)"
        return 0
    elif [[ "$days_until_expiry" -gt 7 ]]; then
        print_warning "SSL certificate expires soon ($days_until_expiry days remaining)"
        return 0
    else
        print_error "SSL certificate expires very soon ($days_until_expiry days remaining)"
        send_alert "$APP_NAME SSL Certificate Expiring" "Certificate expires in $days_until_expiry days"
        return 1
    fi
}

# Function to check log errors
check_logs() {
    print_status "Checking recent logs for errors..."
    
    local error_count
    error_count=$(docker-compose logs --since="1h" 2>&1 | grep -i error | wc -l)
    
    if [[ "$error_count" -eq 0 ]]; then
        print_success "No errors found in recent logs"
        return 0
    elif [[ "$error_count" -lt 10 ]]; then
        print_warning "Found $error_count errors in recent logs"
        return 0
    else
        print_error "Found $error_count errors in recent logs"
        send_alert "$APP_NAME High Error Count" "Found $error_count errors in the last hour"
        return 1
    fi
}

# Function to generate status report
generate_report() {
    print_status "Generating status report..."
    
    local report_file="/tmp/cloudaudit-frontend-status-$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "CloudAudit Frontend Status Report"
        echo "Generated: $(date)"
        echo "=================================="
        echo
        
        echo "System Information:"
        echo "- Hostname: $(hostname)"
        echo "- Uptime: $(uptime)"
        echo "- Load Average: $(uptime | awk -F'load average:' '{print $2}')"
        echo
        
        echo "Docker Containers:"
        docker-compose ps
        echo
        
        echo "Disk Usage:"
        df -h
        echo
        
        echo "Memory Usage:"
        free -h
        echo
        
        echo "Recent Logs (last 50 lines):"
        docker-compose logs --tail=50
        
    } > "$report_file"
    
    print_success "Status report generated: $report_file"
}

# Main monitoring function
main() {
    local exit_code=0
    
    print_status "Starting $APP_NAME monitoring check..."
    
    # Run all checks
    check_health || exit_code=1
    check_containers || exit_code=1
    check_disk_space || exit_code=1
    check_memory || exit_code=1
    check_logs || exit_code=1
    
    # Check SSL certificate if domain is provided
    if [[ -n "$1" ]]; then
        check_ssl_certificate "$1" || exit_code=1
    fi
    
    # Generate report if requested
    if [[ "$2" == "--report" ]]; then
        generate_report
    fi
    
    if [[ "$exit_code" -eq 0 ]]; then
        print_success "All monitoring checks passed"
    else
        print_error "Some monitoring checks failed"
    fi
    
    return $exit_code
}

# Show help
show_help() {
    cat << EOF
CloudAudit Frontend Monitoring Script

Usage: $0 [domain] [options]

Arguments:
    domain          Domain name to check SSL certificate (optional)

Options:
    --report        Generate detailed status report
    --help          Show this help message

Examples:
    $0                              # Basic monitoring checks
    $0 app.cloudaudit.com          # Include SSL certificate check
    $0 app.cloudaudit.com --report # Include SSL check and generate report

EOF
}

# Parse command line arguments
case "$1" in
    --help)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
