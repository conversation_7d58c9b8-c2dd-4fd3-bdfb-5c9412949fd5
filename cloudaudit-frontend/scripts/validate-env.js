#!/usr/bin/env node

/**
 * Environment Validation Script
 * Validates environment configuration before build/deployment
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ANSI color codes for console output
const colors = {
  reset: "\x1b[0m",
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  magenta: "\x1b[35m",
  cyan: "\x1b[36m",
};

function log(message, color = "reset") {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function loadEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return null;
  }

  const content = fs.readFileSync(filePath, "utf8");
  const env = {};

  content.split("\n").forEach((line) => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith("#")) {
      const [key, ...valueParts] = trimmed.split("=");
      if (key && valueParts.length > 0) {
        env[key.trim()] = valueParts.join("=").trim();
      }
    }
  });

  return env;
}

function validateUrl(url, name) {
  try {
    new URL(url);
    return { valid: true };
  } catch (error) {
    return { valid: false, error: `${name} is not a valid URL: ${url}` };
  }
}

function validateEnvironment(env, envName) {
  const errors = [];
  const warnings = [];

  // Required variables
  const requiredVars = [
    "VITE_BACKEND_LOCAL_BASE_URL",
    "VITE_APP_NAME",
    "VITE_APP_BASE_URL",
    "VITE_APP_ENV",
  ];

  // Check required variables
  for (const varName of requiredVars) {
    if (!env[varName] || env[varName].trim() === "") {
      errors.push(`Missing required variable: ${varName}`);
    }
  }

  // URL validations
  if (env.VITE_BACKEND_LOCAL_BASE_URL) {
    const urlValidation = validateUrl(
      env.VITE_BACKEND_LOCAL_BASE_URL,
      "VITE_BACKEND_LOCAL_BASE_URL"
    );
    if (!urlValidation.valid) {
      errors.push(urlValidation.error);
    }
  }

  if (env.VITE_APP_BASE_URL) {
    const urlValidation = validateUrl(
      env.VITE_APP_BASE_URL,
      "VITE_APP_BASE_URL"
    );
    if (!urlValidation.valid) {
      errors.push(urlValidation.error);
    }
  }

  // Environment-specific validations
  const appEnv = env.VITE_APP_ENV;

  if (appEnv === "production") {
    // Production-specific checks
    if (env.VITE_ENABLE_DEBUG_LOGGING === "true") {
      warnings.push("Debug logging is enabled in production");
    }
    if (env.VITE_ENABLE_DEVTOOLS === "true") {
      warnings.push("DevTools are enabled in production");
    }
    if (!env.VITE_SENTRY_DSN) {
      warnings.push("Sentry DSN not configured for production error reporting");
    }
    if (
      env.VITE_BACKEND_LOCAL_BASE_URL &&
      env.VITE_BACKEND_LOCAL_BASE_URL.includes("localhost")
    ) {
      errors.push("Production environment should not use localhost URLs");
    }
  }

  if (appEnv === "development") {
    // Development-specific checks
    if (env.VITE_ENABLE_ERROR_REPORTING === "true") {
      warnings.push("Error reporting is enabled in development");
    }
  }

  // Numeric validations
  const numericVars = [
    "VITE_API_TIMEOUT",
    "VITE_MAX_RETRIES",
    "VITE_TOKEN_REFRESH_THRESHOLD",
    "VITE_SESSION_TIMEOUT",
    "VITE_DEV_PORT",
  ];

  for (const varName of numericVars) {
    if (env[varName] && isNaN(parseInt(env[varName]))) {
      errors.push(`${varName} must be a valid number: ${env[varName]}`);
    }
  }

  return { errors, warnings };
}

function main() {
  const environment = process.argv[2] || "development";

  log(`🔍 Validating environment configuration for: ${environment}`, "cyan");
  log("", "reset");

  // Determine environment file
  let envFile;
  switch (environment) {
    case "production":
      envFile = ".env.production";
      break;
    case "staging":
      envFile = ".env.staging";
      break;
    case "development":
    default:
      envFile = ".env.local";
      break;
  }

  const envPath = path.resolve(process.cwd(), envFile);

  log(`📁 Loading environment file: ${envFile}`, "blue");

  // Load environment file
  const env = loadEnvFile(envPath);

  if (!env) {
    log(`❌ Environment file not found: ${envPath}`, "red");
    log(`💡 Create the file by copying from .env.example`, "yellow");
    process.exit(1);
  }

  log(`✅ Environment file loaded successfully`, "green");
  log("", "reset");

  // Validate environment
  const validation = validateEnvironment(env, environment);

  // Display results
  if (validation.errors.length > 0) {
    log(
      `❌ Validation failed with ${validation.errors.length} error(s):`,
      "red"
    );
    validation.errors.forEach((error) => {
      log(`   • ${error}`, "red");
    });
    log("", "reset");
  }

  if (validation.warnings.length > 0) {
    log(`⚠️  ${validation.warnings.length} warning(s):`, "yellow");
    validation.warnings.forEach((warning) => {
      log(`   • ${warning}`, "yellow");
    });
    log("", "reset");
  }

  if (validation.errors.length === 0) {
    log(`✅ Environment validation passed!`, "green");
    if (validation.warnings.length === 0) {
      log(`🎉 No warnings found`, "green");
    }
  } else {
    log(`❌ Environment validation failed`, "red");
    process.exit(1);
  }

  // Display key configuration
  log("📋 Key Configuration:", "magenta");
  log(`   Environment: ${env.VITE_APP_ENV || "not set"}`, "reset");
  log(`   App Name: ${env.VITE_APP_NAME || "not set"}`, "reset");
  log(`   API URL: ${env.VITE_BACKEND_LOCAL_BASE_URL || "not set"}`, "reset");
  log(`   App URL: ${env.VITE_APP_BASE_URL || "not set"}`, "reset");
  log(`   Debug Logging: ${env.VITE_ENABLE_DEBUG_LOGGING || "false"}`, "reset");
  log(`   DevTools: ${env.VITE_ENABLE_DEVTOOLS || "false"}`, "reset");
}

// Run main function
main();

export { validateEnvironment, loadEnvFile };
